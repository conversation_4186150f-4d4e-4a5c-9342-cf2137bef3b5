<odoo>
    <record id="view_xml_backup_wizard_form" model="ir.ui.view">
        <field name="name">xml.backup.wizard.form</field>
        <field name="model">xml.backup.wizard</field>
        <field name="arch" type="xml">
            <form string="XML Backup Wizard" js_class="xml_backup_wizard_form">
                <!-- invisible field -->
                <field name="is_have_attachment_ids" invisible="1"/>

                <!-- View -->
                <group>
                    <group>
                        <field name="model_id" required="1" />
                        <field name="include_relations" />
                        <field name="naming_field" />
                    </group>
                    <group>
                        <field name="ignore_field_ids" widget="many2many_tags" />
                        <field name="skip_existing_xml_id" />
                        <field name="backup_compute_field" />
                        <field name="skip_ref_one2many" />
                        <field name="add_noupdate"/>
                    </group>
                </group>
                <field name="attachment_ids" attrs="{'invisible': [('is_have_attachment_ids', '=', False)]}">
                    <tree>
                        <field name="name" />
                        <field name="file_size" sum="Size"/>
                        <field name="mimetype"/>
                    </tree>
                </field>
                <footer>
                    <button string="Dowload" class="btn-primary" type="object" name="download_all" attrs="{'invisible': [('is_have_attachment_ids', '=', False)]}"/>
                    <button string="Export XML" class="btn-primary" type="object" name="action_export_xml" />
                    <button string="Export XML DFS" class="btn-primary" type="object" name="action_export_xml_dfs" />
                    <button string="Cancel" class="btn-secondary" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_xml_backup_wizard" model="ir.actions.act_window">
        <field name="name">XML Backup Wizard</field>
        <field name="res_model">xml.backup.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_xml_backup_wizard_form" />
        <field name="target">new</field>
    </record>

    <menuitem id="menu_xml_backup_wizard"
        name="XML Backup Wizard"
        parent="base.menu_administration"
        action="action_xml_backup_wizard" />
</odoo>