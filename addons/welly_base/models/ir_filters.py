from odoo import models, _
from odoo.exceptions import UserError


class IrFilters(models.Model):
    _inherit = 'ir.filters'

    # overide hàm unlink thêm điều kiện chỉ có quyền system_admin mới đư<PERSON> xóa
    def unlink(self):
        user = self.env.user
        if not user.has_group('base.group_system'):
            raise UserError(_("Bạn không có quyền xóa dữ liệu này."))
        return super(IrFilters, self).unlink()
