from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from odoo.addons.welly_base.fields import selection

class WellyPartnerAccount<PERSON>ove(models.Model):
    _name = 'welly.partner.account.move'
    _description = '<PERSON><PERSON> đơn hợp đồng khách hàng'

    account_move_id = fields.Many2one('account.move', string='Hoá đơn', readonly=True, store=True, default=lambda self: self._get_account_move_id())
    partner_id = fields.Many2one('res.partner', string='Khách hàng', required=True)
    partner_date = fields.Datetime(string="Ngày tạo khách hàng", related="partner_id.create_date", store=True)

    welly_contract_id = fields.Many2one(
        'welly.contract',
        string='Welly Contract',
        readonly=True
    )
    # Trạng thái hợp đồng
    contract_state = fields.Selection(string="Trạng thái hợp đồng", related="welly_contract_id.state", store=True)
    ###
    partner_name_print = fields.Char(
        string="<PERSON>ên khách hàng", compute='_compute_partner_info', store=True)
    partner_id_number = fields.Char(
        string="CCCD/Hộ chiếu", compute='_compute_partner_info', store=True)
    gender = fields.Selection(
        string=selection.Gender._string,
        selection=selection.Gender._selection, compute='_compute_partner_info', store=True
    )
    address = fields.Char(string="Địa chỉ", compute='_compute_partner_info', store=True)

    birthdate = fields.Date(
        string='Ngày sinh', compute='_compute_partner_info', store=True
    )
    nationality_id = fields.Many2one(
        'res.country', string="Quốc gia", compute='_compute_partner_info', store=True)
    phone = fields.Char(string="Điện thoại", compute='_compute_partner_info', store=True)
    email = fields.Char(string="Email", compute='_compute_partner_info', store=True)

    # Trạng thái hoá đơn
    account_move_state = fields.Selection(related='account_move_id.state', invisible=True, store=True)

    # Người đi kèm
    is_attached = fields.Boolean(string='Người đi kèm', default=False)

    # Date related
    welly_contract_create_date = fields.Datetime(string="Ngày tạo hợp đồng", related="welly_contract_id.create_date", store=True)
    welly_contract_date_start = fields.Date(string="Ngày bắt đầu hợp đồng", related="welly_contract_id.date_start", store=True)
    welly_contract_date_end = fields.Date(string="Ngày kết thúc hợp đồng", related="welly_contract_id.date_end", store=True)

    invoice_date = fields.Date(string="Ngày tạo hoá đơn", related="account_move_id.invoice_date", store=True)

    # Product relate
    product_id = fields.Many2one('product.product', related="account_move_id.main_product_id", store=True)
    
    # Gói dịch vụ
    sale_order_template_id = fields.Many2one(related="welly_contract_id.sale_order_template_id", store=True)
    
    # Source id
    source_id = fields.Many2one(string="Nguồn", related="welly_contract_id.utm_source_id", store=True)

    # contract type
    contract_type = fields.Selection(related="welly_contract_id.service_type", store=True)

    # Hình thức đăng Ký
    register_form_id = fields.Many2one(string="Hình thức đăng ký", related="account_move_id.registration_form_id", store=True)

    ###########################################################################################################
    _sql_constraints = [
        ('unique_partner_id_account_move_id', 'unique(partner_id, account_move_id)', 'partner_id and account_move_id must be unique!')
    ]

    # Mapping lại thông tin khách hàng
    @api.depends('partner_id')
    def _compute_partner_info(self):
        for rec in self:
            partner = rec.partner_id
            street = rec.partner_id.street or ''
            street2 = rec.partner_id.street2 or ''
            city = rec.partner_id.city or ''
            non_empty_values = [value for value in [
                street, street2, city] if value]
            full_address = ', '.join(
                non_empty_values) if non_empty_values else ''
            rec.address = full_address or ''
            rec.partner_name_print = partner.name or ''
            rec.partner_id_number = partner.partner_id_number or ''
            rec.gender = partner.gender or ''
            rec.birthdate = partner.birthdate or ''
            rec.nationality_id = partner.nationality_id or ''
            rec.phone = partner.phone or ''
            rec.email = partner.email or ''

    # Cập nhật lại domain cho field partner_id theo account_move_id
    @api.onchange('account_move_id')
    def _compute_domain(self):
        move = self.env['account.move'].browse(self._get_account_move_id())
        partner_ids = list(map(lambda x: x.partner_id.id, move.partner_account_move_ids))
        partner_ids.append(move.partner_id.id)
        op = 'not in' if move.state == 'draft' else 'in'
        domain = {'domain':
            {
                'partner_id': [('id', op, partner_ids)],
            }
        }
        return domain

    def _get_account_move_id(self):
        move_id = self.env.context.get('account_move_id')
        if not move_id:
            move_id = self.env.context.get('active_id') if self.env.context.get('active_model') == 'account.move' else False
        return move_id

    def get_report_data(self):
        street = self.partner_id.street
        street2 = self.partner_id.street2
        city = self.partner_id.city
        non_empty_values = [value for value in [
            street, street2, city] if value]
        full_address = ', '.join(
            non_empty_values) if non_empty_values else ''
        return {
            'partner_name_print': self.partner_id.name or '',
            'partner_id_number': self.partner_id.partner_id_number or '',
            'address': full_address,
            'birthdate': self.partner_id.birthdate.strftime('%d/%m/%Y') if self.partner_id.birthdate else '',
            'gender': dict(self.fields_get(allfields=['gender'])['gender']['selection'])[self.partner_id.gender] if self.partner_id.gender else '',
            'phone': self.partner_id.phone or '',
            'email': self.partner_id.email or '',
            'nationality_id': self.partner_id.nationality_id.name or '',
        }

    @api.model_create_multi
    def create(self, vals_list):
        records = super(WellyPartnerAccountMove, self).create(vals_list)
        # Với mỗi bản ghi mới được tạo, tăng customer_rank cho res_partner đó lên theo _increase_rank
        for record in records:
            if record.partner_id:
                record.partner_id._increase_rank('customer_rank')

        return records
