/** @odoo-module **/
import {Dialog} from "@web/core/dialog/dialog";
import {useChildRef, useService} from "@web/core/utils/hooks";

import {Component} from "@odoo/owl";

export class CheckinDialog extends Component {
    setup() {
        this.dialogService = useService("dialog");
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.env.dialogData.close = this.props.close;
        this.modalRef = useChildRef();
        this.action = useService("action");
        // đặt prop intervalId và xóa đi khi popup được tắt đi -> tránh lãng phí tài nguyên
        this.intervalId = setInterval(() => {
            this.props.time_close -= 1;
            if (this.props.time_close <= 0) {
                this.close();
            } else {
                this.render();
            }
        }, 1000);
    }

    onWillUnmount() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
    }

    get timeClose() {
        return this.props.time_close;
    }

    get calendar() {
        return this.props.schedule.calendar;
    }

    get contractCheckinID() {
        return this.props.schedule.contract_id_checkin;
    }

    get note() {
        if (this.props.type === "qr_code" && this.props.status === 'success') {
            return `Check-in Thành Công Mã QR Hợp Đồng: ${this.props.description}`
        } else if (!this.props.schedule.calendar) {
            return 'Hiện tại chưa có lịch tập với PT';
        } else if (this.props.schedule.contract_id_service_type === "pt" && this.props.status === 'invalid') {
            return 'Hiện tại chưa có lịch tập với PT';
        } else {
            return "";
        }
    }

    get contractClass() {
        return this.props.schedule.calendar ? 'contract-min' : 'contract';
    }

    close() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        this.props.close();
    }

    get getGuestsBorrowTowels() {
        return this.props.borrow_towel;
    }

    async handleBorrowTowelChange(event) {
        const checked = event.target.checked;

        try {
            // Cập nhật giá trị trong props để UI phản ánh đúng trạng thái mới
            this.props.borrow_towel = !this.props.borrow_towel
            // Gọi phương thức từ server để cập nhật trường is_borrow_towel
            await this.rpc("/web/dataset/call_kw", {
                model: "member.timesheet",
                method: "write",
                args: [[this.props.id], {is_borrow_towel: checked}],
                kwargs: {},
            });
            this.notification.add(this.env._t('Thành công'),
                { type: "success" }
            );
        } catch (error) {
            // Cập nhật giá trị trong props để UI phản ánh đúng trạng thái mới
            this.props.borrow_towel = !this.props.borrow_towel
            // Hiển thị thông báo lỗi
            this.notification.add(
                this.env._t('Tick chọn "Khách mượn khăn" không thành công, bạn không có quyền'),
                { type: "danger" }
            );
            // Khôi phục trạng thái checkbox nếu có lỗi
            event.target.checked = !checked;
        }
    }
}

CheckinDialog.template = "web.CheckinDialog";
CheckinDialog.components = {Dialog};
CheckinDialog.props = {
    id: {type: Number, optional: true},
    title: {type: String, optional: true},
    notification_url: {type: String, optional: true},
    type: {type: String, optional: true},
    description: {type: String, optional: true},
    member: {type: Object, optional: true},
    schedule: {type: Object, optional: true},
    time_in: {type: String, optional: true},
    time_out: {type: String, optional: true},
    time_duration: {type: String, optional: true},
    timestamp: {type: Number, optional: true},
    is_attached: {type: Boolean, optional: true},
    main_member: {type: String, optional: true},
    status: {type: String, optional: true},
    is_late: {type: Boolean, optional: true},
    time_close: {type: Number, optional: true},
    close: {type: Function, optional: true},
    borrow_towel: {type: Boolean, optional: true},
    onBorrowTowelChange: {type: Function, optional: true},
};
CheckinDialog.defaultProps = {
    title: "Thông báo checkin",
    close: () => {
    },
    time_close: 20,
    onBorrowTowelChange: () => {
    },
};
