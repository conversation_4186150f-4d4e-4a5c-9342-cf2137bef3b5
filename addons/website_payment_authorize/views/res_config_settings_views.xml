<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.payment.authorize</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="website_payment.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <div id="website_payment" position="after">
                <div class="col-12 col-lg-6 o_setting_box">
                    <div class="o_setting_right_pane">
                        <label for="authorize_capture_method"/>
                        <div class="text-muted">
                            Charge order directly or authorize at the order and capture the payment later on, manually.
                        </div>
                        <div class="content-group">
                            <div class="content-group">
                                <div class="row mt16 ms-4">
                                    <field name="authorize_capture_method" class="w-75" widget="radio"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>
