# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2015-09-30 09:27+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Call for Tender Reference:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Selection Type:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"A Call for Tenders is a procedure for generating competing offers from\n"
"            different bidders. In the call for tenders, you can record the\n"
"            products you need to buy and generate the creation of RfQs to\n"
"            vendors. Once the tenders have been registered, you can review "
"and\n"
"            compare them and you can validate some and cancel others."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_account_analytic_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_account_analytic_id
msgid "Analytic Account"
msgstr "Amiḍan n tusliṭ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Approved by Vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_bid_line_qty
msgid "Bid Line Qty"
msgstr "Tanecta n izirig n usiwel i wesumer"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Bid Selection"
msgstr "Afran n usiwel i wesumer"

#. module: purchase_requisition
#: model:ir.actions.report.xml,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_requisition_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_name
msgid "Call for Tenders Reference"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders in negotiation"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Call for Tenders where tenders are closed"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Cancel"
msgstr "Sefsex"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Call"
msgstr "Sefsex Asiwel i wesumer"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Cancel Choice"
msgstr "Sefsex afran"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel Purchase Order"
msgstr "Sefsex taladna n tiɣin"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Cancelled"
msgstr "Ifsax"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:302
#, python-format
msgid ""
"Cancelled by the call for tenders associated to this request for quotation."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:67
#, python-format
msgid "Cancelled by the tender associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_bid_line_qty
msgid "Change Bid line quantity"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Change Quantity"
msgstr "Snifel tanecta"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_partner
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Choose Vendor"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Choose product lines"
msgstr "Feren izirigen n ufaris"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Click to start a new Call for Tenders process."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Closed Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_company_id
msgid "Company"
msgstr "Takebbwanit"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Call"
msgstr "Sentem asiwel n usumer"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Confirm Order"
msgstr "Sentem taladna"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm Purchase Order"
msgstr "Sentem taladna n tiɣin"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: selection:purchase.requisition,state:0
msgid "Confirmed"
msgstr "Intem"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_partner
msgid "Create Request for Quotation"
msgstr "Rnu Asuter n ssuma"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Create a draft purchase order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_uid
msgid "Created by"
msgstr "Yerna-t"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_create_date
msgid "Created on"
msgstr "Yerna di"

#. module: purchase_requisition
#: code:addons/purchase_requisition/wizard/purchase_requisition_partner.py:22
#, python-format
msgid "Define product(s) you want to include in the call for tenders."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_description
msgid "Description"
msgstr "Aglam"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_display_name
msgid "Display Name"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Done"
msgstr "Immed"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "Draft"
msgstr "Arewway"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "End Month"
msgstr "Azemz n tagara"

#. module: purchase_requisition
#. openerp-web
#: code:addons/purchase_requisition/static/src/xml/purchase_requisition.xml:5
#, python-format
msgid "Generate PO"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Sdukel s"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_id
msgid "ID"
msgstr "Asulay"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line___last_update
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner___last_update
msgid "Last Modified on"
msgstr "Aleqqem aneggaru di"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_uid
msgid "Last Updated by"
msgstr "Aleqqem aneggaru sɣuṛ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_write_date
msgid "Last Updated on"
msgstr "Aleqqem aneggaru di"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_procurement_order_requisition_id
msgid "Latest Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_multiple_rfq_per_supplier
msgid "Multiple RFQ per vendor"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Multiple Requisitions"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New"
msgstr "Amaynut"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Order Date"
msgstr "Azemz n tladna"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Azemz n tladna"

#. module: purchase_requisition
#: selection:purchase.requisition,state:0
msgid "PO Created"
msgstr "Taladna n tiqin tlul-ed"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_picking_type_id
msgid "Picking Type"
msgstr "Tawsit n uheggi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_procurement_order
#: model:ir.model.fields,field_description:purchase_requisition.field_product_template_purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_procurement_id
msgid "Procurement"
msgstr "Asigeẓ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_id
msgid "Product"
msgstr "Afaris"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Product Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_template
msgid "Product Template"
msgstr "Taneɣruft n ufaris"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_uom_id
msgid "Product Unit of Measure"
msgstr "Aferdis n usɣel n ufaris"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Ifarisen"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_po_line_ids
msgid "Products by vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_ids
msgid "Products to Purchase"
msgstr "Ifarisen i tiɣin"

#. module: purchase_requisition
#: selection:product.template,purchase_requisition:0
msgid "Propose a call for tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Purchase Order"
msgstr "Taladna n tiɣin"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Izirig n tladna n tiɣin"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Purchase Order Lines"
msgstr "Izirigen n tladna n tiɣin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_purchase_ids
msgid "Purchase Orders"
msgstr "Tiludna n tiɣin"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_partner
msgid "Purchase Requisition Partner"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:435
#, python-format
msgid "Purchase Requisition created"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Purchase Requisitions (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
msgid "Purchase Tenders"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.act_res_partner_2_purchase_order
msgid "Purchase orders"
msgstr "Tiludna n tiɣin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_bid_line_qty_qty
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_bid_line_qty
msgid "Quantity"
msgstr "Tanecta"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid "Quantity Tendered"
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:201
#, python-format
msgid "RFQ created"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Bids"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reference"
msgstr "Tamsisɣelt"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Request a Quotation"
msgstr "Suter ssuma"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Requests for Quotation"
msgstr "Asuter n ssuma"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr "Aglam leqqayen n isutar n ssuma"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Rrit d arewway"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Responsible"
msgstr "Amasay"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line_schedule_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_schedule_date
msgid "Scheduled Date"
msgstr "Azemz iɣiwsen"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_ordering_date
msgid "Scheduled Ordering Date"
msgstr "Azemz n tladna iɣiwsen"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Call for Tenders"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select multiple RFQ"
msgstr ""

#. module: purchase_requisition
#: selection:purchase.requisition,exclusive:0
msgid "Select only one RFQ (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_exclusive
msgid ""
"Select only one RFQ (exclusive):  On the confirmation of a purchase order, "
"it cancels the remaining purchase order.\n"
"Select multiple RFQ:  It allows to have multiple purchase orders.On "
"confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Send RFQ by Email"
msgstr "Azen asuter n ssuma s Imayl"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Source"
msgstr "Aɣbalu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_origin
msgid "Source Document"
msgstr "Arrat aɣbalu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Addad"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order_line_quantity_tendered
msgid ""
"Technical field for not loosing the initial information about the quantity "
"proposed in the tender"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_date_end
msgid "Tender Closing Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.purchase_line_tree
msgid "Tender Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_exclusive
msgid "Tender Selection Type"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Tiwtilin timatutin"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_schedule_date
msgid ""
"The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Unassigned  Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_tree_tender
msgid "Vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_partner_partner_ids
msgid "Vendors"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_warehouse_id
msgid "Warehouse"
msgstr "Agadir"

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:72
#, python-format
msgid "You can not confirm call because there is no product line."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:249
#, python-format
msgid "You have already generate the purchase order(s)."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:198
#, python-format
msgid ""
"You have already one %s purchase order for this partner, you must cancel "
"this purchase order to create a new quotation."
msgstr ""

#. module: purchase_requisition
#: code:addons/purchase_requisition/purchase_requisition.py:258
#, python-format
msgid "You have no line selected for buying."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "Amedya. PO0025"

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Azemz n yizen anegaru iţunefken ɣef ukalas agi"

#~ msgid "Followers"
#~ msgstr "Imeltaɣen"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Ma yeṛcem, iznan imaynuten suturen tamuɣli leqqayen."

#~ msgid "Last Message Date"
#~ msgstr "Azemz n yizen aneggaru"

#~ msgid "Messages"
#~ msgstr "Iznan"

#~ msgid "Messages and communication history"
#~ msgstr "Amazray n iznan  d  teywalin"

#~ msgid "Unread Messages"
#~ msgstr "Iznan ur neţwaɣer-ara"

#~ msgid "Website Messages"
#~ msgstr "Iznan n n usmel  Web"

#~ msgid "Website communication history"
#~ msgstr "Amazray n Taywalt n usmel  Web"
