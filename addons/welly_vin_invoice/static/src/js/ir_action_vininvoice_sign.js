/** @odoo-module **/
import { registry } from "@web/core/registry";


registry.category("action_handlers").add('ir.actions.act_vin_invoice_sign',
    async (options) => {
        const { einvoice_id, invoice_number, login_key, tax_code } = options.action;
        const { orm, notification } = options.env.services;
        if (!invoice_number || !login_key || !tax_code) {
            notification.add(
                options.env._t("Missing configuration for e-invoice signing. Please check company settings (VIN-Invoice section)."),
                { type: "danger" }
            );
            return;
        }

        const payload = {
            idValue: String(invoice_number),
            actionType: "InvoiceTax",
            loginKey: login_key,
            taxCode: tax_code,
        };

        const response = await fetch("http://localhost:24038/certificated", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            notification.add(
                options.env._t(`E-invoice signing failed: ${response.status}`),
                { type: "danger" }
            );
            return;
        }

        const res = await response.json();
        if (res.errorCode !== "0") {
            notification.add(
                options.env._t(`E-invoice signing failed: ${res.errors}`),
                { type: "danger" }
            );
            return;
        }
        await orm.write('account.move.einvoice', [einvoice_id], { state: 'signed' });
        notification.add(
            options.env._t(`E-invoice signing successful`),
            { type: "success" }
        );
        options.env.services.dialog.closeAll();
    }
);
