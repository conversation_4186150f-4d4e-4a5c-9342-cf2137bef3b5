# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class EInvoiceConfirmWizard(models.TransientModel):
    _inherit = 'einvoice.confirm.wizard'

    def _call_einvoice_api(self, einvoice_data):
        """Override để sử dụng VinInvoice API và xử lý toàn bộ logic"""
        
        # Tạo hóa đơn điện tử
        einvoice = self.env['account.move.einvoice'].create_from_payment(
            self.payment_id, einvoice_data
        )
        
        # Kiểm tra xem công ty có cấu hình VinInvoice không
        if self.company_id.e_invoice_provider != 'vininvoice':
            # Nếu không có cấu hình VinInvoice, sử dụng mock
            return super()._call_einvoice_api(einvoice_data)
        
        # Gọi API VinInvoice thực sự với einvoice_data
        vin_instance = self.company_id.vininvoice_config_id
        result = vin_instance.create_electronic_invoice(einvoice_data)
        
        # Cập nhật thông tin từ kết quả API trả về
        einvoice.ensure_one()
        
        # Cập nhật số hóa đơn (đảm bảo 8 ký tự)
        invoice_id = result.get('invoice_id', '')
        if invoice_id:
            einvoice.invoice_number = invoice_id
        
        # Cập nhật ngày hóa đơn
        einvoice.invoice_date = fields.Datetime.now()
        
        # Cập nhật mã tra cứu
        if invoice_id:
            einvoice.lookup_code = invoice_id
        
        # Cập nhật trạng thái đã gửi
        einvoice.state = 'sent'
        
        # Xác nhận thanh toán và xuất hóa đơn điện tử (logic từ confirm_payment_and_export_einvoice)
        payment = self.payment_id
        payment.ensure_one()
        
        # Cập nhật trạng thái xác nhận thanh toán
        payment.is_payment_confirmed = True
        
        # Đánh dấu đã xuất hóa đơn
        payment.is_einvoice_exported = True
        
        # Kiểm tra tất cả thanh toán đã được xác nhận
        payment._check_move_approval_status()
        
        # Ghi log
        payment.move_id.message_post(
            body=_('Thanh toán %s đã được xác nhận và xuất hóa đơn điện tử.') % payment.name
        )

        view_id = self.env.ref('welly_e_invoice.view_account_move_einvoice_form').id
        action = {
            'name': _('Hóa đơn điện tử'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.move.einvoice',
            'view_mode': 'form',
            'view_type': 'form',
            'res_id': einvoice.id,
            'target': 'new',
            'view_id': view_id,
            'context': {'readonly': True},
        }
        # Trả về notification thành công
        return action