from odoo import models
from odoo.exceptions import UserError


class AccountMoveEInvoice(models.Model):
    _inherit = 'account.move.einvoice'

    def action_sign(self):
        """Override action_sign để sử dụng VinInvoice API"""
        if self.env.company.e_invoice_provider != 'vininvoice':
            return super().action_sign()
        
        return {
            'type': 'ir.actions.act_vin_invoice_sign',
            'einvoice_id': self.id,
            'login_key': self.company_id.vininvoice_config_id.login_key,
            'tax_code': self.company_id.tax_code,
            'invoice_number': self.invoice_number,
        }