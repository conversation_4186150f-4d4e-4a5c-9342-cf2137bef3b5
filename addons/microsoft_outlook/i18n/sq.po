# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Language-Team: Albanian (https://app.transifex.com/odoo/teams/41243/sq/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sq\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('server_type', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('smtp_authentication', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Outlook account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your personal Outlook account using OAuth. \n"
"You will be redirected to the Outlook login page to accept the permissions."
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID of your Outlook app"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__fetchmail_server__server_type__outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__ir_mail_server__smtp_authentication__outlook
msgid "Outlook OAuth Authentication"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please connect with your Outlook account before using it."
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Outlook/Office365 username "
"(your email address). This should be the same account as the one used for "
"the Outlook OAuthentication Token."
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Read More"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required for the server %r."
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret of your Outlook app"
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__server_type
msgid "Server Type"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"This server %r can only be used for your personal email address. Please fill"
" the \"from_filter\" field with %r."
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr ""
