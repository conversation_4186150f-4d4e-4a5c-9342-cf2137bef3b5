# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_outlook
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                        Connect your Outlook account"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "<i class=\"fa fa-cog\" title=\"Edit Settings\"/>"
msgstr "<i class=\"fa fa-cog\" title=\"แก้ไขการตั้งค่า\"/>"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('server_type', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"<span attrs=\"{'invisible': ['|', ('smtp_authentication', '!=', 'outlook'), ('microsoft_outlook_refresh_token', '=', False)]}\" class=\"badge text-bg-success\">\n"
"                        Outlook Token Valid\n"
"                    </span>"
msgstr ""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "An error occurred when fetching the access token. %s"
msgstr "เกิดข้อผิดพลาดขณะดึงข้อมูลโทเค็นเพื่อการเข้าถึง %s"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__smtp_authentication
msgid "Authenticate with"
msgstr "ยืนยันตัวตนด้วย"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "Authentication URI"
msgstr "URI การยืนยันถูกต้อง"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Connect your Outlook account with the OAuth Authentication process.  \n"
"By default, only a user with a matching email address will be able to use this server. To extend its use, you should set a \"mail.default.from\" system parameter."
msgstr ""
"เชื่อมต่อบัญชี Outlook ของคุณกับกระบวนการยืนยันตัวตน OAuth\n"
"ตามค่าเริ่มต้น เฉพาะผู้ใช้ที่มีที่อยู่อีเมลตรงกันเท่านั้นที่จะสามารถใช้เซิร์ฟเวอร์นี้ได้ หากต้องการขยายการใช้งาน คุณควรตั้งค่าพารามิเตอร์ระบบ \"mail.default.from\""

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid ""
"Connect your personal Outlook account using OAuth. \n"
"You will be redirected to the Outlook login page to accept the permissions."
msgstr ""
"เชื่อมต่อบัญชี Outlook ส่วนตัวของคุณโดยใช้ OAuth\n"
"คุณจะถูกนำไปที่หน้าเข้าสู่ระบบ Outlook เพื่อยอมรับการอนุญาต"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.microsoft_outlook_oauth_error
msgid "Go back to your mail server"
msgstr "กลับไปที่เซิร์ฟเวอร์อีเมลของคุณ"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID"
msgstr "ไอดี"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "ID of your Outlook app"
msgstr "ID ของแอป Outlook ของคุณ"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr "เซิร์ฟเวอร์อีเมลขาเข้า"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Incorrect Connection Security for Outlook mail server %r. Please set it to "
"\"TLS (STARTTLS)\"."
msgstr ""
"ความปลอดภัยในการเชื่อมต่อไม่ถูกต้องสำหรับเซิร์ฟเวอร์จดหมาย Outlook %r "
"โปรดตั้งค่าเป็น \"TLS (STARTTLS)\""

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__is_microsoft_outlook_configured
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__is_microsoft_outlook_configured
msgid "Is Outlook Credential Configured"
msgstr "มีการกำหนดค่าข้อมูลรับรอง Outlook หรือไม่"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_ir_mail_server
msgid "Mail Server"
msgstr "Outgoing Mail Server"

#. module: microsoft_outlook
#: model:ir.model,name:microsoft_outlook.model_microsoft_outlook_mixin
msgid "Microsoft Outlook Mixin"
msgstr "Microsoft Outlook มิกซ์อิน"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Only the administrator can link an Outlook mail server."
msgstr "มีเพียงผู้ดูแลระบบเท่านั้นที่สามารถลิงก์เซิร์ฟเวอร์อีเมล Outlook ได้"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token
msgid "Outlook Access Token"
msgstr "โทเค็นการเข้าถึง Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_access_token_expiration
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_access_token_expiration
msgid "Outlook Access Token Expiration Timestamp"
msgstr "การประทับเวลาหมดอายุของโทเค็นการเข้าถึง Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_identifier
msgid "Outlook Client Id"
msgstr "Outlook Client Id"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_res_config_settings__microsoft_outlook_client_secret
msgid "Outlook Client Secret"
msgstr "ความลับของไคลเอนต์ Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__fetchmail_server__server_type__outlook
#: model:ir.model.fields.selection,name:microsoft_outlook.selection__ir_mail_server__smtp_authentication__outlook
msgid "Outlook OAuth Authentication"
msgstr "การยืนยันตัวตน OAuth ของ Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_ir_mail_server__microsoft_outlook_refresh_token
#: model:ir.model.fields,field_description:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_refresh_token
msgid "Outlook Refresh Token"
msgstr "โทเค็นการรีเฟรช Outlook"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please configure your Outlook credentials."
msgstr "โปรดกำหนดค่าข้อมูลรับรอง Outlook ของคุณ"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Please connect with your Outlook account before using it."
msgstr "โปรดเชื่อมต่อกับบัญชี Outlook ของคุณก่อนใช้งาน"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please fill the \"Username\" field with your Outlook/Office365 username "
"(your email address). This should be the same account as the one used for "
"the Outlook OAuthentication Token."
msgstr ""
"กรุณากรอกข้อมูลในช่อง \"ชื่อผู้ใช้\" ด้วยชื่อผู้ใช้ Outlook/Office365 ของคุณ"
" (ที่อยู่อีเมลของคุณ) นี่ควรเป็นบัญชีเดียวกับบัญชีที่ใช้สำหรับ Outlook "
"OAuthentication Token"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"Please leave the password field empty for Outlook mail server %r. The OAuth "
"process does not require it"
msgstr ""
"โปรดเว้นฟิลด์รหัสผ่านว่างไว้สำหรับเซิร์ฟเวอร์อีเมล Outlook %r กระบวนการ "
"OAuth ไม่ต้องการมัน"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid "Read More"
msgstr "อ่านเพิ่มเติม"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/fetchmail_server.py:0
#, python-format
msgid "SSL is required for the server %r."
msgstr "จำเป็นต้องมี SSL สำหรับเซิร์ฟเวอร์ %r"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret"
msgstr "ความลับ"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.res_config_settings_view_form
msgid "Secret of your Outlook app"
msgstr "ความลับของแอปพลิเคชัน Outlook ของคุณ"

#. module: microsoft_outlook
#: model:ir.model.fields,field_description:microsoft_outlook.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "ประเภทเซิร์ฟเวอร์"

#. module: microsoft_outlook
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.fetchmail_server_view_form
#: model_terms:ir.ui.view,arch_db:microsoft_outlook.ir_mail_server_view_form
msgid ""
"Setup your Outlook API credentials in the general settings to link a Outlook"
" account."
msgstr ""
"ตั้งค่าข้อมูลประจำตัว Outlook API "
"ของคุณในการตั้งค่าทั่วไปเพื่อเชื่อมโยงบัญชี Outlook"

#. module: microsoft_outlook
#: model:ir.model.fields,help:microsoft_outlook.field_fetchmail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_ir_mail_server__microsoft_outlook_uri
#: model:ir.model.fields,help:microsoft_outlook.field_microsoft_outlook_mixin__microsoft_outlook_uri
msgid "The URL to generate the authorization code from Outlook"
msgstr "URL เพื่อสร้างรหัสการยืนยันตัวตนจาก Outlook"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/ir_mail_server.py:0
#, python-format
msgid ""
"This server %r can only be used for your personal email address. Please fill"
" the \"from_filter\" field with %r."
msgstr ""
"เซิร์ฟเวอร์ %r นี้สามารถใช้ได้เฉพาะกับที่อยู่อีเมลส่วนตัวของคุณเท่านั้น "
"กรุณากรอกช่อง \"from_filter\" ด้วย %r"

#. module: microsoft_outlook
#. odoo-python
#: code:addons/microsoft_outlook/models/microsoft_outlook_mixin.py:0
#, python-format
msgid "Unknown error."
msgstr "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ"
