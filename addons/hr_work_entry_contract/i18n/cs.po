# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract
# 
# Translators:
# <PERSON> <jan.<PERSON><PERSON><PERSON><PERSON>@centrum.cz>, 2022
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karolina.schustero<PERSON>@vdp.sk>, 2022
# <PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-04-14 05:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        Definuje zdroj pro generování pracovních záznamů\n"
"\n"
"        Pracovní plán: Pracovní záznamy budou generovány z pracovní doby níže.\n"
"        Docházka: Pracovní záznamy budou generovány z  docházky zaměstnance. (vyžaduje aplikaci Docházka)\n"
"        Plánování: Pracovní záznamy budou generovány z plánování zaměstnance. (vyžaduje aplikaci Plánování)\n"
"    "

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid "%s does not have a contract from %s to %s."
msgstr "%s nemá smlouvu od %s do %s."

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
#, python-format
msgid ""
"%s has multiple contracts from %s to %s. A work entry cannot overlap "
"multiple contracts."
msgstr ""
"%s má více smluv od %s do %s. Pracovní záznam se nemůže překrývat s více "
"smlouvami."

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\" title=\"Warning\"/>You are not "
"allowed to regenerate validated work entries"
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\" title=\"Varování\"/>Není "
"povoleno regenerovat ověřené pracovní položky"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "<i class=\"fa fa-info-circle me-1\" title=\"Hint\"/>"
msgstr "<i class=\"fa fa-info-circle me-1\" title=\"Nápověda\"/>"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<span class=\"text-muted\">Warning: The work entry regeneration will delete "
"all manual changes on the selected period.</span>"
msgstr ""
"<span class=\"text-muted\">Varování: Regenerace pracovního záznamu vymaže "
"všechny ruční změny ve zvoleném období.</span>"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Allow the work entry type to be linked with time off types."
msgstr "Povolit propojení typu pracovního záznamu s typy volna."

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Cancel"
msgstr "Zrušit"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_compensatory
msgid "Compensatory Time Off"
msgstr "Náhradní volno"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__contract_id
msgid "Contract"
msgstr "Smlouva"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date_message
msgid "Earliest Available Date Message"
msgstr "Nejdříve dostupná zpráva o datu"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date
msgid "Earliest date"
msgstr "Nejbližší datum"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__employee_id
msgid "Employee"
msgstr "Zaměstnanec"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_contract
msgid "Employee Contract"
msgstr "Smlouva zaměstnance"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__employee_ids
msgid "Employees"
msgstr "Zaměstnanci"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar_model.js:0
#, python-format
msgid "Everybody's work entries"
msgstr "Práce každého z nás"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_extra_hours
msgid "Extra Hours"
msgstr "Přesčasy"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_from
msgid "From"
msgstr "Od"

#. module: hr_work_entry_contract
#: model:ir.actions.server,name:hr_work_entry_contract.ir_cron_generate_missing_work_entries_ir_actions_server
#: model:ir.cron,cron_name:hr_work_entry_contract.ir_cron_generate_missing_work_entries
msgid "Generate Missing Work Entries"
msgstr "Generovat chybějící pracovní záznamy"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_from
msgid "Generated From"
msgstr "Generováno z"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_to
msgid "Generated To"
msgstr "Generováno do"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_leave
msgid "Generic Time Off"
msgstr "Obecné volno"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Vstup do práce HR"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Typ zadání HR práce"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_home_working
msgid "Home Working"
msgstr "Domácí práce"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"In order to regenerate the work entries, you need to provide the wizard with"
" an employee_id, a date_from and a date_to. In addition to that, the time "
"interval defined by date_from and date_to must not contain any validated "
"work entries."
msgstr ""
"Chcete-li znovu vygenerovat pracovní záznamy, musíte průvodce poskytnout s "
"identifikátorem zaměstnance, datem_z a datem_to. Kromě toho nesmí časový "
"interval definovaný date_from a date_to obsahovat žádné ověřené pracovní "
"položky."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__last_generation_date
msgid "Last Generation Date"
msgstr "Datum posledního generování"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date_message
msgid "Latest Available Date Message"
msgstr "Poslední dostupná zpráva o datu"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date
msgid "Latest date"
msgstr "Poslední datum"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_long_leave
msgid "Long Term Time Off"
msgstr "Dlouhodobé volno"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_legal_leave
msgid "Paid Time Off"
msgstr "Placené volné dny"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_regeneration_wizard
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Znovu vygenerujte záznamy o práci zaměstnanců"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/js/work_entries_controller_mixin.js:0
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
#, python-format
msgid "Regenerate Work Entries"
msgstr "Obnovte pracovní záznamy"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__search_criteria_completed
msgid "Search Criteria Completed"
msgstr "Kritéria vyhledávání dokončena"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_sick_leave
msgid "Sick Time Off"
msgstr "Volné dny z důvodu nevolnosti"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_contract.py:0
#, python-format
msgid ""
"Sorry, generating work entries from cancelled contracts is not allowed."
msgstr ""
"Je nám líto, ale generování pracovních záznamů ze zrušených smluv není "
"povoleno."

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
#, python-format
msgid ""
"The from date must be >= '%(earliest_available_date)s' and the to date must "
"be <= '%(latest_available_date)s', which correspond to the generated work "
"entries time interval."
msgstr ""
"Datum od musí být >= '%(earliest_available_date)s' a datum do musí být <= "
"'%(latest_available_date)s', které odpovídají časovému intervalu "
"generovaných pracovních záznamů."

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_contract_view_form_inherit
msgid "This work entry cannot be validated. The work entry type is undefined."
msgstr "Tuto pracovní položku nelze ověřit. Typ záznamu práce není definován."

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Time Off"
msgstr "Volné dny"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_to
msgid "To"
msgstr "Do"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_unpaid_leave
msgid "Unpaid"
msgstr "Nezaplacené"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__valid
msgid "Valid"
msgstr "Platný"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Work Entries"
msgstr "Pracovní záznamy"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__validated_work_entry_ids
msgid "Work Entries Within Interval"
msgstr "Pracovní záznamy v intervalu"

#. module: hr_work_entry_contract
#: model:ir.actions.act_window,name:hr_work_entry_contract.hr_work_entry_regeneration_wizard_action
msgid "Work Entry Regeneration"
msgstr "Regenerace vstupu do práce"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid "Work Entry Source"
msgstr "Zdroj pracovní položky"

#. module: hr_work_entry_contract
#: model:ir.model.fields.selection,name:hr_work_entry_contract.selection__hr_contract__work_entry_source__calendar
msgid "Working Schedule"
msgstr "Pracovní plán"
