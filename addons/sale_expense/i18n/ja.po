# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_expense
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr "経費の数"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__can_be_reinvoiced
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__can_be_reinvoiced
msgid "Can be reinvoiced"
msgstr "再度請求可能"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__sale_order_id
msgid "Customer to Reinvoice"
msgstr "再請求先顧客"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "経費"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_product_product__expense_policy_tooltip
#: model:ir.model.fields,field_description:sale_expense.field_product_template__expense_policy_tooltip
msgid "Expense Policy Tooltip"
msgstr "経費ポリシーツールのヒント"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "経費報告"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_account_bank_statement_line__expense_sheet_id
#: model:ir.model.fields,field_description:sale_expense.field_account_move__expense_sheet_id
#: model:ir.model.fields,field_description:sale_expense.field_account_payment__expense_sheet_id
msgid "Expense Sheet"
msgstr "経費シート"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "経費分割"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "経費"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
#, python-format
msgid "Expenses of this category may not be added to a Sales Order."
msgstr "このカテゴリの経費は販売オーダに追加することができません。"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
#, python-format
msgid ""
"Expenses will be added to the Sales Order at their actual cost when posted."
msgstr "経費は、計上時に実費で販売オーダに追加されます。"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
#, python-format
msgid ""
"Expenses will be added to the Sales Order at their sales price (product "
"price, pricelist, etc.) when posted."
msgstr "経費は、計上時に販売価格（プロダクト価格、価格表など）で販売オーダに追加されます。"

#. module: sale_expense
#: model:ir.model.fields,help:sale_expense.field_hr_expense__sale_order_id
msgid ""
"If the category has an expense policy, it will be reinvoiced on this sales "
"order"
msgstr "カテゴリに経費ポリシーがある場合は、この販売オーダで再請求されます。"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "請求"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move
msgid "Journal Entry"
msgstr "仕訳"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move_line
msgid "Journal Item"
msgstr "仕訳明細"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_product_template
msgid "Product"
msgstr "プロダクト"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/hr_expense_sheet.py:0
#, python-format
msgid "Reinvoiced Sales Orders"
msgstr "再請求された販売オーダ"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_sheet__sale_order_count
msgid "Sale Order Count"
msgstr "販売オーダ数"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Sales Order"
msgstr "販売オーダ"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.hr_expense_sheet_view_form
msgid "Sales Orders"
msgstr "オーダ数量"
