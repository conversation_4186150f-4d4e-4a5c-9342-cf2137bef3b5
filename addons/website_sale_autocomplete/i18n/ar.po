# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_autocomplete
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                            Create a Google Project and get a key"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                            إنشاء مشروع Google والحصول على مفتاح "

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                            Enable billing on your Google Project"
msgstr ""
"<i class=\"fa fa-arrow-right\"/>\n"
"                            قم بتفعيل الفوترة في مشروع Google الخاص بك "

#. module: website_sale_autocomplete
#: model_terms:ir.ui.view,arch_db:website_sale_autocomplete.res_config_settings_view_form_inherit_autocomplete_googleplaces
msgid "API Key"
msgstr "مفتاح الواجهة البرمجية للتطبيق "

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: website_sale_autocomplete
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_res_config_settings__google_places_api_key
#: model:ir.model.fields,field_description:website_sale_autocomplete.field_website__google_places_api_key
msgid "Google Places API Key"
msgstr "مفتاح الواجهة البرمجية لـ Google Places "

#. module: website_sale_autocomplete
#. odoo-javascript
#: code:addons/website_sale_autocomplete/static/src/xml/autocomplete.xml:0
#, python-format
msgid "Powered by Google"
msgstr "مشغل بواسطة Google "

#. module: website_sale_autocomplete
#: model:ir.model,name:website_sale_autocomplete.model_website
msgid "Website"
msgstr "الموقع الإلكتروني"
