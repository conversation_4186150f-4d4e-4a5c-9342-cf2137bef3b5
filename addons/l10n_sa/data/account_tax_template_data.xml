<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="sa_sales_tax_15" model="account.tax.template">
            <field name="name">Sales Tax 15%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="description">Sales Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_15"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_standard_rated_15_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_201017'),
                'plus_report_expression_ids': [ref('tax_report_line_standard_rated_15_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_standard_rated_15_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_201017'),
                'minus_report_expression_ids': [ref('tax_report_line_standard_rated_15_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_local_sales_tax_0" model="account.tax.template">
            <field name="name">Local Sales 0%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="description">Local Sales 0%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_local_sales_subject_to_0_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_local_sales_subject_to_0_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_local_sales_subject_to_0_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'minus_report_expression_ids': [ref('tax_report_line_local_sales_subject_to_0_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_export_sales_tax_0" model="account.tax.template">
            <field name="name">Export Sales 0%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="description">Export Sales 0%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_export_sales_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_export_sales_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_export_sales_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'minus_report_expression_ids': [ref('tax_report_line_export_sales_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_exempt_sales_tax_0" model="account.tax.template">
            <field name="name">Exempt Sales Tax 0%</field>
            <field name="type_tax_use">sale</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="description">Exempt Sales Tax 0%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_exempt_sales_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_exempt_sales_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_exempt_sales_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'minus_report_expression_ids': [ref('tax_report_line_exempt_sales_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_purchase_tax_15" model="account.tax.template">
            <field name="name">Purchase Tax 15%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="description">Purchase Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_15"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_standard_rated_15_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_104041'),
                'plus_report_expression_ids': [ref('tax_report_line_standard_rated_15_purchases_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_standard_rated_15_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_104041'),
                'minus_report_expression_ids': [ref('tax_report_line_standard_rated_15_purchases_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_rcp_tax_15" model="account.tax.template">
            <field name="name">Reverse charge provision Tax 15%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Reverse charge provision Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_15"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_imports_subject_tp_reverse_charge_mechanism_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_104041'),
                'plus_report_expression_ids': [ref('tax_report_line_imports_subject_tp_reverse_charge_mechanism_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_imports_subject_tp_reverse_charge_mechanism_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_104041'),
                'minus_report_expression_ids': [ref('tax_report_line_imports_subject_tp_reverse_charge_mechanism_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_import_tax_paid_15_paid_to_customs" model="account.tax.template">
            <field name="name">Import tax 15% Paid to customs</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="description">Import tax 15% Paid to customs</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_taxable_imports_15_paid_to_customs_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_101060'),
                'plus_report_expression_ids': [ref('tax_report_line_taxable_imports_15_paid_to_customs_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_taxable_imports_15_paid_to_customs_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_101060'),
                'minus_report_expression_ids': [ref('tax_report_line_taxable_imports_15_paid_to_customs_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_purchases_tax_0" model="account.tax.template">
            <field name="name">Purchases 0%</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="description">Purchases 0%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_zero_rated_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_zero_rated_purchases_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_zero_rated_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'minus_report_expression_ids': [ref('tax_report_line_zero_rated_purchases_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_exempt_purchases_tax" model="account.tax.template">
            <field name="name">Exempt Purchases</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">0</field>
            <field name="amount_type">percent</field>
            <field name="description">Exempt Purchases</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_other"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_exempt_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_exempt_purchases_tax_tag')],
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_exempt_purchases_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'minus_report_expression_ids': [ref('tax_report_line_exempt_purchases_tax_tag')],
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_rental" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Rental)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_rental_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_rental_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_rental_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_rental_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_tickets_or_air_freight" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Tickets or Air Freight)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_air_freight_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_air_freight_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_air_freight_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_air_freight_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_tickets_or_sea_freight" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Tickets or Sea Freight)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_sea_freight_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_sea_freight_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_sea_freight_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_tickets_or_sea_freight_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_international_telecommunication" model="account.tax.template">
            <field name="name">Withholding Tax 5% (International Telecommunication)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_international_telecommunication_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_international_telecommunication_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_international_telecommunication_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_international_telecommunication_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_distributed_profits" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Distributed Profits)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_distributed_profits_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_distributed_profits_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_distributed_profits_tax_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_distributed_profits_base_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_consulting_and_technical" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Consulting and Technical)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_consulting_and_technical_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_consulting_and_technical_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_consulting_and_technical_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_consulting_and_technical_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_return_from_loans" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Return from Loans)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_return_from_loans_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_return_from_loans_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_return_from_loans_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_return_from_loans_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_5_insurance_amd_reinsurance" model="account.tax.template">
            <field name="name">Withholding Tax 5% (Insurance &amp; Reinsurance)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">5</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 5%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_insurance_and_reinsurance_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_insurance_and_reinsurance_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_insurance_and_reinsurance_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_5_insurance_and_reinsurance_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_15_royalties" model="account.tax.template">
            <field name="name">Withholding Tax 15% (Royalties)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_royalties_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_royalties_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_royalties_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_royalties_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_15_paid_services_from_main_branch" model="account.tax.template">
            <field name="name">Withholding Tax 15% (Paid Services from Main Branch)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_main_branch_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_main_branch_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_main_branch_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_main_branch_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_15_paid_services_from_another_branch" model="account.tax.template">
            <field name="name">Withholding Tax 15% (Paid Services from another branch)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_another_branch_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_another_branch_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_another_branch_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_paid_services_from_another_branch_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_15_others" model="account.tax.template">
            <field name="name">Withholding Tax 15% (Others)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">15</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 15%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_others_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_others_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_others_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_15_others_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
        <record id="sa_withholding_tax_20_managerial" model="account.tax.template">
            <field name="name">Withholding Tax 20% (Managerial)</field>
            <field name="type_tax_use">purchase</field>
            <field name="amount">20</field>
            <field name="amount_type">percent</field>
            <field name="tax_scope">service</field>
            <field name="description">Withholding Tax 20%</field>
            <field name="tax_group_id" ref="sa_tax_group_taxes_withholding"/>
            <field name="chart_template_id" ref="sa_chart_template_standard"/>
            <field name="invoice_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_20_managerial_base_tag')],
            }),
            (0,0, {
                'account_id': ref('sa_account_400073'),
                'repartition_type': 'tax',
                'plus_report_expression_ids': [ref('tax_report_line_withholding_tax_20_managerial_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
            <field name="refund_repartition_line_ids" eval="[(5, 0, 0),
            (0,0, {
                'repartition_type': 'base',
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_20_managerial_base_tag')],
            }),
            (0,0, {
                'repartition_type': 'tax',
                'account_id': ref('sa_account_400073'),
                'minus_report_expression_ids': [ref('tax_report_line_withholding_tax_20_managerial_tax_tag')],
            }),
            (0,0, {
                'factor_percent': -100,
                'account_id': ref('sa_account_201020'),
                'repartition_type': 'tax',
            }),
        ]"/>
        </record>
    </data>
</odoo>
