# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* auth_password_policy
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <bask<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON>munkh Ganbat <<EMAIL>>, 2022\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: auth_password_policy
#: model:ir.model.fields,field_description:auth_password_policy.field_res_config_settings__minlength
msgid "Minimum Password Length"
msgstr "Нууц үгийн уртын доод хэмжээ"

#. module: auth_password_policy
#: model:ir.model.fields,help:auth_password_policy.field_res_config_settings__minlength
msgid ""
"Minimum number of characters passwords must contain, set to 0 to disable."
msgstr ""
"Нууц үг хамгийн багадаа хичнээн тэмдэгт агуулах ёстойг заана. 0 бол "
"тэмдэгтийн тоо заахгүй."

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_field.js:0
#, python-format
msgid "Password"
msgstr "Нууц үг"

#. module: auth_password_policy
#. odoo-python
#: code:addons/auth_password_policy/models/res_users.py:0
#, python-format
msgid "Passwords must have at least %d characters, got %d."
msgstr ""
"Нууц үг хамгийн багадаа %dтэмдэгт агуулах ёстой. Харин та %dтэмдэгт оруулсан"
" байна."

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
#, python-format
msgid ""
"Required: %s\n"
"\n"
"Hint: to increase password strength, increase length, use multiple words, and use non-letter characters."
msgstr ""

#. module: auth_password_policy
#: model:ir.model,name:auth_password_policy.model_res_users
msgid "User"
msgstr "Хэрэглэгч"

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s character classes"
msgstr ""

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s characters"
msgstr ""

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_policy.js:0
#, python-format
msgid "at least %s words"
msgstr ""

#. module: auth_password_policy
#. odoo-javascript
#: code:addons/auth_password_policy/static/src/password_meter.js:0
#, python-format
msgid "no requirements"
msgstr "Шаардлагууд хангасангүй."
