# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_loyalty
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2022
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# jabiri7, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid ""
"\n"
"        Technical field used to link multiple reward lines from the same reward together.\n"
"    "
msgstr ""
"\n"
"Camp tècnic utilitzat per enllaçar múltiples línies de recompensa de la mateixa recompensa junts."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/PartnerLine.js:0
#, python-format
msgid "%s Points"
msgstr "%s punts"

#. module: pos_loyalty
#: model:loyalty.program,name:pos_loyalty.15_pc_on_next_order
msgid "15% on next order"
msgstr "15% en la següent comanda"

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.15_pc_on_next_order_reward
msgid "15% on your order"
msgstr "15% en la vostra comanda"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "A better global discount is already applied."
msgstr "Ja s'aplica un descompte global millor."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "A reward could not be loaded"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__promo_barcode
msgid ""
"A technical field used as an alternative to the promo code. This is "
"automatically generated when the promo code is changed."
msgstr ""
"Un camp tècnic utilitzat com a alternativa al codi promocional. Això es "
"genera automàticament quan es canvia el codi promocional."

#. module: pos_loyalty
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "All PoS"
msgstr "Tots els PoS"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__any_product
msgid "Any Product"
msgstr "Qualsevol producte"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid ""
"Are you sure you want to remove %s from this order?\n"
" You will still be able to claim it through the reward button."
msgstr ""
"Esteu segur que voleu eliminar %s d'aquesta comanda?\n"
" Encara podràs reclamar-ho mitjançant el botó de recompensa."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Balance"
msgstr "Saldo"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__promo_barcode
#, python-format
msgid "Barcode"
msgstr "Codi de barres"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regla del codi de barres"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustos de configuració"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__coupon_id
#: model:ir.model.fields.selection,name:pos_loyalty.selection__barcode_rule__type__coupon
msgid "Coupon"
msgstr "Cupó"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Coupon Codes"
msgstr "Codis de cupons"

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.15_pc_on_next_order
msgid "Coupon point(s)"
msgstr "Punt(s) del cupó"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/Orderline.xml:0
#, python-format
msgid "Current Balance:"
msgstr "Saldo actual:"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Customer"
msgstr "Client/a"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Customer needed"
msgstr "Cal un client"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Deactivating reward"
msgstr "S'està desactivant la recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,help:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Defines the way you want to set your gift cards."
msgstr ""
"Defineix la forma en què desitja configurar les seves targetes de regal."

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_discount_loyalty_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Discount & Loyalty"
msgstr "Lleialtat del descompte"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/PromoCodeButton.js:0
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/PromoCodeButton.xml:0
#, python-format
msgid "Enter Code"
msgstr "Introduïu el codi"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Enter the gift card code"
msgstr "Introduïu el codi de la targeta regal"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Error"
msgstr "Error"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/PaymentScreen.js:0
#, python-format
msgid "Error validating rewards"
msgstr "Error validant recompenses"

#. module: pos_loyalty
#: model:loyalty.reward,description:pos_loyalty.loyalty_program_reward
msgid "Free Product - Simple Pen"
msgstr "Producte lliure - Simple Pen"

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__create_set
msgid "Generate PDF cards"
msgstr "Genera targetes PDF"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Generate a Gift Card"
msgstr "Genera una targeta de regal"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_config__gift_card_settings
#: model:ir.model.fields,field_description:pos_loyalty.field_res_config_settings__pos_gift_card_settings
msgid "Gift Cards settings"
msgstr "Paràmetres de les targetes de regal"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/PromoCodeButton.js:0
#, python-format
msgid "Gift card or Discount code"
msgstr "Targeta de regal o codi de descompte"

#. module: pos_loyalty
#: model:ir.ui.menu,name:pos_loyalty.menu_gift_ewallet_type_config
#: model_terms:ir.ui.view,arch_db:pos_loyalty.res_config_view_form_inherit_pos_loyalty
msgid "Gift cards & eWallet"
msgstr "Gift Cards eWallet"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__points_cost
msgid "How many point this reward cost on the coupon."
msgstr "Quants punts costa aquesta recompensa al cupó."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program reward. Use 1 currency per point discount."
msgstr ""
"Recompensa del programa de targetes regal no vàlida. Utilitza 1 divisa per "
"descompte de punt."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program rule. Use 1 point per currency spent."
msgstr ""
"Regla de programa de targeta regal no vàlida. Utilitza 1 punt per moneda "
"gastada."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one reward."
msgstr "El programa de la targeta regal no és vàlid. Més d'una recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Invalid gift card program. More than one rule."
msgstr "El programa de la targeta regal no és vàlid. Més d'una regla."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Is Reward Line"
msgstr "És una línia de reenviament"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "Comunicació lleial"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Cupó lleial"

#. module: pos_loyalty
#: model:loyalty.program,portal_point_name:pos_loyalty.loyalty_program
msgid "Loyalty Points"
msgstr "Punts de fidelitat "

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_program
#: model:loyalty.program,name:pos_loyalty.loyalty_program
msgid "Loyalty Program"
msgstr "Programa de fidelitat "

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Recompensa de fidelitat"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Regles de fidelitat "

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "No"
msgstr "No"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "No reward can be claimed with this coupon."
msgstr "No es pot reclamar cap recompensa amb aquest cupó."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "No rewards available."
msgstr "No hi ha recompenses disponibles."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "No valid eWallet found"
msgstr ""

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "Please select a product for this reward"
msgstr "Seleccioneu un producte per a aquesta recompensa"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "Please select a reward"
msgstr "Si us plau selecciona una recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_order_count
msgid "PoS Order Count"
msgstr "Comptador de comandes PoS"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS Order Reference"
msgstr "Referència de l'ordre PoS"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
#, python-format
msgid "PoS Orders"
msgstr "Comandes TPV "

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_card__source_pos_order_id
msgid "PoS order where this coupon was generated."
msgstr "Ordre del PoS on es va generar aquest cupó."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_ok
#: model_terms:ir.ui.view,arch_db:pos_loyalty.loyalty_program_view_form_inherit_pos_loyalty
msgid "Point of Sale"
msgstr "Punt de Venda"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuració del Punt de Venda"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Línies de tiquet de punt de venda"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_order
msgid "Point of Sale Orders"
msgstr "Comandes del Punt de Venda"

#. module: pos_loyalty
#: model:ir.model,name:pos_loyalty.model_pos_session
msgid "Point of Sale Session"
msgstr "Sessió del Punt de Venda"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Point of Sales"
msgstr "Terminal Punt de Venda"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Points"
msgstr "Punts "

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__points_cost
msgid "Points Cost"
msgstr "Cost dels punts"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_mail__pos_report_print_id
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "Print Report"
msgstr "Imprimeix l'informe"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Reward Product: `%(reward_product)s`"
msgstr "Programa: %(name)s, Reenvia el producte: `%(reward_product)s`"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "Program: %(name)s, Rule Product: `%(rule_product)s`"
msgstr "Programa: %(name)s, Regla Producte: `%(rule_product)s`"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Refund with eWallet"
msgstr "Abonament amb eWallet"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/TicketScreen.js:0
#, python-format
msgid ""
"Refunding a top up or reward product for an eWallet or gift card program is "
"not allowed."
msgstr ""
"No es permet el reemborsament d'un producte superior o de recompensa per a "
"un programa eWallet o de targeta regal."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/ResetProgramsButton.xml:0
#, python-format
msgid "Reset Programs"
msgstr "Reinicia els programes"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_config_ids
msgid "Restrict publishing to those shops."
msgstr "Restringeix la publicació a aquestes botigues."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/ControlButtons/RewardButton.xml:0
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_id
#, python-format
msgid "Reward"
msgstr "Recompensa"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_pos_order_line__reward_identifier_code
msgid "Reward Identifier Code"
msgstr "Reenvia el codi d'identificació"

#. module: pos_loyalty
#: model:ir.model.fields.selection,name:pos_loyalty.selection__pos_config__gift_card_settings__scan_use
msgid "Scan existing cards"
msgstr "Escaneja les targetes existents"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Select program"
msgstr "Selecciona el programa"

#. module: pos_loyalty
#: model:product.template,name:pos_loyalty.simple_pen_product_template
msgid "Simple Pen"
msgstr "Bolígraf simple"

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"Some coupons are invalid. The applied coupons have been updated. Please "
"check the order."
msgstr ""
"Alguns cupons no són vàlids. S'han actualitzat els cupons aplicats. Si us "
"plau, comproveu la comanda."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Spent:"
msgstr "Ort:"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__any_product
msgid "Technical field, whether all product match"
msgstr "Camp tècnic, si tot el producte coincideix"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "That coupon code has already been scanned and activated."
msgstr "Aquest codi de cupó ja s'ha escanejat i activat."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "That promo code program has already been activated."
msgstr "Aquest programa de codi promocional ja s'ha activat."

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__coupon_id
msgid "The coupon used to claim that reward."
msgstr "El cupó solia reclamar aquesta recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid ""
"The following codes already exist in the database, perhaps they were already sold?\n"
"%s"
msgstr ""
"Els següents codis ja existeixen en la base de dades, potser ja s'han venut?\n"
"%s"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_mail__pos_report_print_id
msgid ""
"The report action to be executed when creating a coupon/gift card/loyalty "
"card in the PoS."
msgstr ""
"L'informe de l'acció que s'executarà en crear un cupó/targeta de "
"regal/targeta de lleialtat al PoS."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid ""
"The reward \"%s\" contain an error in its domain, your domain must be "
"compatible with the PoS client"
msgstr ""

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__reward_id
msgid "The reward associated with this line."
msgstr "La recompensa associada a aquesta línia."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "The reward could not be applied."
msgstr "No es va poder aplicar la recompensa."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/RewardButton.js:0
#, python-format
msgid "There are no rewards claimable for this customer."
msgstr "No hi ha recompenses reclamables per aquest client."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_order.py:0
#, python-format
msgid "There are not enough points for the coupon: %s."
msgstr "No hi ha prou punts per al cupó: %s."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "There are not enough points on the coupon to claim this reward."
msgstr "No hi ha punts suficients al cupó per reclamar aquesta recompensa."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "There are not enough products in the basket to claim this reward."
msgstr ""
"No hi ha prou productes a la cistella per reclamar aquesta recompensa."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no email template on the gift card program and your pos is set to "
"print them."
msgstr ""
"No hi ha cap plantilla de correu electrònic al programa de la targeta regal "
"i el vostre equip està configurat per imprimir-les."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"There is no print report on the gift card program and your pos is set to "
"print them."
msgstr ""
"No hi ha cap informe d'impressió al programa de la targeta regal i el vostre"
" equip està configurat per imprimir-los."

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "These are the products that are valid for this rule."
msgstr "Aquests són els productes vàlids per a aquesta norma."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is expired (%s)."
msgstr "Aquest cupó ha caducat (%s)."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid "This coupon is invalid (%s)."
msgstr "Aquest cupó no és vàlid (%s)."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "This gift card has already been sold"
msgstr "Aquesta targeta de regal ja s'ha venut"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid ""
"This gift card is not linked to any order. Do you really want to apply its "
"reward?"
msgstr ""
"Aquesta targeta regal no està enllaçada a cap comanda. Realment vols aplicar"
" la seva recompensa?"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_loyalty_program__pos_report_print_id
msgid "This is used to print the generated gift cards from PoS."
msgstr ""
"Això s'utilitza per a imprimir les targetes de regal generades des del PoS."

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/pos_config.py:0
#, python-format
msgid ""
"To continue, make the following reward products available in Point of Sale."
msgstr ""
"Per continuar, feu disponibles els següents productes de recompensa a Point "
"of Sale."

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_barcode_rule__type
msgid "Type"
msgstr "Tipus"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unknown discount type"
msgstr "Tipus de descompte desconegut"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unpaid gift card"
msgstr "Targeta regal no pagada"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/Loyalty.js:0
#, python-format
msgid "Unpaid gift card rejected."
msgstr "S'ha rebutjat la targeta de regal no pagada."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "Use eWallet to pay"
msgstr "Utilitza eWallet per pagar"

#. module: pos_loyalty
#: model:ir.model.fields,field_description:pos_loyalty.field_loyalty_rule__valid_product_ids
msgid "Valid Product"
msgstr "Producte vàlid"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Valid until:"
msgstr "Valid fins a:"

#. module: pos_loyalty
#: model:ir.model.fields,help:pos_loyalty.field_pos_order_line__is_reward_line
msgid "Whether this line is part of a reward or not."
msgstr "Si aquesta línia forma part d'una recompensa o no."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "Won:"
msgstr "Won:"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "Yes"
msgstr "Sí"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "You cannot sell a gift card that has already been sold."
msgstr "No es pot vendre una targeta de regal que ja s'ha venut."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "You cannot set negative quantity or price to gift card or ewallet."
msgstr ""
"No podeu establir la quantitat o el preu negatiu a la targeta regal o "
"ewallet."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid ""
"You either have not created an eWallet or all your eWallets have expired."
msgstr ""

#. module: pos_loyalty
#. odoo-python
#: code:addons/pos_loyalty/models/loyalty_program.py:0
#, python-format
msgid "You must set '%s' before setting '%s'."
msgstr "Heu d'establir «%s» abans de definir «%s»."

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet"
msgstr "eWallet"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet Pay"
msgstr "Pagament eWallet"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ControlButtons/eWalletButton.js:0
#, python-format
msgid "eWallet Refund"
msgstr "Abonament eWallet"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/js/ProductScreen.js:0
#, python-format
msgid "eWallet requires a customer to be selected"
msgstr "eWallet requereix que se seleccioni un client"

#. module: pos_loyalty
#. odoo-javascript
#: code:addons/pos_loyalty/static/src/xml/OrderReceipt.xml:0
#, python-format
msgid "no expiration"
msgstr "sense venciment"
