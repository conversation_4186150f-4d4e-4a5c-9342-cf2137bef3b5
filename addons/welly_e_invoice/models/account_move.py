# -*- coding: utf-8 -*-

import json
from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class AccountMove(models.Model):
    _inherit = 'account.move'
    
    # New field to track if the move needs electronic invoice export
    is_need_export_einvoice = fields.Boolean(
        string='Cần xuất hóa đơn điện tử',
        help='<PERSON><PERSON>h dấu nếu biên lai này cần xuất hóa đơn điện tử',
        compute='_compute_is_need_export_einvoice',
        store=True,
    )
    @api.depends('account_payment_ids.is_einvoice_exported')
    def _compute_is_need_export_einvoice(self):
        for record in self:
            record.is_need_export_einvoice = not all(record.account_payment_ids.mapped('is_einvoice_exported'))

    # New fields for work package #4866
    invoice_customer_id = fields.Many2one(
        'res.partner',
        string='<PERSON>h<PERSON>ch hàng xuất hóa đơn',
        domain="[('customer_rank', '>', 0)]",
        help='<PERSON>h<PERSON><PERSON> hàng sẽ được ghi trên hóa đơn điện tử',
    )
    
    invoice_address_id = fields.Many2one(
        'invoice.address',
        string='Địa chỉ hóa đơn',
        help='Địa chỉ hóa đơn của khách hàng',
    )
    
    # Liên kết với hóa đơn điện tử
    einvoice_ids = fields.One2many(
        'account.move.einvoice',
        'move_id',
        string='Danh sách hóa đơn điện tử',
        readonly=True,
    )

    @api.onchange('partner_id')
    def _onchange_partner_id_invoice_customer(self):
        """Tự động đặt khách hàng xuất hóa đơn khi chọn partner"""
        if self.partner_id:
            self.invoice_customer_id = self.partner_id
            # Reset địa chỉ hóa đơn khi đổi khách hàng
            self.invoice_address_id = False

    @api.onchange('invoice_customer_id')
    def _onchange_invoice_customer_id(self):
        """Kiểm tra và cập nhật domain cho địa chỉ hóa đơn"""
        if not self.invoice_customer_id:
            self.invoice_address_id = False
            return
        
        # Nếu khách hàng là công ty, địa chỉ hóa đơn sẽ bắt buộc
        # Tìm địa chỉ hóa đơn có sẵn
        invoice_addresses = self.invoice_customer_id.child_ids.filtered(
            lambda x: x.type == 'invoice' and x.is_company == False
        )
        
        # Nếu có địa chỉ hóa đơn, tự động chọn địa chỉ đầu tiên
        if invoice_addresses:
            self.invoice_address_id = invoice_addresses[0]
        else:
            self.invoice_address_id = False

    @api.constrains('invoice_customer_id', 'invoice_address_id')
    def _check_invoice_address_required(self):
        """Kiểm tra bắt buộc địa chỉ hóa đơn cho công ty"""
        for record in self:
            if (record.invoice_customer_id and 
                record.invoice_customer_id.is_company and 
                not record.invoice_address_id):
                    raise ValidationError(_(
                        'Địa chỉ hóa đơn là bắt buộc khi khách hàng xuất hóa đơn là công ty.'
                    ))

    def _auto_create_einvoice_records(self):
        """Tự động tạo record hóa đơn điện tử cho các thanh toán xuất hóa đơn"""
        self.ensure_one()
        
        if self.move_type not in ['out_invoice', 'out_refund']:
            return
        
        # Lấy các thanh toán có journal xuất hóa đơn điện tử
        einvoice_payments = self.payment_ids.filtered('journal_id.export_e_invoice')
        
        for payment in einvoice_payments:
            # Kiểm tra xem đã có hóa đơn điện tử chưa
            existing_einvoice = self.einvoice_ids.filtered(lambda e: e.payment_id == payment)
            
            if not existing_einvoice:
                # Tạo hóa đơn điện tử mới
                einvoice_vals = {
                    'move_id': self.id,
                    'payment_id': payment.id,
                    'amount_total': payment.amount,
                    'state': 'draft',
                }
                self.env['account.move.einvoice'].create(einvoice_vals)

    @api.model_create_single
    def create(self, vals):
        """Override create để tự động tạo hóa đơn điện tử"""
        res = super().create(vals)
        if not res.invoice_customer_id:
            res.invoice_customer_id = res.partner_id.id
            address = res.partner_id.address_ids.filtered(lambda x: x.is_invoice_address)
            if address:
                res.invoice_address_id = address[0].id
        return res
    
    def write(self, vals):
        """Override write để cập nhật danh sách hóa đơn điện tử"""
        result = super().write(vals)
        
        # Nếu có thay đổi về thanh toán, cập nhật danh sách hóa đơn điện tử
        if any(field in vals for field in ['payment_ids', 'state']):
            for record in self:
                record._auto_create_einvoice_records()
        
        return result