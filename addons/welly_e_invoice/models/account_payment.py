# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    # Trường cho xác nhận xuất hóa đơn điện tử
    is_einvoice_exported = fields.Boolean(
        string='Đã xuất hóa đơn',
        default=False,
        readonly=True,
        help='Đánh dấu khi đã xuất hóa đơn điện tử cho thanh toán này'
    )
    
    is_payment_confirmed = fields.Bo<PERSON>an(
        string='Xác nhận thanh toán',
        default=False,
        readonly=True,
        help='Đ<PERSON>h dấu khi kế toán đã xác nhận thanh toán'
    )
    
    # Liên kết với hóa đơn điện tử
    einvoice_id = fields.Many2one(
        'account.move.einvoice',
        string='Hóa đơn điện tử'
    )
    
      # Ki<PERSON><PERSON> tra có thể xuất hóa đơn điện tử
    can_confirm_einvoice = fields.<PERSON><PERSON><PERSON>(
        string='<PERSON><PERSON> thể xác nhận xuất HĐ',
        compute='_compute_can_confirm_einvoice'
    )
    
    # Related field để sử dụng trong view attrs
    journal_export_einvoice = fields.Boolean(
        string='Journal xuất hóa đơn điện tử',
        related='journal_id.export_e_invoice',
        readonly=True
    )

    @api.depends('journal_id', 'is_payment_confirmed', 'state')
    def _compute_can_confirm_einvoice(self):
        """Kiểm tra có thể xác nhận xuất hóa đơn điện tử"""
        for record in self:
            can_confirm = (
                record.state == 'posted' and
                record.journal_id.export_e_invoice and
                not record.is_payment_confirmed
            )
            record.can_confirm_einvoice = can_confirm

    def action_confirm_einvoice(self):
        """Mở wizard xác nhận xuất hóa đơn điện tử"""
        self.ensure_one()
        
        if not self.can_confirm_einvoice:
            raise UserError(_('Không thể xác nhận xuất hóa đơn điện tử cho thanh toán này.'))
        
        # Kiểm tra thông tin cần thiết
        if not self.account_move_parent_id.invoice_customer_id:
            raise UserError(_('Vui lòng chọn khách hàng xuất hóa đơn trước.'))
        
        if (self.account_move_parent_id.invoice_customer_id.is_company and 
            not self.account_move_parent_id.invoice_address_id):
            raise UserError(_('Vui lòng chọn địa chỉ hóa đơn cho khách hàng công ty.'))
        
        return {
            'name': _('Xuất hóa đơn điện tử'),
            'type': 'ir.actions.act_window',
            'res_model': 'einvoice.confirm.wizard',
            'view_mode': 'form',
            'view_type': 'form',
            'target': 'new',
            'context': {
                'default_payment_id': self.id,
            }
        }

    def _check_move_approval_status(self):
        """Kiểm tra và cập nhật trạng thái phê duyệt của biên lai"""
        move = self.account_move_parent_id
        
        # Lấy tất cả thanh toán của biên lai
        payments = move.payment_ids
        
        # Kiểm tra các thanh toán có xuất hóa đơn điện tử
        einvoice_payments = payments.filtered('journal_id.export_e_invoice')
        non_einvoice_payments = payments.filtered(lambda p: not p.journal_id.export_e_invoice)
        
        # Kiểm tra điều kiện tự động phê duyệt
        all_einvoice_confirmed = all(p.is_payment_confirmed for p in einvoice_payments)
        
        if all_einvoice_confirmed and move.payment_state == 'paid':
            # Nếu tất cả thanh toán xuất hóa đơn đã xác nhận và đã thanh toán đủ
            # Tự động xác nhận các thanh toán không xuất hóa đơn
            non_einvoice_payments.write({'is_payment_confirmed': True})
            
            # Cập nhật trạng thái phê duyệt biên lai
            move.approval_state = 'approved'

    def action_view_einvoices(self):
        """Xem danh sách hóa đơn điện tử"""
        self.ensure_one()
        
        return {
            'name': _('Hóa đơn điện tử'),
            'type': 'ir.actions.act_window',
            'res_model': 'account.move.einvoice',
            'view_mode': 'tree,form',
            'domain': [('payment_id', '=', self.id)],
            'context': {'default_payment_id': self.id, 'default_move_id': self.move_id.id}
        }
