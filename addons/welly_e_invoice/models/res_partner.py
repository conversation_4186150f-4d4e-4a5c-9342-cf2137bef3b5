# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResPartner(models.Model):
    _inherit = 'res.partner'

    address_ids = fields.One2many(
        'invoice.address',
        'partner_id',
        string='Địa chỉ giao hàng và Xuất hóa đơn'
    )
    
    # Computed fields for quick access
    invoice_address_count = fields.Integer(
        string='Số địa chỉ hóa đơn',
        compute='_compute_address_counts'
    )
    delivery_address_count = fields.Integer(
        string='Số địa chỉ giao hàng',
        compute='_compute_address_counts'
    )
    
    def _compute_address_counts(self):
        """Tính số lượng địa chỉ theo loại"""
        for record in self:
            record.invoice_address_count = len(record.address_ids.filtered('is_invoice_address'))
            record.delivery_address_count = len(record.address_ids.filtered('is_delivery_address'))

    def _auto_create_invoice_address(self):
        """Tự động tạo invoice address từ thông tin địa chỉ partner"""
        if not self.customer_rank or not any([self.street, self.city, self.state_id, self.country_id]):
            return
            
        # Ki<PERSON>m tra xem đã có invoice address với thông tin tương tự chưa
        existing_address = self.address_ids.filtered(
            lambda addr: (
                addr.street == (self.street or '') and
                addr.state_id == self.state_id and
                addr.country_id == (self.country_id or self.env.ref('base.vn', False)) and
                addr.is_invoice_address
            )
        )
        
        if existing_address:
            return  # Đã có address tương tự, không tạo duplicate
            
        # Tạo invoice address mới
        country_id = self.country_id.id if self.country_id else self.env.ref('base.vn', False).id
        
        self.env['invoice.address'].create({
            'partner_id': self.id,
            'street': self.street or '',
            'state_id': self.state_id.id if self.state_id else False,
            'country_id': country_id,
            'is_invoice_address': True,
            'is_delivery_address': False,
        })
    
    def write(self, vals):
        """Override write để detect thay đổi địa chỉ và tự động tạo invoice address"""
        result = super().write(vals)
        
        # Kiểm tra nếu có thay đổi các field địa chỉ
        address_fields = ['street', 'city', 'state_id', 'country_id']
        if any(field in vals for field in address_fields):
            for record in self:
                record._auto_create_invoice_address()
                
        return result

    @api.model_create_single
    def create(self, vals):
        """Override create để tự động tạo invoice address khi tạo partner mới"""
        partner = super().create(vals)
        partner._auto_create_invoice_address()
        return partner
