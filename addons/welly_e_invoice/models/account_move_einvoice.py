# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class AccountMoveEInvoice(models.Model):
    _name = 'account.move.einvoice'
    _description = 'Danh sách hóa đơn điện tử'
    _order = 'create_date desc'
    _rec_name = 'invoice_number'

    # Liên kết với account.move và account.payment
    move_id = fields.Many2one(
        'account.move',
        string='Biên lai',
        related='payment_id.account_move_parent_id',
        ondelete='cascade',
        store=True
    )
    
    payment_id = fields.Many2one(
        'account.payment',
        string='Thanh toán',
        help='Dòng thanh toán liên quan đến hóa đơn điện tử này'
    )
    
    # Thông tin hóa đơn điện tử
    invoice_number = fields.Char(
        string='Số hóa đơn',
        help='Số hóa đơn đượ<PERSON> hệ thống eInvoice trả về (8 ký tự)'
    )
    
    invoice_date = fields.Datetime(
        string='<PERSON>ày hóa đơn',
        default=fields.Datetime.now
    )
    
    template_no = fields.Char(
        string='Mẫu số',
        help='Mẫu số hóa đơn'
    )
    
    symbol = fields.Char(
        string='Ký hiệu',
        help='Ký hiệu hóa đơn'
    )
    
    template_symbol = fields.Char(
        string='Mẫu số & Ký hiệu',
        compute='_compute_template_symbol',
        store=True
    )
    
    # Thông tin tài chính
    amount_total = fields.Monetary(
        string='Tổng cộng',
        currency_field='currency_id',
        related='payment_id.amount_total'
    )
    
    currency_id = fields.Many2one(
        related='move_id.currency_id',
        store=True
    )
    
    company_id = fields.Many2one('res.company', related='move_id.company_id')

    # Thông tin eInvoice
    lookup_code = fields.Char(
        string='Mã tra cứu',
        help='Mã tra cứu hóa đơn điện tử từ eInvoice'
    )
    
    # Trạng thái
    state = fields.Selection([
        ('draft', 'Nháp'),
        ('sent', 'Đã gửi'),
        ('signed', 'Đã ký số'),
        ('confirmed', 'Đã xác nhận'),
        ('error', 'Lỗi'),
    ], string='Trạng thái', default='draft')
    
    # File PDF hóa đon
    invoice_pdf = fields.Binary(
        string='File hóa đơn điện tử',
        help='File PDF hóa đơn điện tử từ eInvoice'
    )
    
    invoice_pdf_filename = fields.Char(
        string='Tên file hóa đơn',
        compute='_compute_invoice_pdf_filename'
    )

    @api.depends('template_no', 'symbol')
    def _compute_template_symbol(self):
        """Tính toán mẫu số & ký hiệu"""
        for record in self:
            if record.template_no and record.symbol:
                record.template_symbol = f"{record.template_no}/{record.symbol}"
            else:
                record.template_symbol = ""

    @api.depends('move_id', 'invoice_number')
    def _compute_invoice_pdf_filename(self):
        """Tính toán tên file PDF"""
        for record in self:
            if record.invoice_number:
                record.invoice_pdf_filename = f"{record.invoice_number}.pdf"
            elif record.move_id:
                record.invoice_pdf_filename = f"{record.move_id.name}.pdf"
            else:
                record.invoice_pdf_filename = "hoadon.pdf"

    def action_view_invoice_pdf(self):
        """Xem hóa đơn điện tử"""
        self.ensure_one()
        if not self.invoice_pdf:
            raise ValidationError(_('Chưa có file hóa đơn điện tử.'))
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/account.move.einvoice/{self.id}/invoice_pdf/{self.invoice_pdf_filename}',
            'target': 'new',
        }

    @api.model
    def create_from_payment(self, payment, invoice_data: dict):
        """Tạo hóa đơn điện tử từ payment"""
        vals = {
            'payment_id': payment.id,
            'template_no': invoice_data.get('template_no'),
            'symbol': invoice_data.get('symbol'),
        }
        return self.create(vals)

    def action_sign(self):
        """Sẽ được override bởi các provider cụ thể"""

    @api.model_create_multi
    def create(self, vals_list):
        recs = super().create(vals_list)
        for rec in recs:
            rec.payment_id.einvoice_id = rec.id
        return recs