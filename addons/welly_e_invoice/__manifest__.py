# -*- coding: utf-8 -*-
{
    'name': 'Welly E-Invoice System',
    'version': '********.0',
    'category': 'Accounting',
    'summary': '<PERSON><PERSON> thống hóa đơn điện tử tích hợp vớ<PERSON>',
    'description': """
        Hệ thống hóa đơn điện tử bao gồm:
        - <PERSON><PERSON><PERSON> hình công ty với mã số thuế
        - Cấu hình journal xuất hóa đơn điện tử
        - Tự động tính giá sau thuế cho sản phẩm
        - Quản lý địa chỉ khách hàng
        - Quản lý mẫu hóa đơn
        - Tích hợp xuất hóa đơn điện tử
        - Hỗ trợ abstract provider pattern
    """,
    'author': 'WellyTech Company',
    'website': 'https://wellytech.com',
    'depends': [
        'base',
        'account',
        'sale',
        'product',
        'welly_base',
    ],
    'data': [
        # Security
        'security/security.xml', 
        'security/ir.model.access.csv',

        # Data
        'data/system_parameters.xml',
        'data/email_templates.xml',
        'data/uom_data.xml',

        # Views
        'views/res_company_views.xml',
        'views/account_journal_views.xml',
        'views/product_template_views.xml',
        'views/res_partner_views.xml',
        'views/invoice_address_views.xml',
        'views/invoice_template_views.xml',
        'views/account_move_views.xml',
        'views/account_payment_views.xml',
        'views/account_move_e_invoice_views.xml',

        # Wizards
        'wizard/einvoice_confirm_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'welly_e_invoice/static/src/css/e_invoice.css',
            'welly_e_invoice/static/src/js/account_move_einvoice_form_controller.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
    'post_init_hook': 'post_init_hook',
}
