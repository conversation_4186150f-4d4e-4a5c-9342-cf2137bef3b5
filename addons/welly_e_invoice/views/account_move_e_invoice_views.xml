<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Account Move E-Invoice Tree View -->
    <record id="view_account_move_einvoice_tree" model="ir.ui.view">
        <field name="name">account.move.einvoice.tree</field>
        <field name="model">account.move.einvoice</field>
        <field name="arch" type="xml">
            <tree string="Danh sách hóa đơn điện tử" decoration-success="state == 'confirmed'"
                decoration-info="state == 'signed'" decoration-warning="state == 'sent'"
                decoration-danger="state == 'error'">
                <field name="invoice_number" />
                <field name="invoice_date" />
                <field name="template_symbol" />
                <field name="amount_total" />
                <field name="lookup_code" />
                <field name="state" />
                <field name="invoice_pdf" invisible="1" />
                <button name="action_view_invoice_pdf"
                    type="object"
                    string="Xem hóa đơn"
                    icon="fa-file-pdf-o"
                    attrs="{'invisible': [('invoice_pdf', '=', False)]}" />
            </tree>
        </field>
    </record>

    <!-- Account Move E-Invoice Form View -->
    <record id="view_account_move_einvoice_form" model="ir.ui.view">
        <field name="name">account.move.einvoice.form</field>
        <field name="model">account.move.einvoice</field>
        <field name="arch" type="xml">
            <form string="Hóa đơn điện tử">
                <!-- Invisible fields -->
                <field name="invoice_pdf" invisible="1" />
                <field name="company_id" invisible="1" />

                <!-- View -->
                <header>
                    <field name="state" widget="statusbar" />
                    <button name="action_view_invoice_pdf"
                        type="object"
                        string="Xem hóa đơn điện tử"
                        class="btn-primary"
                        attrs="{'invisible': [('invoice_pdf', '=', False)]}" />
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="invoice_number" readonly="1"
                                placeholder="Chưa có số hóa đơn" />
                        </h1>
                    </div>

                    <group>
                        <group string="Thông tin cơ bản">
                            <field name="move_id" readonly="1" />
                            <field name="payment_id" readonly="1" />
                            <field name="invoice_date" readonly="1" />
                            <field name="template_no" readonly="1" />
                            <field name="symbol" readonly="1" />
                            <field name="template_symbol" readonly="1" />
                            <field name="lookup_code" readonly="1" />
                        </group>
                        <group string="Thông tin tài chính">
                            <field name="amount_total" readonly="1" />
                            <field name="currency_id" invisible="1" />
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button
                        type="object"
                        name="action_sign"
                        string="Sign"
                        class="btn-primary"
                        icon="fa-pencil"
                        attrs="{'invisible': [('state', 'not in', ['sent'])]}" />
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for Account Move E-Invoice -->
    <record id="action_account_move_einvoice" model="ir.actions.act_window">
        <field name="name">Hóa đơn điện tử</field>
        <field name="res_model">account.move.einvoice</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo hóa đơn điện tử đầu tiên của bạn!
            </p>
            <p>
                Hóa đơn điện tử sẽ được tạo tự động khi bạn xác nhận xuất hóa đơn từ thanh toán.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_account_move_einvoice"
        name="Hóa đơn điện tử"
        parent="account.menu_finance_entries"
        action="action_account_move_einvoice"
        sequence="15" />
</odoo>