<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Extend Account Move Form View -->
        <record id="view_account_move_form_e_invoice" model="ir.ui.view">
            <field name="name">account.move.form.e_invoice</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="welly_base.view_move_form" />
            <field name="arch" type="xml">
                <!-- Add E-Invoice fields -->
                <group name="line_3_1" position="inside">
                    <field name="invoice_customer_id"
                        attrs="{'invisible': [('move_type', 'not in', ['out_invoice', 'out_refund'])],
                                   'required': [('move_type', 'in', ['out_invoice', 'out_refund'])]}" />
                    <field name="invoice_address_id"
                        attrs="{'invisible': ['|', ('move_type', 'not in', ['out_invoice', 'out_refund']), ('invoice_customer_id', '=', False)]}" />
                </group>
                <!-- Add E-Invoice data tab -->
                <page name="payment_tab" position="after">
                    <page string="Danh sách hóa đơn điện tử" name="einvoice_list"
                        attrs="{'invisible': [('move_type', 'not in', ['out_invoice', 'out_refund'])]}">
                        <field name="einvoice_ids" nolabel="1" readonly="1">
                            <tree>
                                <field name="invoice_number" />
                                <field name="invoice_date" />
                                <field name="template_no" />
                                <field name="symbol" />
                                <field name="amount_total" />
                                <field name="lookup_code" />
                                <field name="state" />
                                <field name="invoice_pdf" invisible="1" />
                                <button
                                    type="object"
                                    name="action_sign"
                                    string="Sign"
                                    class="btn-primary"
                                    icon="fa-pencil"
                                    attrs="{'invisible': [('state', 'not in', ['sent'])]}" />
                                <button name="action_view_invoice_pdf"
                                    type="object"
                                    string="Xem hóa đơn điện tử"
                                    icon="fa-file-pdf-o"
                                    attrs="{'invisible': [('invoice_pdf', '=', False)]}" />
                            </tree>
                        </field>
                    </page>
                </page>

                <!-- Button xác nhận hoá đơn -->
                <button name="open_welly_recept_report" position="after">
                    <field name="is_einvoice_exported" invisible="1" />
                    <button name="action_confirm_einvoice"
                        type="object"
                        string="Xuất hóa đơn điện tử"
                        class="btn-primary"
                        attrs="{'invisible': [('is_einvoice_exported', '=', True)]}"
                    />
                </button>
            </field>
        </record>

        <!-- Extend Account Move Search View -->
        <record id="view_account_move_search_einvoice" model="ir.ui.view">
            <field name="name">account.move.search.einvoice</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter" />
            <field name="arch" type="xml">
                <filter name="posted" position="after">
                    <filter name="need_confirmation"
                        string="Biên lai cần xác nhận"
                        domain="[('is_need_export_einvoice', '=', True)]"
                        help="Biên lai có thanh toán cần xác nhận xuất hóa đơn" />
                </filter>
            </field>
        </record>
    </data>
</odoo>