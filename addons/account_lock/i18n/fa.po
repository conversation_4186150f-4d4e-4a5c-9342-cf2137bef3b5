# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_lock
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>hory <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:44+0000\n"
"Last-Translator: Mostafa Barmshory <<EMAIL>>, 2024\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"Any new All Users Lock Date must be posterior (or equal) to the previous "
"one."
msgstr ""
"تمام تاریخ‌های بستن حساب کاربران باید قبل (یا برابر با) از تاریخ قبلی باشند."
" "

#. module: account_lock
#: model:ir.model,name:account_lock.model_res_company
msgid "Companies"
msgstr "شرکت"

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The lock date for accountants is irreversible and can't be removed."
msgstr "تاریخ بستن حساب حسابداران غیر قابل برگشت و غیر قابل جابه‌جایی است."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The new tax lock date must be set after the previous lock date."
msgstr "تاریخ جدید بستن حساب مالیاتی باید پس از تاریخ قفل قبلی باشد."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid "The tax lock date is irreversible and can't be removed."
msgstr "تاریخ بستن حساب مالیاتی غیرقابل تغییر و جابه‌جایی است."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the All Users "
"Lock Date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"شما نمی‌توانید دوره‌ای که هنوز به پایان نرسیده است را ببندید. بنابراین، "
"تاریخ بستن حساب کاربران باید قبل از (برابر با) آخرین روز ماه قبل باشد."

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot lock a period that has not yet ended. Therefore, the tax lock "
"date must be anterior (or equal) to the last day of the previous month."
msgstr ""
"شما نمی‌توانید دوره‌ای که هنوز تمام نشده را ببندید. بنابراین تاریخ بستن حساب"
" مالیاتی باید قبل از (برابر با) آخرین روز ماه قبل باشد. "

#. module: account_lock
#: code:addons/account_lock/models/res_company.py:0
#, python-format
msgid ""
"You cannot set stricter restrictions on accountants than on users. "
"Therefore, the All Users Lock Date must be anterior (or equal) to the "
"Invoice/Bills Lock Date."
msgstr ""
"شما نمی‌توانید محدودیت‌های حسابداران را بیشتر از کاربران مشخص کنید. "
"بنابراین، تاریخ بستن حساب تمام کاربران باید قبل از (برابر با) تاریخ بستن "
"فاکتورها/صورت‌حساب‌ها باشد."
