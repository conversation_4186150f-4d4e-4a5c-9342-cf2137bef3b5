# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account
# 
# Translators:
# <PERSON><PERSON><PERSON>alay <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# Nadir <PERSON>azioglu <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON>ıl <<EMAIL>>, 2022
# <PERSON>, 2023
# İlknur Gözütok, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:51+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: İlknur Gözütok, 2023\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: mrp_account
#: code:addons/mrp_account/models/mrp_production.py:0
#: model:ir.model,name:mrp_account.model_account_analytic_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_bom__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__analytic_account_id
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter__costs_hour_account_id
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
#, python-format
msgid "Analytic Account"
msgstr "Analitik Hesap"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitik Satırı"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_production__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take        place "
"for financial management of the manufacturing order."
msgstr ""
"Üretim emrinin finansal yönetimi için maliyet ve gelir girişlerinin yer "
"alacağı analitik hesap."

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_bom__analytic_account_id
msgid ""
"Analytic account in which cost and revenue entries will take place for "
"financial management of the manufacturing order."
msgstr ""
"Üretim emrinin finansal yönetimi için maliyet ve gelir girişlerinin yer "
"alacağı analitik hesap."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_bom
msgid "Bill of Material"
msgstr "Ürün Reçetesi"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Bills of Materials"
msgstr "Ürün Reçetesi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__bom_count
msgid "BoM Count"
msgstr "BoM Sayısı"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_line__category
msgid "Category"
msgstr "Kategori"

#. module: mrp_account
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_product
#: model:ir.actions.server,name:mrp_account.action_compute_price_bom_template
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid "Compute Price from BoM"
msgstr "BoM'dan fiyat hesapla"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_ext_form_view2
#: model_terms:ir.ui.view,arch_db:mrp_account.product_product_view_form_normal_inherit_extended
#: model_terms:ir.ui.view,arch_db:mrp_account.product_variant_easy_edit_view_bom_inherit
msgid ""
"Compute the price of the product using products and operations of related "
"bill of materials, for manufactured products only."
msgstr ""
"Ürünün fiyatını yalnızca üretilmiş ürünlerin ürün ağaçlarıyla ilişkili "
"ürünleri ve işlemleri kullanarak hesapla."

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workcenter_productivity__cost_already_recorded
msgid "Cost Recorded"
msgstr "Maliyet Kaydedildi"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__extra_cost
msgid "Extra Unit Cost"
msgstr "Ekstra Birim Maliyet"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_account_move_line
msgid "Journal Item"
msgstr "Yevmiye Kalemi"

#. module: mrp_account
#: model:ir.model.fields.selection,name:mrp_account.selection__account_analytic_line__category__manufacturing_order
msgid "Manufacturing Order"
msgstr "Üretim Emri"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_ids
#: model_terms:ir.ui.view,arch_db:mrp_account.account_analytic_account_view_form_mrp
#, python-format
msgid "Manufacturing Orders"
msgstr "Üretim Emirleri"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__production_count
msgid "Manufacturing Orders Count"
msgstr "Üretim Emri Sayısı"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__mo_analytic_account_line_id
msgid "Mo Analytic Account Line"
msgstr "MO Analitik Hesap Satırları"

#. module: mrp_account
#: model:ir.model.fields,help:mrp_account.field_mrp_workcenter__costs_hour_account_id
msgid ""
"Posts analytical accounting entries in real time for both component and "
"operational costs."
msgstr ""
"Hem bileşen hem de operasyonel maliyetler için analitik muhasebe girişlerini"
" gerçek zamanlı olarak deftere nakleder."

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_template
msgid "Product"
msgstr "Ürün"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_product_product
msgid "Product Variant"
msgstr "Ürün Varyantı"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_production
msgid "Production Order"
msgstr "Üretim Emri"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_production__show_valuation
msgid "Show Valuation"
msgstr "Show Valuation"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_move
msgid "Stock Move"
msgstr "Stok Hareketi"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_stock_rule
msgid "Stock Rule"
msgstr "Stok Kuralı"

#. module: mrp_account
#: model_terms:ir.ui.view,arch_db:mrp_account.mrp_production_form_view_inherited
msgid "Valuation"
msgstr "Değerleme"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_mrp_workorder__wc_analytic_account_line_id
msgid "Wc Analytic Account Line"
msgstr "İş Merkezi Analitik Hesap Satırı"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter
msgid "Work Center"
msgstr "İş Merkezi"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "İş Merkezi Kullanımı"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workorder
msgid "Work Order"
msgstr "İş Emri"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workorder_count
msgid "Work Order Count"
msgstr "İş Emri Sayısı"

#. module: mrp_account
#: code:addons/mrp_account/models/analytic_account.py:0
#, python-format
msgid "Work Orders"
msgstr "İş Emirleri"

#. module: mrp_account
#: model:ir.model,name:mrp_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "İş Merkezi Verimlilik Günlüğü"

#. module: mrp_account
#: model:ir.model.fields,field_description:mrp_account.field_account_analytic_account__workcenter_ids
msgid "Workcenters"
msgstr "İş Merkezleri"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/stock_move.py:0
#, python-format
msgid "[Raw] %s"
msgstr "[Bileşen] %s"

#. module: mrp_account
#. odoo-python
#: code:addons/mrp_account/models/mrp_production.py:0
#: code:addons/mrp_account/models/mrp_workorder.py:0
#, python-format
msgid "[WC] %s"
msgstr "[WC] %s"
