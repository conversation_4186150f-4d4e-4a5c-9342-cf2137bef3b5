# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * event
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:07+0000\n"
"PO-Revision-Date: 2016-02-20 10:37+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: French (Belgium) (http://www.transifex.com/odoo/odoo-9/"
"language/fr_BE/)\n"
"Language: fr_BE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: event
#: model:mail.template,body_html:event.event_registration_mail_template_badge
msgid ""
"\n"
"<p>Dear ${object.name},</p>\n"
"<p>Thank you for your inquiry.</p>\n"
"<p>Here is your badge for the event ${object.event_id.name}.</p>\n"
"<p>If you have any questions, please let us know.</p>\n"
"<p>Best regards,</p>"
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_subscription
msgid ""
"\n"
"<p>Dear ${object.name},</p>\n"
"<p>Thank you for your inquiry.</p>\n"
"<p>We confirm your subscription to the event <b>${object.event_id.name}</b>. "
"You will receive emails with practical information.</p>\n"
"<p>If you have any questions, please let us know.</p>\n"
"<p>Best regards,</p>"
msgstr ""

#. module: event
#: model:mail.template,body_html:event.event_reminder
msgid ""
"\n"
"<p>Dear ${object.name},</p>\n"
"<p>Thank you for your registration to the event ${object.event_id.name}.</"
"p>\n"
"<p>The event will take place at ${object.event_id.address_id.city}\n"
"% if object.event_id.address_id.street\n"
", ${object.event_id.address_id.street}\n"
"% endif\n"
"from ${object.event_id.date_begin_located} to ${object.event_id."
"date_end_located}.</p>\n"
"<p>If you have any questions, please let us know.</p>\n"
"<p>Best regards,</p>"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_cancel_state
msgid " # No of Cancelled Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_confirm_state
msgid " # No of Confirmed Registrations"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_draft_state
msgid " # No of Draft Registrations"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "(stats)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "3 Intensive Days"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "5 Intensive Days"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"<em>(Chamber Works reserves the right to cancel, re-name or re-locate<br>the "
"event or change the dates on which it is held.)</em>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid ""
"<i class=\"fa fa-clock-o\"/>\n"
"                                        <b>To</b>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<i>You will be able to create dynamic page interacting with the ORM.</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<i>You will be able to develop a full application with backend and user "
"interface.</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<i>You will be able to develop simple dynamic components in HTML pages.</i>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
#: model_terms:ir.ui.view,arch_db:event.event_registration_report_template_badge
msgid "<i>to</i>"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid ""
"<span attrs=\"{'invisible': [('seats_availability', '=', 'unlimited')]}\" "
"class=\"oe_read_only\">\n"
"                                                to \n"
"                                            </span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<span style=\"text-align: -webkit-center; \">This course is dedicated "
"to developers who need to grasp knowledge of the <strong>business "
"applications development </strong>process. This course is for new developers "
"or for IT professionals eager to learn more about technical aspects.</span>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Conference on Business Apps</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"<strong>Having attended this course, participants should be able to:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_2
#: model_terms:event.event,description:event.event_3
msgid "<strong>Objectives:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "<strong>Our prices include:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Program:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "<strong>Requirements:</strong>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<strong>Where to find us:</strong>"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_active
msgid "Active"
msgstr "Actif"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Advanced JQuery"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
msgid "After each subscription"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
msgid "After the event"
msgstr ""

#. module: event
#: selection:event.config.settings,module_event_sale:0
msgid "All events are free"
msgstr ""

#. module: event
#: selection:event.config.settings,module_website_event_questions:0
msgid "Allow adding extra questions on subscriptions"
msgstr ""

#. module: event
#: selection:event.config.settings,module_event_sale:0
msgid "Allow selling tickets"
msgstr ""

#. module: event
#: selection:event.config.settings,module_website_event_track:0
msgid "Allow tracks, agenda and dedicated menus/website per event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_configuration
msgid "Apply"
msgstr "Applique"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Archived"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Arrays"
msgstr ""

#. module: event
#: selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: selection:report.event.registration,registration_state:0
msgid "Attended"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_date_closed
msgid "Attended Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Attended the Event"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_registration
#: model:ir.model.fields,field_description:event.field_event_mail_registration_registration_id
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Attendee"
msgstr "Participant"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Attendee / Contact"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_name
#: model_terms:ir.ui.view,arch_db:event.event_event_report_template_badge
msgid "Attendee Name"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.act_event_registration_from_event
#: model:ir.actions.act_window,name:event.action_registration
#: model:ir.model.fields,field_description:event.field_event_event_registration_ids
#: model:ir.ui.menu,name:event.menu_action_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Attendees"
msgstr "Participants"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_auto_confirmation
msgid "Auto Confirmation"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_back
msgid "Badge Back"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_front
msgid "Badge Front"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_innerleft
msgid "Badge Inner Left"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_badge_innerright
msgid "Badge Inner Right"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Banner Odoo Image"
msgstr ""

#. module: event
#: selection:event.mail,interval_type:0
msgid "Before the event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Best regards,"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Bootstrap CSS"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Bring your own laptop."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Building a Full Application"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Calling the ORM"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_configuration
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Cancel"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Cancel Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Cancel Registration"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: selection:report.event.registration,event_state:0
#: selection:report.event.registration,registration_state:0
msgid "Cancelled"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_type_id
msgid "Category"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Chamber Works 60, Rosewood Court Detroit, MI 48212 (United States)"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid "Click to add a new event."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:206
#, python-format
msgid "Closing Date cannot be set before Beginning Date."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_company_id
#: model:ir.model.fields,field_description:event.field_event_registration_company_id
#: model:ir.model.fields,field_description:event.field_report_event_registration_company_id
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Company"
msgstr "Société"

#. module: event
#: code:addons/event/models/event.py:402
#, python-format
msgid "Compose Email"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_2
msgid "Conference"
msgstr ""

#. module: event
#: model:event.event,name:event.event_2
msgid "Conference on Business Apps"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_configuration
msgid "Configuration"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_configuration
msgid "Configure Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Confirm"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Confirm Anyway"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Confirm Event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Confirm Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_auto_confirm
msgid "Confirmation not required"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: selection:report.event.registration,event_state:0
#: selection:report.event.registration,registration_state:0
msgid "Confirmed"
msgstr "Confirmé"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Confirmed attendees"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Confirmed events"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_partner_id
msgid "Contact"
msgstr "Contact"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Controllers and Views"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_country_id
msgid "Country"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Course Summary"
msgstr ""

#. module: event
#: model:web.tip,description:event.event_tip_1
msgid ""
"Create and manage registrations for this event, send emails to attendees and "
"print identification badges."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_create_uid
#: model:ir.model.fields,field_description:event.field_event_confirm_create_uid
#: model:ir.model.fields,field_description:event.field_event_event_create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_create_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration_create_uid
#: model:ir.model.fields,field_description:event.field_event_registration_create_uid
#: model:ir.model.fields,field_description:event.field_event_type_create_uid
msgid "Created by"
msgstr "Créé par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_create_date
#: model:ir.model.fields,field_description:event.field_event_confirm_create_date
#: model:ir.model.fields,field_description:event.field_event_event_create_date
#: model:ir.model.fields,field_description:event.field_event_mail_create_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration_create_date
#: model:ir.model.fields,field_description:event.field_event_registration_create_date
#: model:ir.model.fields,field_description:event.field_event_type_create_date
msgid "Created on"
msgstr "Créé le"

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_create_date
msgid "Creation Date"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:379
#, python-format
msgid "Customer"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:381
#, python-format
msgid "Customer Email"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Day 1"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Day 2"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Day 3"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
msgid "Day(s)"
msgstr "Jour(s)"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_default_registration_max
msgid "Default Maximum Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_default_registration_min
msgid "Default Minimum Registration"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Delete"
msgstr "Supprimer"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_description
msgid "Description"
msgstr "Description"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Develop a new module for a particular application."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_display_name
#: model:ir.model.fields,field_description:event.field_event_confirm_display_name
#: model:ir.model.fields,field_description:event.field_event_event_display_name
#: model:ir.model.fields,field_description:event.field_event_mail_display_name
#: model:ir.model.fields,field_description:event.field_event_mail_registration_display_name
#: model:ir.model.fields,field_description:event.field_event_registration_display_name
#: model:ir.model.fields,field_description:event.field_event_type_display_name
#: model:ir.model.fields,field_description:event.field_report_event_registration_display_name
msgid "Display Name"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_sequence
msgid "Display order"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Dive into Strings"
msgstr ""

#. module: event
#: selection:event.event,state:0
#: selection:report.event.registration,event_state:0
msgid "Done"
msgstr ""

#. module: event
#: selection:report.event.registration,event_state:0
#: selection:report.event.registration,registration_state:0
msgid "Draft"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid ""
"During this conference, our team will give a detailed overview of our "
"business applications. You’ll know all the benefits of using it."
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_email
msgid "Email"
msgstr "Email"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Email Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_group_email_scheduling
msgid "Email Scheduling"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_template_id
msgid "Email to Send"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_end
msgid "End Date"
msgstr "Date de fin"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_end_located
msgid "End Date Located"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_event
#: model:ir.model.fields,field_description:event.field_event_mail_event_id
#: model:ir.model.fields,field_description:event.field_event_registration_event_id
#: model:ir.model.fields,field_description:event.field_report_event_registration_event_id
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event"
msgstr "Evènement"

#. module: event
#: model:ir.actions.report.xml,name:event.report_event_event_badge
msgid "Event Badge"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event Beginning Date"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_confirm
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid "Event Confirmation"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_event_date
msgid "Event Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_event_end_date
msgid "Event End Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Event Information"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_logo
msgid "Event Logo"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Event Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_tree
msgid "Event Mail Schedulers"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event Month"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_name
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Event Name"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_calendar
msgid "Event Organization"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_calendar
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Event Registration"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_user_id
msgid "Event Responsible"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_event_begin_date
msgid "Event Start Date"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_event_state
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event State"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_type
#: model:ir.model.fields,field_description:event.field_event_type_name
#: model:ir.model.fields,field_description:event.field_report_event_registration_event_type_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_type_form
#: model_terms:ir.ui.view,arch_db:event.view_event_type_tree
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event Type"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.report_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.report_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Event on Registration"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_view
#: model:ir.ui.menu,name:event.event_main_menu
#: model:ir.ui.menu,name:event.menu_event_event
#: model:ir.ui.menu,name:event.menu_report_event_registration
#: model_terms:ir.ui.view,arch_db:event.view_event_configuration
#: model_terms:ir.ui.view,arch_db:event.view_event_form
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_event_tree
msgid "Events"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_report_event_registration
msgid "Events Analysis"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_mail
msgid "Events Mail Schedulers"
msgstr ""

#. module: event
#: model:ir.actions.act_window,name:event.action_event_type
msgid "Events Types"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Events in New state"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Events which are in New state"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Events which are in confirm state"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_1
msgid "Exhibition"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Expected"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_kanban
msgid "Expected attendees"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Extended Filters..."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Finish Event"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "For any additional information, please contact us at"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event_seats_max
msgid ""
"For each event you can define a maximum registration of seats(number of "
"attendees), above this numbers the registrations are not accepted."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event_seats_min
msgid ""
"For each event you can define a minimum reserved seats (number of "
"attendees), if it does not reach the mentioned registrations the event can "
"not be confirmed (keep 0 to ignore this rule)"
msgstr ""

#. module: event
#: model:event.event,name:event.event_1
msgid "Functional Webinar"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Functional flow of the main applications;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Functions"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Group By"
msgstr "Grouper par"

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Having attended this conference, participants should be able to:"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Hello World"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
msgid "Hour(s)"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "How to Debug"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_id
#: model:ir.model.fields,field_description:event.field_event_confirm_id
#: model:ir.model.fields,field_description:event.field_event_event_id
#: model:ir.model.fields,field_description:event.field_event_mail_id
#: model:ir.model.fields,field_description:event.field_event_mail_registration_id
#: model:ir.model.fields,field_description:event.field_event_registration_id
#: model:ir.model.fields,field_description:event.field_event_type_id
#: model:ir.model.fields,field_description:event.field_report_event_registration_id
msgid "ID"
msgstr "ID"

#. module: event
#: model:ir.model.fields,help:event.field_event_event_state
msgid ""
"If event is created, the status is 'Draft'. If event is confirmed for the "
"particular dates the status is set to 'Confirmed'. If the event is over, the "
"status is set to 'Done'. If event is cancelled the status is set to "
"'Cancelled'."
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
msgid "Immediately"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Install and administer your own server;"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_config_settings_module_event_sale
msgid "Install the event_sale module"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_config_settings_module_website_event_track
msgid "Install the module website_event_track"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_config_settings_module_website_event_questions
msgid "Install the website_event_questions module"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Integrated Help"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_nbr
msgid "Interval"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Introduction to JQuery"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Introduction to Javascript"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Introduction to QWeb"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Introduction, CRM, Sales Management"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_default_registration_max
msgid "It will select this default maximum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_type_default_registration_min
msgid "It will select this default minimum value when you choose this event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_color
msgid "Kanban Color Index"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings___last_update
#: model:ir.model.fields,field_description:event.field_event_confirm___last_update
#: model:ir.model.fields,field_description:event.field_event_event___last_update
#: model:ir.model.fields,field_description:event.field_event_mail___last_update
#: model:ir.model.fields,field_description:event.field_event_mail_registration___last_update
#: model:ir.model.fields,field_description:event.field_event_registration___last_update
#: model:ir.model.fields,field_description:event.field_event_type___last_update
#: model:ir.model.fields,field_description:event.field_report_event_registration___last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_write_uid
#: model:ir.model.fields,field_description:event.field_event_confirm_write_uid
#: model:ir.model.fields,field_description:event.field_event_event_write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_registration_write_uid
#: model:ir.model.fields,field_description:event.field_event_mail_write_uid
#: model:ir.model.fields,field_description:event.field_event_registration_write_uid
#: model:ir.model.fields,field_description:event.field_event_type_write_uid
msgid "Last Updated by"
msgstr "Derniere fois mis à jour par"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_write_date
#: model:ir.model.fields,field_description:event.field_event_confirm_write_date
#: model:ir.model.fields,field_description:event.field_event_event_write_date
#: model:ir.model.fields,field_description:event.field_event_mail_registration_write_date
#: model:ir.model.fields,field_description:event.field_event_mail_write_date
#: model:ir.model.fields,field_description:event.field_event_registration_write_date
#: model:ir.model.fields,field_description:event.field_event_type_write_date
msgid "Last Updated on"
msgstr "Dernière mis à jour le"

#. module: event
#: selection:event.event,seats_availability:0
msgid "Limited"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_address_id
msgid "Location"
msgstr "Endroit"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Loops"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Luigi Roni, Senior Event Manager"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_event_mail_ids
msgid "Mail Schedule"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_scheduler_id
msgid "Mail Scheduler"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_type
msgid "Mail Schedulers"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_mail_sent
msgid "Mail Sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_mail_sent
msgid "Mail Sent on Event"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_mail_registration_ids
msgid "Mail registration ids"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_manager
msgid "Manager"
msgstr ""

#. module: event
#: selection:event.config.settings,auto_confirmation:0
msgid "Manually confirm every subscription"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_seats_max
msgid "Max Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_availability
#: model:ir.model.fields,field_description:event.field_event_event_seats_available
msgid "Maximum Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_max
msgid "Maximum Attendees Number"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_min
msgid "Minimum Attendees"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Modules"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
msgid "Month(s)"
msgstr "Mois"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "My Events"
msgstr "Mes événements"

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "New"
msgstr "Nouveau"

#. module: event
#: selection:event.config.settings,group_email_scheduling:0
msgid "No automated emails"
msgstr ""

#. module: event
#: selection:event.config.settings,module_website_event_questions:0
msgid "No extra questions on subscriptions"
msgstr ""

#. module: event
#: selection:event.config.settings,module_website_event_track:0
msgid "No mini website per event"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:200
#, python-format
msgid "No more available seats."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:300
#, python-format
msgid "No more seats available for this event."
msgstr ""

#. module: event
#: selection:event.config.settings,auto_confirmation:0
msgid "No validation step on subscription"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_nbevent
msgid "Number of Events"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_expected
msgid "Number of Expected Attendees"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_used
msgid "Number of Participants"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_nbregistration
msgid "Number of Registrations"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Odoo Official Website"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Odoo Web Client"
msgstr ""

#. module: event
#: model_terms:ir.actions.act_window,help:event.action_event_view
msgid ""
"Odoo helps you schedule and efficiently organize your events:\n"
"    track subscriptions and participations, automate the confirmation "
"emails,\n"
"    sell tickets, etc."
msgstr ""

#. module: event
#: model:event.event,name:event.event_0
msgid "Open Days in Los Angeles"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_organizer_id
msgid "Organizer"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Participant"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_name_registration
msgid "Participant / Contact Name"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"Participants are expected to have some knowledge in programming. A basic "
"knowledge of the Python programming is recommended."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid ""
"Participants preferably have a functional knowledge of our software (see "
"Functional Training)."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Partner"
msgstr "Partenaire"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_phone
msgid "Phone"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Point of Sale (POS), Introduction to report customization."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Project management, Human resources, Contract management."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Purchase, Sales &amp; Purchase management, Financial accounting."
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Python Objects"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_registration_origin
msgid ""
"Reference of the document that created the registration, for example a sale "
"order"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Register with this event"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_graph
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_pivot
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_tree
msgid "Registration"
msgstr ""

#. module: event
#: model:ir.actions.report.xml,name:event.report_event_registration_badge
msgid "Registration Badge"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_date_open
msgid "Registration Date"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Day"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration ID"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration Mails"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Registration Month"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_report_event_registration_registration_state
msgid "Registration State"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_report_event_registration_search
msgid "Registration contact"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_mail_form
msgid "Registration mail"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Registrations"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_reminder
msgid "Reminder of event ${object.event_id.name}"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_type_default_reply_to
msgid "Reply To"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_reply_to
msgid "Reply-To Email"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Report Engine"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_reporting_events
msgid "Reports"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Requirements"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_reserved
msgid "Reserved Seats"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_user_id
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Responsible"
msgstr "Responsable"

#. module: event
#: model:res.groups,name:event.group_email_scheduling
msgid "Schedule Emails on Event Subscription"
msgstr ""

#. module: event
#: selection:event.config.settings,group_email_scheduling:0
msgid "Schedule emails to attendees and subscribers"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_scheduled_date
msgid "Scheduled Sent Mail"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_registration_scheduled_date
msgid "Scheduled Time"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_0
msgid "Seminar"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Send by Email"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_done
msgid "Sent"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_form
msgid "Set To Draft"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_registration_form
msgid "Set To Unconfirmed"
msgstr ""

#. module: event
#: model:ir.ui.menu,name:event.menu_event_global_settings
msgid "Settings"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_3
msgid "Show"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_registration_origin
msgid "Source Document"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_begin
msgid "Start Date"
msgstr "Date de début"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_begin_located
msgid "Start Date Located"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Start Month"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_state
#: model:ir.model.fields,field_description:event.field_event_registration_state
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Status"
msgstr "Statut"

#. module: event
#: model:ir.actions.act_window,name:event.act_register_event_partner
msgid "Subscribe"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_module_website_event_questions
msgid "Subscription Survey"
msgstr ""

#. module: event
#: model:event.event,name:event.event_3
msgid "Technical Training"
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_event_reply_to
msgid ""
"The email address of the organizer is likely to be put here, with the effect "
"to be in the 'Reply-To' of the mails sent automatically at event or "
"registrations confirmation. You can also put the email address of your mail "
"gateway if you use one."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_mail_template_id
msgid ""
"This field contains the template of the mail that will be automatically sent"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_module_event_sale
msgid "Tickets"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_date_tz
msgid "Timezone"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "To get more information, visit the"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_config_settings_module_website_event_track
msgid "Tracks and Agenda"
msgstr ""

#. module: event
#: model:event.type,name:event.event_type_4
msgid "Training"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Training Center Module"
msgstr ""

#. module: event
#: selection:event.event,state:0 selection:event.registration,state:0
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Unconfirmed"
msgstr "Non confirmé"

#. module: event
#: model:ir.model.fields,field_description:event.field_event_event_seats_unconfirmed
msgid "Unconfirmed Seat Reservations"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Underscore"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Understand the development concepts and architecture;"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Understand the various modules;"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_unit
msgid "Unit"
msgstr "Unité"

#. module: event
#: selection:event.event,seats_availability:0
msgid "Unlimited"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
#: model_terms:ir.ui.view,arch_db:event.view_registration_search
msgid "Unread Messages"
msgstr "Messages non lus"

#. module: event
#: model:ir.model.fields,help:event.field_event_config_settings_auto_confirmation
msgid ""
"Unselect this option to manually manage draft event and draft subscription"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming"
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_search
msgid "Upcoming events from today"
msgstr ""

#. module: event
#: model:res.groups,name:event.group_event_user
msgid "User"
msgstr "Utilisateur"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Variables &amp; Operators"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "Warehouse management, Manufacturing (MRP) &amp; Sales, Import/Export."
msgstr ""

#. module: event
#: model_terms:ir.ui.view,arch_db:event.view_event_confirm
msgid ""
"Warning: This Event has not reached its Minimum Registration Limit. Are you "
"sure you want to confirm it?"
msgstr ""

#. module: event
#: selection:event.mail,interval_unit:0
msgid "Week(s)"
msgstr "Semaine(s)"

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "What you will learn?"
msgstr ""

#. module: event
#: model:ir.model.fields,field_description:event.field_event_mail_interval_type
msgid "When to Run "
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "Workflows"
msgstr ""

#. module: event
#: code:addons/event/models/event.py:232
#, python-format
msgid ""
"You have already set a registration for this event as 'Attended'. Please "
"reset it to draft if you want to cancel this event."
msgstr ""

#. module: event
#: code:addons/event/models/event.py:357
#, python-format
msgid "You must wait for the starting day of the event to do this action."
msgstr ""

#. module: event
#: model:ir.model.fields,help:event.field_event_config_settings_group_email_scheduling
msgid ""
"You will be able to configure emails, and to schedule them to be "
"automatically sent to the attendees on subscription and/or attendance"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_registration_mail_template_badge
msgid "Your badge for ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,subject:event.event_subscription
msgid "Your subscription at ${object.event_id.name}"
msgstr ""

#. module: event
#: model:mail.template,report_name:event.event_registration_mail_template_badge
msgid "badge_of_${(object.event_id.name or '').replace('/','_')}"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "drinks and lunch;"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_config_settings
msgid "event.config.settings"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_confirm
msgid "event.confirm"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_event_mail
msgid "event.mail"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_2
msgid "<EMAIL>"
msgstr ""

#. module: event
#: model:ir.model,name:event.model_report_event_registration
msgid "report.event.registration"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_3
msgid "to learn <i>business applications development</i>"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
msgid "to learn Odoo Programming"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_1
msgid "to learn technical aspects"
msgstr ""

#. module: event
#: model_terms:event.event,description:event.event_0
#: model_terms:event.event,description:event.event_1
#: model_terms:event.event,description:event.event_3
msgid "training material."
msgstr ""

#~ msgid "Date of the last message posted on the record."
#~ msgstr "Date du dernier message posté sur l'enregistrement."

#~ msgid "Followers"
#~ msgstr "Abonnés"

#~ msgid "If checked new messages require your attention."
#~ msgstr "Si coché, les nouveaux messages requierent votre attention. "

#~ msgid "Last Message Date"
#~ msgstr "Date du dernier message"

#~ msgid "Messages"
#~ msgstr "Messages"

#~ msgid "Messages and communication history"
#~ msgstr "Messages et historique des communications"
