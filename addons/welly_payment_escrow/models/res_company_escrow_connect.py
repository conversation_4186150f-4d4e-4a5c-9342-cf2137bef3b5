from odoo import models, fields


class ResCompany(models.Model):
    _inherit = "res.company"

    welly_payment_escrow_app_id = fields.Char(string='Escrow App ID')
    welly_payment_escrow_ecommerce_id = fields.Char(string='Escrow Ecommerce ID')
    welly_payment_escrow_terminal_id = fields.Char(string='Escrow TID')
    welly_payment_escrow_host = fields.Char(string='Escrow Host')
    welly_payment_escrow_hmac_checksum_secret = fields.Char(string='Escrow CheckSum Secret')
    welly_payment_escrow_ip_ipn = fields.Char(string='Escrow Ip')