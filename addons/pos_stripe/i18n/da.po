# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_stripe
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:22+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Complete the Stripe onboarding for company %s."
msgstr "Udfør Stripe-onboarding for virksomhed %s."

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Do not have access to fetch token from Stripe"
msgstr "Har ikke adgang til at hente token fra Stripe"

#. module: pos_stripe
#: model_terms:ir.ui.view,arch_db:pos_stripe.pos_payment_method_view_form_inherit_pos_stripe
msgid ""
"Don't forget to complete Stripe connect before using this payment method."
msgstr ""
"Glem ikke at fuldføre Stripe Connect, før du bruger denne betalingsmetode."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to discover: %s"
msgstr "Kunne ikke finde: %s "

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Failed to load resource: net::ERR_INTERNET_DISCONNECTED."
msgstr "Kunne ikke indlæse ressource: net::ERR_INTERNET_DISCONNECTED."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "No available Stripe readers."
msgstr "Ingen tilgængelige Stripe-læsere."

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Payment canceled because not reader connected"
msgstr "Betaling annulleret, fordi læseren ikke er tilsluttet"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Point of Sale betalingsmetoder"

#. module: pos_stripe
#: model:ir.model,name:pos_stripe.model_pos_session
msgid "Point of Sale Session"
msgstr "POS session"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Reader disconnected"
msgstr "Læser ikke forbundet"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe"
msgstr "Stribe"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe Error"
msgstr "Stripe Fejl"

#. module: pos_stripe
#: model:ir.model.fields,field_description:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "Stripe Serial Number"
msgstr "Stripe Serienummer"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Stripe payment provider for company %s is missing"
msgstr "Stripe betalingsudbyder for virksomhed %s mangler"

#. module: pos_stripe
#. odoo-javascript
#: code:addons/pos_stripe/static/src/js/payment_stripe.js:0
#, python-format
msgid "Stripe readers %s not listed in your account"
msgstr "Stripe-læsere %s ikke opført på din konto"

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "Terminal %s is already used on payment method %s."
msgstr "Terminal %s er allerede anvendt på betalingsmetode %s."

#. module: pos_stripe
#. odoo-python
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#: code:addons/pos_stripe/models/pos_payment_method.py:0
#, python-format
msgid "There are some issues between us and Stripe, try again later."
msgstr "Der er nogle problemer mellem os og Stripe, prøv igen senere."

#. module: pos_stripe
#: model:ir.model.fields,help:pos_stripe.field_pos_payment_method__stripe_serial_number
msgid "[Serial number of the stripe terminal], for example: WSC513105011295"
msgstr "[Stripe terminalens serienummer], for eksempel: WSC513105011295"
