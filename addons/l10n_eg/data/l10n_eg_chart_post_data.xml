<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="egypt_chart_template_standard" model="account.chart.template">
        <field name="account_journal_suspense_account_id" ref="egy_account_201001"/>
        <field name="property_account_receivable_id" ref="egy_account_102011"/>
        <field name="property_account_payable_id" ref="egy_account_201002"/>
        <field name="property_account_expense_categ_id" ref="egy_account_400028"/>
        <field name="property_account_income_categ_id" ref="egy_account_500001"/>
        <field name="property_account_expense_id" ref="egy_account_400028"/>
        <field name="property_account_income_id" ref="egy_account_500001"/>
        <field name="expense_currency_exchange_account_id" ref="egy_account_400053"/>
        <field name="income_currency_exchange_account_id" ref="egy_account_500011"/>
        <field name="default_pos_receivable_account_id" ref="egy_account_102012"/>
        <field name="default_cash_difference_income_account_id" ref="egy_account_999002"/>
        <field name="default_cash_difference_expense_account_id" ref="egy_account_999001"/>
        <field name="account_journal_payment_credit_account_id" ref="egy_account_105003"/>
        <field name="account_journal_payment_debit_account_id" ref="egy_account_101004"/>
        <field name="property_tax_payable_account_id" ref="egy_account_202003"/>
        <field name="property_tax_receivable_account_id" ref="egy_account_100103"/>
        <field name="account_journal_early_pay_discount_loss_account_id" ref="egy_account_400079"/>
        <field name="account_journal_early_pay_discount_gain_account_id" ref="egy_account_500014"/>
    </record>
</odoo>
