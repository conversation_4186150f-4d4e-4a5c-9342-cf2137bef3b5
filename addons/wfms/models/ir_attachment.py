import logging
import os

import boto3

from odoo import models, api
from odoo.tools import config

_logger = logging.getLogger(__name__)


class Attachment(models.Model):
    _inherit = 'ir.attachment'
    # Biến toàn cục để lưu trữ client S3
    _s3_client = None

    @classmethod
    def _get_s3_client(cls):
        # L<PERSON>y thông tin cấu hình từ odoo.conf
        if cls._s3_client is None:
            try:
                s3_region = config.get('s3_region')
                s3_access_key = config.get('s3_access_key')
                s3_secret_key = config.get('s3_secret_key')
                # Khởi tạo S3 client
                cls._s3_client = boto3.client(
                    's3',
                    region_name=s3_region,
                    aws_access_key_id=s3_access_key,
                    aws_secret_access_key=s3_secret_key,
                )
            except Exception as e:
                _logger.error(f"Error connect from S3: Error: {str(e)}")
                return False
        return cls._s3_client

    def _get_s3_key(self, store_fname):
        # <PERSON><PERSON><PERSON> tên database hiện tại và store_fname từ attachment
        db_name = self.env.cr.dbname
        # Tạo đường dẫn trên S3 với cấu trúc giống filestore
        s3_key = f'{db_name}/{store_fname}'
        return s3_key

    @api.model_create_multi
    def create(self, vals_list):
        # Gọi hàm create của Odoo để lưu file vào filestore
        attachment = super().create(vals_list)
        # chỉ sử dụng tính năng kết nối với s3 trên môi trường prod
        s3_client = self._get_s3_client()
        if config.get('production') and s3_client:
            for file in attachment:
                # Nếu file được lưu trữ thành công (có store_fname)
                if file.store_fname:
                    s3_key = self._get_s3_key(file.store_fname)

                    # Đọc file từ filestore và upload lên S3
                    file_path = os.path.join(self.env['ir.attachment']._filestore(), file.store_fname)
                    with open(file_path, 'rb') as file_data:
                        try:
                            s3_client.put_object(
                                Bucket=config.get('s3_bucket_name'),
                                Key=s3_key,
                                Body=file_data,
                                ContentType=file.mimetype
                            )
                        except Exception as e:
                            _logger.error(f"Error put_object from S3: Error: {str(e)}")
        return attachment

    def unlink(self):

        s3_client = self._get_s3_client()
        bucket_name = config.get('s3_bucket_name')
        # Lặp qua tất cả các attachment để xóa trên S3
        if config.get('production') and s3_client:
            for attachment in self:
                if attachment.store_fname:
                    s3_key = self._get_s3_key(attachment.store_fname)

                    # Kiểm tra và xóa file trên S3
                    try:
                        s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
                    except Exception as e:
                        _logger.error(f"Failed to delete S3 file: {s3_key}, Error: {str(e)}")

        # Gọi phương thức unlink của Odoo để xóa attachment khỏi cơ sở dữ liệu
        return super(Attachment, self).unlink()

    def write(self, vals):
        s3_client = self._get_s3_client()
        bucket_name = config.get('s3_bucket_name')

        if config.get('production') and s3_client:
            # Lặp qua tất cả các attachment để kiểm tra
            for attachment in self:
                old_store_fname = attachment.store_fname

                # Thực hiện ghi dữ liệu (sử dụng super để gọi hàm gốc)
                res = super(Attachment, self).write(vals)

                # Kiểm tra nếu store_fname đã thay đổi
                if old_store_fname != attachment.store_fname:
                    # Nếu có file cũ trên S3, xóa nó
                    if old_store_fname:
                        old_s3_key = self._get_s3_key(old_store_fname)
                        try:
                            s3_client.delete_object(Bucket=bucket_name, Key=old_s3_key)
                        except Exception as e:
                            _logger.error(f"Failed to delete old S3 file: {old_s3_key}, Error: {str(e)}")

                    # Upload file mới lên S3
                    if attachment.store_fname:
                        new_s3_key = self._get_s3_key(attachment.store_fname)

                        # Đọc file từ filestore và upload lên S3
                        file_path = os.path.join(self.env['ir.attachment']._filestore(), attachment.store_fname)
                        with open(file_path, 'rb') as file_data:
                            try:
                                s3_client.put_object(
                                    Bucket=bucket_name,
                                    Key=new_s3_key,  # Đường dẫn mới
                                    Body=file_data,
                                    ContentType=attachment.mimetype
                                )
                            except Exception as e:
                                _logger.error(f"Error put_object from S3: Error: {str(e)}")
                return res
        else:
            return super(Attachment, self).write(vals)

    @api.model
    def _file_read(self, fname):
        assert isinstance(self, Attachment)
        full_path = self._full_path(fname)
        try:
            # Cố gắng đọc file từ filestore
            with open(full_path, 'rb') as f:
                return f.read()
        except (IOError, OSError):
            _logger.info("File not found in filestore, attempting to read from S3: %s", full_path, exc_info=True)
            s3_client = self._get_s3_client()
            if config.get('production') and s3_client:
                # Nếu không tìm thấy file, thử đọc từ S3
                s3_key = self._get_s3_key(fname)  # Tạo đường dẫn tương ứng trên S3

                try:
                    # Lấy file từ S3
                    response = s3_client.get_object(Bucket=config.get('s3_bucket_name'),
                                                    Key=s3_key)
                    return response['Body'].read()
                except Exception as e:
                    _logger.error(f"Error retrieving file from S3: {s3_key}, Error: {str(e)}")

        # Trả về chuỗi byte rỗng nếu không tìm thấy file
        return b''
