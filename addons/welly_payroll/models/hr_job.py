from odoo import api,models,fields, _
from odoo.exceptions import ValidationError


class HrJob(models.Model):
    _inherit = "hr.job"
    
    currency_id = fields.Many2one('res.currency', related="company_id.currency_id")
    department_id = fields.Many2one(tracking=True)
    contract_type_id = fields.Many2one(tracking=True)
    
    sale_target = fields.Monetary("Mục tiêu <PERSON>h <PERSON>ố", currency_field="currency_id", tracking=True)
    
    # Cấu hình hoa hồng dạy
    pt_commission_type = fields.Selection(
        string="Loại chỉ tiêu hoa hồng dạy",
        selection=[("kpi", "Doanh số"), ("booking", "Số buổi dạy")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    pt_commission_data_type = fields.Selection(
        string="Kiểu dữ liệu hoa hồng dạy",
        selection=[("percent", "Tỷ Lệ %"), ("revenue", "Số Tiền")],
        tracking=True,
        copy=True
    )

    hr_job_pt_commission_line_ids = fields.One2many(
        string="Cơ chế hoa hồng dạy",
        comodel_name='hr.job.pt.commission',
        inverse_name='hr_job_id')

    # Cấu hình hoa hồng bán
    sale_commission_type = fields.Selection(
        string="Loại chỉ tiêu hoa hồng bán",
        selection=[("kpi", "Doanh số")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    sale_commission_data_type = fields.Selection(
        string="Kiểu dữ liệu hoa hồng bán",
        selection=[("percent", "Tỷ Lệ %"), ("revenue", "Số Tiền")],
        required=True,
        tracking=True,
        copy=True
    )

    hr_job_sale_commission_line_ids = fields.One2many(
        string="Cơ chế hoa hồng bán",
        comodel_name='hr.job.sale.commission',
        inverse_name='hr_job_id')

    # Cấu hình hệ số lương K
    k_coefficient_type = fields.Selection(
        string="Loại chỉ tiêu hệ số lương",
        selection=[("kpi", "Doanh số"), ("booking", "Số giờ dạy"), ("constant", "Hằng số")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    # Check các dòng cơ chế hoa hồng bán phải liên tục và không trùng lặp
    @api.constrains('hr_job_sale_commission_line_ids')
    def _check_hr_job_sale_commission_line_ids(self):
        for job in self:
            if job.hr_job_sale_commission_line_ids:
                # Sử dụng mixin để validate
                mixin = self.env['commission.range.mixin']
                mixin.validate_commission_ranges(
                    job.hr_job_sale_commission_line_ids,
                    "Cơ chế hoa hồng bán: "
                )

                # Tự động sắp xếp theo begin_value
                mixin.auto_sort_by_begin_value(job.hr_job_sale_commission_line_ids)

    # Check các dòng cơ chế hoa hồng dạy phải liên tục và không trùng lặp
    @api.constrains('hr_job_pt_commission_line_ids')
    def _check_hr_job_pt_commission_line_ids(self):
        for job in self:
            if job.hr_job_pt_commission_line_ids:
                # Sử dụng mixin để validate
                mixin = self.env['commission.range.mixin']
                mixin.validate_commission_ranges(
                    job.hr_job_pt_commission_line_ids,
                    "Cơ chế hoa hồng dạy: "
                )

                # Tự động sắp xếp theo begin_value
                mixin.auto_sort_by_begin_value(job.hr_job_pt_commission_line_ids)
