import base64
import json
import locale
import random
from datetime import datetime, timezone, timedelta

import jwt
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization

from odoo import http, fields, api
from odoo.http import request


# JWT authen
from odoo.sql_db import db_connect
from odoo.tools import config


def verify_token(token, is_update_welly_id: bool):
    try:
        SECRET_KEY = request.env['ir.config_parameter'].sudo().get_param('jwt.secret')
        # G<PERSON><PERSON><PERSON> mã chuỗi khóa công khai từ base64
        public_key_bytes = base64.b64decode(SECRET_KEY)

        # Tạo đối tượng PublicKey từ chuỗi khóa công khai
        public_key = serialization.load_der_public_key(
            public_key_bytes,
            backend=default_backend()
        )

        # Xác thực JWT
        payload = jwt.decode(
            token,
            public_key,
            algorithms=["RS256"],
            options={"verify_aud": False}
        )
        welly_id = payload.get("sub")
        partner_phone = payload.get("preferred_username")
        # lấy sdt từ jwt có dạng "+84xxx" về "0xxx"
        if partner_phone and partner_phone.startswith("+84"):
            partner_phone = '0' + partner_phone[3:]

        # nếu là lần đầu đăng nhập thì cập nhật welly_id vào db không thì lấy ra và so sánh
        if is_update_welly_id:
            partner = request.env['res.partner'].sudo().search([
                ('phone', '=', partner_phone)
            ])
            partner.write({'welly_id': welly_id})
        else:
            partner = request.env['res.partner'].sudo().search([
                ('welly_id', '=', welly_id),
                ('phone', '=', partner_phone)
            ])
        if not partner:
            return None, "Partner not exits"
        return partner.id, None  # Token hợp lệ, trả về partner_id

    except jwt.ExpiredSignatureError:
        return None, "Token expired"
    except jwt.InvalidTokenError:
        return None, "Invalid token"
    except Exception as e:
        return None, str(e)


def get_and_verify_jwt_from_header(is_update_welly_id: bool):
    # Lấy header Authorization
    auth_header = request.httprequest.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header[7:]  # Tách Bearer để lấy token
    else:
        return None, "Token is missing or invalid"

    # Xác thực JWT
    partner_id, error = verify_token(token, is_update_welly_id)
    return partner_id, error


class BaseResponse:

    @staticmethod
    def success(data=None, message="SUCCESS", index=None):
        response = {
            "errorCode": 0,
            "message": message,
            "data": data
        }

        if index:
            response["index"] = index
        ex = json.dumps(response)
        response = request.make_response(ex, status=200)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response

    @staticmethod
    def error(error_code, message="Error", data=None):
        request.env.cr.rollback()

        data = {
            "errorCode": error_code,
            "message": message,
            "data": data
        }
        response = None
        if error_code == 1 or error_code == 400 or error_code == 404:
            response = request.make_response(json.dumps(data), status=200)
        else:
            response = request.make_response(json.dumps(data), status=error_code)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response

    @staticmethod
    def successWithJson(data=None, message="SUCCESS", index=None):
        response = {
            "errorCode": 0,
            "message": message,
            "data": data
        }

        if index:
            response["index"] = index
        ex = json.dumps(response)
        response = request.make_response(ex, status=200)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response

    @staticmethod
    def errorWithJson(error_code, message="Error", data=None):
        rs = {
            "errorCode": error_code,
            "message": message,
            "data": data
        }
        response = None
        if error_code == 1 or error_code == 400 or error_code == 404:
            response = request.make_response(json.dumps(rs), status=200)
        else:
            response = request.make_response(json.dumps(data), status=error_code)
        response.headers['Content-Type'] = 'application/json'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'POST, GET, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Origin, X-Requested-With, Content-Type, Accept'
        return response


class BaseController(http.Controller):

    @http.route('/api/getListLocation', type='http', auth='public', methods=['POST'], csrf=False)
    def get_location_list(self, **kwargs):
        try:
            # Lấy danh sách tất cả các database
            db = config.get('use_landing_booking') if config.get('use_landing_booking') else None
            if not db:
                return BaseResponse.errorWithJson(error_code=1, message='Chưa cấu hình use_landing_booking')
            db_list = db.split(',')
            result = []
            for db_name in db_list:
                try:
                    # Kết nối tới database tương ứng
                    conn = db_connect(db_name)
                    with conn.cursor() as cr:
                        # Thiết lập environment cho database hiện tại
                        env = api.Environment(cr, 1, {})
                        location = env['welly.location'].sudo().search([], limit=1)
                        base_url = env['ir.config_parameter'].sudo().get_param('mywelly_host')
                        if location:
                            result.append({
                                'id': location.id,
                                'base_url': base_url,
                                'name': location.name,
                                'company_id': location.company_id.id,
                                'company_name': location.company_id.name,
                            })
                except Exception as e:
                    return BaseResponse.errorWithJson(error_code=1, message=str(e))
            return BaseResponse.successWithJson(data=result)
        except Exception as e:
            return BaseResponse.errorWithJson(error_code=1, message=str(e))

    # api cho phép khách hàng đăng ký trải nghiệm, HT tạo lead và đặt lịch hẹn tự động từ landing page
    @http.route('/api/autoCreateLead', type='http', auth='public', methods=['POST'], csrf=False)
    def auto_create_lead(self, **kwargs):
        try:
            # Lấy các thông số truyền vao
            data = json.loads(request.httprequest.data)
            # Tên của Lead
            name = data.get('name')
            #  Họ và tên
            partner_name = data.get('partner_name')
            # Số điện thoại
            phone = data.get('phone')
            # Địa điểm lấy từ /api/book_pt/getListLocation
            location_id = data.get('location_id')
            # Dịch vụ lấy từ /api/getListServiceType
            service_id = data.get('service_id')
            # Thời gian
            start_time = data.get('start_time')
            stop_time = data.get('stop_time')

            # Tin nhắn
            note = data.get('note')
            affiliate_code = data.get('affiliate_code')

            if not name:
                return BaseResponse.error(error_code=1, message="Lead name required")
            if not partner_name:
                return BaseResponse.error(error_code=1, message="partner_name required")
            if not phone:
                return BaseResponse.error(error_code=1, message="phone required")
            if not location_id:
                return BaseResponse.error(error_code=1, message="Club location required")
            if not service_id:
                return BaseResponse.error(error_code=1, message="Type of Service required")
            if not start_time:
                return BaseResponse.error(error_code=1, message="Booking slot start time required")
            if not stop_time:
                return BaseResponse.error(error_code=1, message="Booking slot stop time required")

            # chuyển về định dạng thời gian
            start_time_utc = fields.Datetime.to_string(self.convert_timestamp_milliseconds_to_utc(start_time))
            stop_time_utc = fields.Datetime.to_string(self.convert_timestamp_milliseconds_to_utc(stop_time))

            # Lấy đối tượng 'crm.lead' từ môi trường Odoo
            lead_model = request.env['crm.lead'].sudo()

            # Lấy ID của nguồn đầu được thiết lập là Online Booking Source danh sách nguồn
            source_id = request.env['utm.source'].search([('online_booking_source', '=', True)], limit=1)

            if not source_id:
                # lấy bản ghi đầu tiên nếu không có record nào được chỉ định
                source_records = request.env['utm.source'].search([])
                source_id = source_records[0].id if source_records else False
                team_id = source_records.crm_team_id.id
            else:
                team_id = source_id.crm_team_id.id

            # Tìm đối tác có số điện thoại di động trùng khớp với số phone được truyền vào
            partner = request.env['res.partner'].sudo().search([('phone', '=', phone)], limit=1)
            location = request.env['welly.location'].sudo().search([('id', '=', location_id)])
            # Nếu đối tác không tồn tại, tạo một đối tác mới
            if not partner:
                partner = request.env['res.partner'].sudo().create({
                    'name': partner_name,
                    'phone': phone,
                })

            # Lấy ID của đối tác
            partner_id = partner.id

            # nếu đã tồn tại ticket của ngày hôm nay thì không tạo mới nữa
            today_start = datetime.combine(datetime.today(), datetime.min.time())
            today_end = datetime.combine(datetime.today(), datetime.max.time())

            lead_exits = lead_model.search([
                ('partner_id', '=', partner_id),
                ('is_deleted', '=', False),
                ('service_id', '=', service_id),
                ('company_id', '=', location.company_id.id),
                ('create_date', '>=', today_start),
                ('create_date', '<=', today_end)
            ])
            if lead_exits:
                return BaseResponse.success(data=[])

            # Dữ liệu để tạo crm.lead mới
            lead_data = {
                'name': name,
                'phone': phone,
                'source_id': source_id.id,
                'team_id': team_id,
                'service_id': service_id,
                'partner_name': partner_name,
                'partner_id': partner_id,
                'company_id': location.company_id.id
            }
            if affiliate_code:
                lead_data['description'] = f'Mã người giới thiệu: <strong>{affiliate_code}</strong>'
            # Tạo crm.lead mới và lấy ID
            lead = lead_model.create(lead_data)
            lead_id = lead.id

            # lấy sale chịu trách nhiệm theo lịch chia turn
            sale_id = lead.user_id.partner_id.id
            # Kiểm tra xem sale_id đã được chỉ định chưa
            if not sale_id:
                # Nếu sale_id chưa được chỉ định, thì hệ thống sẽ tự động chỉ định 1 nhân viên sale trong Team để gán vào calendar
                sales = request.env['crm.team'].sudo().browse(int(team_id)).member_ids
                random_user = random.choice(sales)
                if random_user:
                    # Gán Sale PartnerID vào danh sách Partner của lịch hẹn
                    sale_id = random_user.partner_id.id

            # lấy PT chịu trách nhiệm theo lịch chia turn
            pt_id = lead.user_pt_id.partner_id.id

            # Tạo lịch hẹn cho KH, Sale và PT nếu có
            partner_ids_to_add = [(4, lead.partner_id.id)]
            if pt_id:
                partner_ids_to_add.append((4, pt_id))

            event_vals = {
                'name': f'Đăng ký trải nghiệm: {partner.name}',
                'res_model': 'crm.lead',
                'res_model_id': request.env['ir.model']._get('crm.lead').id,
                'res_id': lead_id,
                'partner_ids': partner_ids_to_add,
                #                'partner_ids': [(4, partner_id), (4, sale_id)] + [4, pt_id] if pt_id else [],
                'start': start_time_utc,
                'stop': stop_time_utc,
                'description': note,
                'event_type': 'calendar',
                'location_id': location_id,
                'company_id': location.company_id.id
            }

            event = request.env['calendar.event'].sudo().create(event_vals)

            # Update CRM lead to ensure it reflects the association with the calendar event
            crm_lead = lead_model.browse(lead_id)
            crm_lead.write({
                'calendar_event_ids': [(4, event.id)]
                # Assuming 'calendar_event_id' is a Many2one field in CRM lead to link with calendar event
            })

            # Thêm vào danh sách lịch hẹn của Sale
            if sale_id:
                event.write({
                    'partner_ids': [(4, int(sale_id))]
                })

            # Thêm vào danh sách lịch hẹn của PT
            if pt_id:
                event.write({
                    'partner_ids': [(4, int(pt_id))]
                })

            # Tạo thông điệp liên quan đến cuộc họp trong Action Log
            message_vals = {
                'model': 'crm.lead',
                'res_id': lead.id,  # ID của bản ghi lead tương ứng
                'message_type': 'comment',  # Loại message, 'comment' cho các ghi chú nội bộ
                'body': f'Cuộc họp dự kiến lúc {event.start + timedelta(hours=7)} và kết thúc lúc {event.stop + timedelta(hours=7)}<br>Chủ đề: khách hàng {partner.name} đăng ký trải nghiệm',
                # Nội dung của message, bạn có thể định dạng HTML
                # Thêm các thông tin khác cần thiết cho message
            }

            # Tạo message trong Action Log
            message = request.env['mail.message'].sudo().create(message_vals)

            # gửi thông báo trên giao diện cho admin_online
            self.notify_user(model=lead, partner_name=partner.name, title="Khách hàng đăng ký trải nghiệm")

            self.action_create_mail_activity(model=lead, summary="Khách hàng đăng ký trải nghiệm",
                                             content=f"{partner.name}")

            return BaseResponse.success(data=lead_id)
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.error(error_code=1, message=str(e))

    # api cho phép khách hàng request cskh để tư vấn, điều kiện nếu khách đã có crm trong ngày hôm nay thì ko cho tạo nữa.
    @http.route('/api/createCrmLead', type='http', auth='public', methods=['POST'], csrf=False)
    def create_crm_lead(self, **kwargs):
        try:
            data = json.loads(request.httprequest.data)
            partner_id, error = get_and_verify_jwt_from_header(False)
            if error:
                return BaseResponse.errorWithJson(error_code=1, message=error)
            service_id = data.get('service_id')
            location_id = data.get('location_id')
            if not service_id or not location_id:
                return BaseResponse.errorWithJson(error_code=1, message="service_id, location_id required")
            partner = request.env['res.partner'].sudo().search([('id', '=', partner_id)])
            service = request.env['welly.service.type'].sudo().search([('id', '=', service_id)])
            location = request.env['welly.location'].sudo().search([('id', '=', location_id)])
            if not partner:
                return BaseResponse.errorWithJson(error_code=1, message="partner không tồn tại")
            # Lấy đối tượng 'crm.lead' từ môi trường Odoo
            lead_model = request.env['crm.lead'].sudo()
            umt_source = request.env['utm.source'].sudo().search([('name', '=', 'MyWelly App')], limit=1)

            today_start = datetime.combine(datetime.today(), datetime.min.time())
            today_end = datetime.combine(datetime.today(), datetime.max.time())
            lead_exits = lead_model.search([
                ('partner_id', '=', partner_id),
                ('is_deleted', '=', False),
                ('service_id', '=', service_id),
                ('source_id', '=', umt_source.id),
                ('team_id', '=', umt_source.crm_team_id.id),
                ('company_id', '=', location.company_id.id),
                ('create_date', '>=', today_start),
                ('create_date', '<=', today_end)
            ])

            # nếu đã tồn tại lead với service và source cùng ngày hôm nay rồi thì ko tạo mới
            if not lead_exits:
                # Dữ liệu để tạo crm.lead mới
                lead_data = {
                    'name': f'Tư Vấn {service.name} Từ MYWELLY: {partner.name}',
                    'partner_id': partner_id,
                    'service_id': service_id,
                    'team_id': umt_source.crm_team_id.id,
                    'source_id': umt_source.id,
                    'company_id': location.company_id.id
                }
                lead_model = lead_model.create(lead_data)
                # gửi thông báo trên giao diện cho admin_online
                self.notify_user(model=lead_model, partner_name=partner.name,
                                 title="Khách hàng đăng ký CSKH từ MYWELLY")
                self.action_create_mail_activity(model=lead_model, summary="Khách hàng đăng ký CSKH từ MYWELLY",
                                                 content=f"{partner.name}")
            return BaseResponse.successWithJson(data=[])
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.errorWithJson(error_code=1, message=str(e))

    # api đăng ký tư vấn từ App tọa crm
    @http.route('/api/createCrmLeadWithoutPartner', type='http', auth='public', methods=['POST'], csrf=False)
    def create_crm_lead_without_partner(self, **kwargs):
        try:
            data = json.loads(request.httprequest.data)
            name = data.get('name')
            phone = data.get('phone')
            birthday = data.get('birthday')
            service_id = data.get('service_id')
            location_id = data.get('location_id')
            if not service_id or not location_id or not name or not phone or not birthday:
                return BaseResponse.errorWithJson(error_code=1,
                                                  message="service_id, location_id, name, phone, birthday required")
            birthday = self._convert_date_format(birthday, "%d/%m/%Y")
            partner = request.env['res.partner'].sudo().search([('phone', '=', phone)])
            service = request.env['welly.service.type'].sudo().search([('id', '=', service_id)])
            location = request.env['welly.location'].sudo().search([('id', '=', location_id)])

            # Lấy đối tượng 'crm.lead' từ môi trường Odoo
            lead_model = request.env['crm.lead'].sudo()
            umt_source = request.env['utm.source'].sudo().search([('name', '=', 'MyWelly App')], limit=1)

            # Nếu chưa có thông tin partner thì tạo mới
            if not partner:
                partner = request.env['res.partner'].sudo().create({
                    'name': name,
                    'phone': phone,
                    'birthdate': birthday
                })
                # Dữ liệu để tạo crm.lead mới
                lead_data = {
                    'name': f'Tư Vấn {service.name} Từ MYWELLY: {name}',
                    'partner_id': partner.id,
                    'service_id': service_id,
                    'team_id': umt_source.crm_team_id.id,
                    'source_id': umt_source.id,
                    'company_id': location.company_id.id
                }
                # Tạo ticket cho khách hàng mới
                lead_model = lead_model.create(lead_data)
                # gửi thông báo trên giao diện cho admin_online
                self.notify_user(model=lead_model, partner_name=partner.name,
                                 title="Khách hàng đăng ký trải nghiệm từ MYWELLY")
                self.action_create_mail_activity(model=lead_model, summary="Khách hàng đăng ký CSKH từ MYWELLY",
                                                 content=f"{partner.name}")
                return BaseResponse.successWithJson(data=[])
            # Nếu đã có thông tin partner
            partner_id = partner.id
            # Check xem đã có hóa đơn phát sinh dịch vụ chưa
            # rule check hóa đơn đã phê duyệt: nếu đã có thì báo đăng nhập
            count_approved_moves = request.env['welly.partner.account.move'].sudo().search_count([
                ('partner_id', '=', partner_id),
                ('account_move_id.approval_state', '=', 'approved')
            ])
            if count_approved_moves > 0:
                return BaseResponse.errorWithJson(error_code=1,
                                                  message=str('Tài khoản đã phát sinh dịch vụ. Vui lòng đăng nhập'))
            # Nếu chưa có hóa đơn phát sinh dịch vụ thì đếm xem hôm đó đã tạo ticket tư vấn chưa
            today_start = datetime.combine(datetime.today(), datetime.min.time())
            today_end = datetime.combine(datetime.today(), datetime.max.time())
            number_ticket_from_App_today = lead_model.search_count([
                ('partner_id', '=', partner_id),
                ('is_deleted', '=', False),
                ('source_id', '=', umt_source.id),
                ('create_date', '>=', today_start),
                ('create_date', '<=', today_end)
            ])
            # Nếu đã có ticket trước đó rồi thì k tạo nữa
            if number_ticket_from_App_today > 0:
                return BaseResponse.errorWithJson(error_code=1,
                                                  message="Chăm sóc khách hàng sẽ liên hệ lại cho bạn, vui lòng chờ")
            # Nếu chưa có ticket tư vấn nào trong ngày thì tạo mới
            lead_data = {
                'name': f'Tư Vấn {service.name} Từ MYWELLY: {name}',
                'partner_id': partner_id,
                'service_id': service_id,
                'team_id': umt_source.crm_team_id.id,
                'source_id': umt_source.id,
                'company_id': location.company_id.id
            }
            lead_model = lead_model.create(lead_data)
            # gửi thông báo trên giao diện cho admin_online
            self.notify_user(model=lead_model, partner_name=partner.name,
                             title="Khách hàng đăng ký trải nghiệm từ MYWELLY")
            self.action_create_mail_activity(model=lead_model, summary="Khách hàng đăng ký trải nghiệm từ MYWELLY",
                                             content=f"{partner.name}")
            return BaseResponse.successWithJson(data=[])
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.errorWithJson(error_code=1, message=str(e))

    def _convert_utc_to_timestamp_milliseconds(self, utc_time):
        timestamp_seconds = utc_time.timestamp()
        timestamp_milliseconds = int(timestamp_seconds * 1000)
        return timestamp_milliseconds

    def convert_timestamp_milliseconds_to_utc(self, timestamp_milliseconds):
        timestamp_seconds = timestamp_milliseconds / 1000
        utc_time = datetime.utcfromtimestamp(timestamp_seconds)
        utc_time = utc_time.replace(tzinfo=timezone.utc)
        return utc_time

    def convert_amount_to_vi(self, amount):
        locale.setlocale(locale.LC_ALL, 'vi_VN.UTF-8')
        formatted_amount = locale.currency(amount, grouping=True)
        return formatted_amount

    # ví dụ: input_date: str time; original_format = "%Y-%m-%d"; destination_format= "%d-%m-%Y"
    def _convert_date_format(self, input_date, original_format):
        datetime_object = datetime.strptime(input_date, original_format)
        return datetime_object

    # Hiển thị thông báo cho người dùng trên giao diện
    def notify_user(self, model, partner_name, title):
        partners = request.env['res.users'].sudo().search(
            [('groups_id', 'in', [request.env.ref('welly_base.group_admin_online').id])]).mapped('partner_id')
        # nvkd
        sale_staff = model.user_id.mapped('partner_id')
        # pt_hỗ trợ
        pt_staff = model.user_pt_id.mapped('partner_id')
        # thêm vào thông báo
        partners = list(set(partners).union(sale_staff, pt_staff))

        notifications = []
        action = request.env.ref('crm.crm_lead_action_pipeline')
        for p in partners:
            bus_message = {
                "company_id": model.company_id.id,
                "type": 'default',
                'message': '%s',
                'links': [{
                    'label': f'{partner_name}',
                    'url': f'#action={action.id}&id={model.id}&view_type=form&model=crm.lead'
                }],
                "title": title,
                "sticky": True,
            }
            notifications.append((
                p, 'web.notify', [bus_message]
            ))
        request.env['bus.bus']._sendmany(notifications)

    # Gửi thông báo cho người dùng
    def action_create_mail_activity(self, model, summary, content):
        # gửi thông báo trên giao diện với nhóm Admin Online
        group = request.env.ref('welly_base.group_admin_online', raise_if_not_found=False)
        users = request.env['res.users'].sudo().search([('groups_id', 'in', group.id)])
        # nvkd
        sale_staff = model.user_id
        # pt_hỗ trợ
        pt_staff = model.user_pt_id
        # thêm vào thông báo
        users = list(set(users).union(sale_staff, pt_staff))
        for user in users:
            activity_type = request.env['mail.activity.type'].sudo().search(
                [('icon', 'ilike', 'tasks')])[0]
            res_model_id = request.env['ir.model'].sudo()._get_id('crm.lead')
            request.env['mail.activity'].sudo().create({
                'activity_type_id': activity_type and activity_type.id,
                'summary': summary or activity_type.summary,
                'automated': True,
                'note': content or activity_type.default_note,
                'res_model_id': res_model_id,
                'res_id': model.id,
                'user_id': user.id,
            })
