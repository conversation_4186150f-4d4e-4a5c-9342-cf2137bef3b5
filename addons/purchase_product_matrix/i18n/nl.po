# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Kenmerkwaardes"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Choose Product Variants"
msgstr "Kies productvarianten"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Close"
msgstr "Afsluiten"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Confirm"
msgstr "Bevestigen"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Configuratie wijzigen"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "Matrix"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Matrix productsjabloon"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr "Matrix update"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""
"Indien ingesteld, wordt de matrix van configureerbare producten getoond in "
"het rapport van deze order."

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Is het product configureerbaar?"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr "Variantenmatrix afdrukken"

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "Product"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "Productsjabloon"

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product Variant"
msgstr "Productvariant"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Productkenmerkwaarde dat geen variant aanmaakt"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "Inkooporder"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Inkooporderregel"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "Technisch veld voor product_matrix functies."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""
"Technische opslag van matrix.\n"
"Indien grid_update, wordt dit geladen op de inkooporder.\n"
"Indien niet grid_update stelt het de matrix voor om te openen."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr ""
"Of de matrixweergave een nieuwe matrix bevat om toe te voegen of niet."

#. module: purchase_product_matrix
#. odoo-python
#: code:addons/purchase_product_matrix/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
"Je kunt de hoeveelheid van een product aanwezig in meerdere "
"inkooporderregels niet wijzigen."
