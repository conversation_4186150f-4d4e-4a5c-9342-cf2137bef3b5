# -*- coding: utf-8 -*-
# from odoo import http


# class WellyWorkEntry(http.Controller):
#     @http.route('/welly_work_entry/welly_work_entry', auth='public')
#     def index(self, **kw):
#         return "Hello, world"

#     @http.route('/welly_work_entry/welly_work_entry/objects', auth='public')
#     def list(self, **kw):
#         return http.request.render('welly_work_entry.listing', {
#             'root': '/welly_work_entry/welly_work_entry',
#             'objects': http.request.env['welly_work_entry.welly_work_entry'].search([]),
#         })

#     @http.route('/welly_work_entry/welly_work_entry/objects/<model("welly_work_entry.welly_work_entry"):obj>', auth='public')
#     def object(self, obj, **kw):
#         return http.request.render('welly_work_entry.object', {
#             'object': obj
#         })
