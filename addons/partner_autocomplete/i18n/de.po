# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "Zusätzliche Infos"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Buy more credits"
msgstr "Mehr Guthaben kaufen"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_company.py:0
#, python-format
msgid "Company auto-completed by Odoo Partner Autocomplete Service"
msgstr ""
"Unternehmen automatisch vervollständigt durch Odoo Partner-"
"Autovervollständigung."

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "Unternehmesdatenbank-ID"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr "Anreicherung abgeschlossen"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-Routing"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "IAP Account Token missing"
msgstr "IAP-Kontotoken fehlt"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr "Automatische Vervollständigung der API des IAP-Partners"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "Unzureichendes Guthaben"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "Ist synchronisiert"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync____last_update
msgid "Last Modified on"
msgstr "Letzte Änderung am"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "No account token"
msgstr "Kein Kontotoken"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
#, python-format
msgid "Not enough credits for Partner Autocomplete"
msgstr "Nicht genügend Guthaben für Partner-Autovervollständigung"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "Partner"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
#: model:ir.cron,cron_name:partner_autocomplete.ir_cron_partner_autocomplete
msgid "Partner Autocomplete : Sync with remote DB"
msgstr "Partner-Autovervollständigung: Synchronisation mit der Remote-DB"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "Synchronisation der Partner-Autovervollständigung"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_partner.py:0
#, python-format
msgid "Partner created by Odoo Partner Autocomplete Service"
msgstr "Partner erstellt durch Odoo-Partner-Autovervollständigungsservice"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Placeholder"
msgstr "Platzhalter"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_fieldchar.js:0
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
#, python-format
msgid "Searching Autocomplete..."
msgstr "Autovervollständigung wird gesucht ..."

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
#, python-format
msgid "Set Your Account Token"
msgstr "Bestimmen Sie Ihr Konto-Token"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
#, python-format
msgid "Test mode"
msgstr "Testmodus"
