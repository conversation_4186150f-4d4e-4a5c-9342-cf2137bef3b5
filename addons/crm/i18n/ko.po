# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON><PERSON>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_count
msgid "# Leads/Opps assigned this month"
msgstr "# 이번달 배정된 영업제안/영업기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_count
msgid "# Meetings"
msgstr "# 회의"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__number_of_months
msgid "# Months"
msgstr "# 개월"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "# Opportunities"
msgstr "# 영업 기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_count
msgid "# Overdue Opportunities"
msgstr "# 기한이 초과된 영업 기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_unassigned_count
msgid "# Unassigned Leads"
msgstr "# 미배정 영업제안"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(assigned)s leads allocated among %(team_count)s teams."
msgstr "%(assigned)s 영업제안이 %(team_count)s개 팀으로 배정되었습니다. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(assigned)s leads allocated to %(team_name)s team."
msgstr "%(assigned)s 영업제안이 %(team_name)s 팀에 배정되었습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "%(attach_name)s (from %(lead_name)s)"
msgstr "%(attach_name)s (%(lead_name)s에서)"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "%(duplicates)s duplicates leads have been merged."
msgstr "%(duplicates)s 중복된 영업제안이 통합되었습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"%(members_assigned)s leads assigned among %(member_count)s salespersons."
msgstr "%(members_assigned)s 영업제안이 %(member_count)s명 영업직원들에게 배정되었습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid "%s and %s"
msgstr "%s 및 %s"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "%s's opportunity"
msgstr "%s의 영업 기회"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "<b>Create your first opportunity.</b>"
msgstr "<b>첫번째 영업기회를 만드세요.</b>"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr "판매 업무의 진행 정도에 따라 해당 열에 <b>영업 기회를 끌어다 놓으십시오.</b>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "<b>Predictive Lead Scoring</b>"
msgstr "<b>예측 영업제안 점수</b>"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "<b>Write a few letters</b> to look for a company, or create a new one."
msgstr "<b>몇글자만 입력해서</b> 회사를 검색하거나, 새로운 회사를 생성합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-star\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-star\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"
msgstr ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "<span class=\"bg-danger\">Lost</span>"
msgstr "<span class=\"bg-danger\">실패</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"attrs=\"{'invisible': [('partner_email_update', '=', False)]}\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"attrs=\"{'invisible': [('partner_email_update', '=', False)]}\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" attrs=\"{'invisible': [('partner_phone_update', '=', False)]}\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" attrs=\"{'invisible': [('partner_phone_update', '=', False)]}\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\"> Meetings</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\"> Meeting</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&lt;', 2)]}\"> 미팅</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('calendar_event_count', '&gt;', 1)]}\"> 미팅</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&lt;', 2)]}\">Similar Leads</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&gt;', 1)]}\">Similar Lead</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&lt;', 2)]}\">비슷한 영업제안</span>\n"
"                                    <span class=\"o_stat_text\" attrs=\"{'invisible': [('duplicate_lead_count', '&gt;', 1)]}\">비슷한 영업제안</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
msgid ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', False)]}\">Leads</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', True)]}\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', False)]}\">영업제안</span>\n"
"                        <span class=\"o_stat_text\" attrs=\"{'invisible': [('use_leads', '=', True)]}\">영업기회</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\"> 영업 제안</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> at </span>"
msgstr ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> 다음에서 </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2\"> at </span>"
msgstr "<span class=\"oe_grey p-2\"> at </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey\"> %</span>"
msgstr "<span class=\"oe_grey\"> %</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> (max) </span>"
msgstr "<span class=\"oe_inline\"> (최대) </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> / </span>"
msgstr "<span class=\"oe_inline\"> / </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Expected Revenues:</span>"
msgstr "<span>예상 수익:</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Merged the Lead/Opportunity</span>"
msgstr "</span>영업제안/영업기회 병합</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<span>Periodically assign leads based on rules</span><br/>\n"
"                                    <span attrs=\"{'invisible': [('crm_use_auto_assignment', '=', False)]}\">\n"
"                                        All sales teams will use this setting by default unless\n"
"                                        specified otherwise.\n"
"                                    </span>"
msgstr ""
"<span>규칙에 따라 주기적으로 영업제안을 배정합니다.</span><br/>\n"
"                                    <span attrs=\"{'invisible': [('crm_use_auto_assignment', '=', False)]}\">\n"
"                                        별도 지정하지 않는 한 해당 내용이 전체 영업팀에\n"
"                                        적용됩니다.\n"
"                                    </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>into this one.</span>"
msgstr "<span>이 내용에 해당합니다.</span>"

#. module: crm
#: model:mail.template,body_html:crm.mail_template_demo_crm_lead
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Lead/Opportunity</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Interest in your products</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hi <t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>,<br><br>\n"
"                            Welcome to <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>.\n"
"                            It's great to meet you! Now that you're on board, you'll discover what <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t> has to offer. My name is <t t-out=\"object.user_id.name or ''\">Marc Demo</t> and I'll help you get the most out of Odoo. Could we plan a quick demo soon?<br>\n"
"                            Feel free to reach out at any time!<br><br>\n"
"                            Best,<br>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\">\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">영업제안/영업기회 안내</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">귀사 제품에 관심이 있습니다</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            안녕하세요, <t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>님,<br><br>\n"
"                            <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>에 오신 것을 환영합니다.\n"
"                            만나서 반갑습니다! 함께 하시게 된 것을 환영하오며,  <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>에서 제안해드리는 내용을 확인하시기 바랍니다. 제 이름은 <t t-out=\"object.user_id.name or ''\">Marc Demo</t>이며 Odoo에 대해 자세히 안내해드리겠습니다. 시연으로 보여드리도록 조만간 데모를 예약해드릴까요?<br>\n"
"                            언제든 연락주시기 바랍니다!<br><br>\n"
"                            감사합니다.<br>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br>전화: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\">\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid ""
"A great tip to boost sales efficiency is to always define a next step on "
"each opportunity. To manage ongoing activities, click on any status of the "
"progress bar to filter opportunities based on their next activities' status."
" Click on the grey area of the progress bar to see all opportunities that "
"have no next activity."
msgstr ""
"판매효율을 높일수 있는 최선의 방법은 항상 각각의 영업기회의 다음단계를 정의하는 것입니다. 진행중인 활동을 관리하기 위해, 진행표시줄을 "
"클릭하고 다음 활동의 상태의 영업기회를 필터링하세요. 회색영역을 클릭하면 다음 활동이 없는 모든 영업기회를 볼 수 있습니다. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr "수신 이메일 허용"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__active
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Active"
msgstr "활성"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_tomerge_ids
msgid "Active Leads"
msgstr "영업제안 활성화"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Activities"
msgstr "활동"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activities Analysis"
msgstr "활동 분석"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr "활동 완료 목표"

#. module: crm
#: model:ir.model,name:crm.model_mail_activity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity"
msgstr "활동"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__body
msgid "Activity Description"
msgstr "활동 설명"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr "업무 유형"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_icon
msgid "Activity Type Icon"
msgstr "업무 유형 아이콘"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr "업무 유형"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Activity by"
msgstr "담당자"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Add a description..."
msgstr "설명 추가..."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before the creation of an opportunity"
msgstr "기회를 만들기 전에 자격 단계 추가"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/views/forecast_kanban/forecast_kanban_column_quick_create.js:0
#, python-format
msgid "Add next %s"
msgstr "다음 %s추가"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_domain
msgid ""
"Additional filter domain when fetching unassigned leads to allocate to the "
"team."
msgstr "할당되지 않은 영업제안을 팀에 배정하기 위해 가져오는 경우 사용하는 추가 필터 도메인입니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Address"
msgstr "주소"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Address:"
msgstr "주소:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_tree
msgid "Alias"
msgstr "별칭"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain
msgid "Alias domain"
msgstr "별칭 도메인"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "All set. Let’s <b>Schedule</b> it."
msgstr "모두 설정되었습니다. <b>일정</b>을 만들어 봅시다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Analysis"
msgstr "분석"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr "중복된 항목 삭제"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_recurring_plan_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Archived"
msgstr "보관됨"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should <a name=\"%d\" type=\"action\" tabindex=\"-1\">join a team.</a>"
msgstr ""
"영업팀이 없으므로 <b>첫번째 팀이 기본으로</b> 파이프라인에 표시되도록 합니다. \n"
"                                  CRM을 사용하려면, <a name=\"%d\" type=\"action\" tabindex=\"-1\">팀에 소속</a>되어 있어야 합니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should join a team."
msgstr ""
"영업팀이 없으므로 <b>첫번째 팀이 기본으로</b> 파이프라인에 표시되도록 합니다. \n"
"                                  CRM을 사용하려면, 팀에 소속되어 있어야 합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign Documentation"
msgstr "배정 관련 문서"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assign Leads"
msgstr "리즈 할당"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr "영업 기회 배정"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign salespersons into multiple Sales Teams."
msgstr "다른 영업팀으로 영업직원들을 배정합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr "이런 류의 영업 기회 배정"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr "해당 영업 기회 배정"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assigned Leads Count"
msgstr "할당된 영업제안 수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Assigned To"
msgstr "담당자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignment Date"
msgstr "배정 일자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_domain
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_domain
msgid "Assignment Domain"
msgstr "배정 도메인"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assignment Rules"
msgstr "배정 규칙"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Assignment domain for team %(team)s is incorrectly formatted"
msgstr "%(team)s 팀 배정 도메인 형식이 잘못되었습니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_id
msgid "Associated Lead"
msgstr "관련된 영업제안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_auto_enabled
msgid "Auto Assignment"
msgstr "자동 할당"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_action
msgid "Auto Assignment Action"
msgstr "자동 배정 작업"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Auto Assignment Interval Unit"
msgstr "자동 배정 간격 단위"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_run_datetime
msgid "Auto Assignment Next Execution Date"
msgstr "자동 배정 다음 실행일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__automated_probability
msgid "Automated Probability"
msgstr "영업가능성 자동변경"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_max
msgid "Average Leads Capacity (on 30 days)"
msgstr "평균 영업제안 수용 수치 (30일 기준)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr "블랙리스트"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "블랙리스트 연락처가 휴대폰입니다"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "블랙리스트 연락처가 전화번호입니다"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Boom! Team record for the past 30 days."
msgstr "지난 30 일간의 팀 기록!"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr "반송"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr "CRM"

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "CRM 활동 분석"

#. module: crm
#: model:ir.model,name:crm.model_crm_recurring_plan
msgid "CRM Recurring revenue plans"
msgstr "CRM 반복 수익 계획"

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr "CRM 단계"

#. module: crm
#: model:ir.actions.server,name:crm.ir_cron_crm_lead_assign_ir_actions_server
#: model:ir.cron,cron_name:crm.ir_cron_crm_lead_assign
msgid "CRM: Lead Assignment"
msgstr "CRM: 리드 할당"

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Calendar Event"
msgstr "행사 일정표"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr "데모 요청"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr "캠페인"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Campaign:"
msgstr "캠페인:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "취소"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_tree
msgid "Channel"
msgstr "채널"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr "수신된 요청을 영업 기회로 변환하고 영업 사원에게 배정하기 전에 영업제안으로 필터링하고 한정하려면 이 상자를 선택하십시오."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr "사전 판매 절차를 영업 기회로 관리하려면 이 상자를 선택하십시오."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr "시/군/구"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr "영업제안/영업 기회 범주를 다음과 같이 분류하고 분석하십시오 : 교육, 서비스"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Click on the breadcrumb to go back to your Pipeline. Odoo will save all "
"modifications as you navigate."
msgstr "탐색 경로를 클릭하시면 파이프라인으로 돌아갑니다. Odoo에서는 탐색 과정에서의 모든 수정 사항이 저장됩니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_closed
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr "마감일"

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:0
#, python-format
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr "마감/폐기된 영업제안은 영업 기회로 전환될 수 없습니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_feedback
msgid "Closing Note"
msgstr "마감 참고"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
msgid "Color Index"
msgstr "색상표"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "회사"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Company Name"
msgstr "회사명"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company Name:"
msgstr "회사명:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company:"
msgstr "회사:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completion Date"
msgstr "완료일"

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "구성"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Configure a custom domain"
msgstr "사용자 지정 도메인 구성"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Confirm"
msgstr "승인"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Congrats, best of luck catching such big fish! :)"
msgstr "축하합니다. 앞으로 더욱 큰 행운이 있기를 바랍니다!"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Consider leads created as of the:"
msgstr "다음 일자로 생성된 영업제안 고려:"

#. module: crm
#: model:ir.model,name:crm.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact Details:"
msgstr "연락처 상세 정보:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Contact Information"
msgstr "연락처 정보"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr "담당자 이름"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact:"
msgstr "연락처:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr "변환 작업"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr "변환 날짜"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr "영업제안에서 영업 기회로 변환된 날짜"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr "변환 옵션"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr "영업제안를 영업 기회로 전환 (대량)"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr "영업제안를 영업 기회로 전환 (대량이 아님)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr "영업 기회로 전환"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr "영업 기회로 전환"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr "영업 기회로 전환"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__convert
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__convert
#, python-format
msgid "Convert to opportunity"
msgstr "영업 기회로 전환"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads and perform data enrichment "
"based on their IP address"
msgstr "웹사이트 방문객을 영업기회 및 데이터로 변경하는 것은 방문객의 IP주소에 기반합니다"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__correct
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__correct
msgid "Correct"
msgstr "정답"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/views/crm_kanban/crm_kanban_renderer.js:0
#, python-format
msgid "Count"
msgstr "수"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "이 연락처로 반송된  이메일 수 계산"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr "국가"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_website_crm_iap_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr "웹사이트 트래픽에서 영업제안/기회 창출"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr "영업 기회 작성"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid "Create a Lead"
msgstr "리즈 추가"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Create a Lost Reason"
msgstr "잃어버린 이유"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid "Create a Recurring Plan"
msgstr "반복판매 계획 생성"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr "기회 파이프라인에서 직접 영업제안 발굴 요청을 작성하십시오."

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__create
msgid "Create a new customer"
msgstr "새로운 고객 만들기"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
#, python-format
msgid "Create a new lead"
msgstr "새 영업제안 만들기"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Create an Opportunity"
msgstr "기회 작성"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
#, python-format
msgid "Create an opportunity to start playing with your pipeline."
msgstr "파이프라인을 활용하여 영업제안을 생성하십시오."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr "작성자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr "작성일자"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Created on:"
msgstr "작성일자:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_create_date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr "작성일자"

#. module: crm
#: model:ir.actions.server,name:crm.action_opportunity_forecast
msgid "Crm: Forecast"
msgstr "CRM: 예상"

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr "Crm : 나의 파이프라인"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr "통화"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#, python-format
msgid "Customer"
msgstr "고객"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Customer Email"
msgstr "고객 이메일"

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr "고객"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr "마감일"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__days
msgid "Days"
msgstr "일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr "배정일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr "마감일"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Deadline: %s"
msgstr "마감일: %s"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Define recurring plans and revenues on Opportunities"
msgstr "여기에서 영업기회를 통해 예상되는 반복수익을 정의합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr "삭제"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Description"
msgstr "설명"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Did you know emails sent to"
msgstr "다음 수신자에게 전송된 이메일을 확인하십시오"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Did you know emails sent to a Sales Team alias generate opportunities in "
"your pipeline?"
msgstr "영업팀 별칭으로 전송된 이메일을 통해 파이프라인에 영업기회가 생성된다는 사실을 알고 있으신가요?"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid ""
"Did you know you can search a company by name or VAT number to instantly "
"fill in all its data? Odoo autocompletes everything for you: logo, address, "
"company size, business information, social media accounts, etc."
msgstr ""
"회사를 이름이나 VAT 번호로 검색하여 전체 내용이 바로 입력할 수 있다는 것을 알고 있으셨나요? Odoo에는 로고나 주소, 회사 규모,"
" 사업 정보와 소셜미디어 계정 등 모든 것을 자동완성할 수 있는 기능이 있습니다."

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid ""
"Did you know you can turn a list of opportunities into a map view, using the"
" top-right map icon? A lot of screens in Odoo can be turned into a map: "
"tasks, contacts, delivery orders, etc."
msgstr ""
"오른쪽 상단의 지도 아이콘으로 영업기회 목록을 지도 화면으로 전환할 수 있다는 것을 알고 있으셨나요? 작업, 연락처, 배송요청서 등 "
"Odoo의 수많은 화면을 지도로 바꾸어 볼 수 있습니다."

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr "요약"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr "표시명"

#. module: crm
#. odoo-python
#: code:addons/crm/models/digest.py:0 code:addons/crm/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "사용 권한 없음, 이 데이터는 사용자 요약 이메일에서 무시됩니다"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__nothing
msgid "Do not link to a customer"
msgstr "고객과 연결하지 마십시오"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Documentation"
msgstr "관련 문서"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Domain"
msgstr "도메인"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Drag your opportunity to <b>Won</b> when you get the deal. Congrats !"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Dropdown menu"
msgstr "드롭다운 메뉴"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "E.g. Monthly"
msgstr "예. 월간"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "Easily set expected closing dates and overview your revenue streams."
msgstr "간단하게 예상되는 마감일을 설정하고 수익 흐름을 살펴보세요."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr "편집하기"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Email"
msgstr "이메일"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_state
msgid "Email Quality"
msgstr "이메일 품질"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Email cc"
msgstr "이메일 참조"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email cc:"
msgstr "이메일 참조:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email:"
msgstr "이메일 :"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_type_demo_email_with_template
msgid "Email: Welcome Demo"
msgstr "이메일: 데모에 오신걸 환영합니다"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__auto
msgid "Enrich all leads automatically"
msgstr "모든 영업제안을 자동으로 강화"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr "영업제안을 자동으로 강화"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__manual
msgid "Enrich leads on demand only"
msgstr "수요에 한해 영업제안 강화"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr "그들의 이메일 주소를 기반으로 회사 데이터를 사용하여 영업제안을 자동으로 강화하십시오."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Enrich your leads with company data based on their email addresses"
msgstr "영업제안 자료에 이메일 주소를 기반으로 회사 정보를 추가하십시오."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr "이 단계의 내부 요구 사항을 여기에 입력하십시오 (예 : 고객에게 제공되는 쿠폰). 단계의 이름에 도구팁으로 표시됩니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr "영업 기회를 얻기까지의 예상일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr "마감 예상일"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Expected Closing:"
msgstr "예상 마감일 :"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected MRR"
msgstr "예상되는 MRR"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Expected Revenue"
msgstr "예상 수익"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr "예상 수익"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr "확장 필터"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Info"
msgstr "추가 정보"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Information"
msgstr "추가 정보"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Extra fields..."
msgstr "그외 필드..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__field_id
msgid "Field"
msgstr "필드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__name
msgid "Field Label"
msgstr "필드 라벨"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr "삭제된 전화 번호를 저장하는 데 사용되는 필드입니다. 검색 및 비교 속도를 높이는 데 도움이 됩니다."

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency_field
msgid "Fields that can be used for predictive lead scoring computation"
msgstr "예측 영업제안 점수 계산에 사용할 수 있는  필드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr "파이프라인 접기"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr "후속 견적"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (파트너)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예 : fa-tasks"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid ""
"For a sales team, there is nothing worse than being dry on leads. "
"Fortunately, in just a few clicks, you can generate leads specifically "
"targeted to your needs: company size, industry, etc. To help you test the "
"feature, we offer you 200 credits for free."
msgstr ""
"영업팀에서 영업제안이 고갈되는 것만큼 큰 일도 없습니다. 다행히 클릭 몇 번 만으로 회사 규모나 업계 등 귀하의 조건에 맞는 영업제안을 "
"발굴해낼 수 있습니다. 이 기능을 테스트하실 수 있도록 200 크레딧을 무료로 드립니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid "Force assignment"
msgstr "강제 할당"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_forecast
#: model:ir.ui.menu,name:crm.crm_menu_forecast
msgid "Forecast"
msgstr "예측"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot_forecast
msgid "Forecast Analysis"
msgstr "Forcast 분석"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "From %(source_name)s"
msgstr "%(source_name)s에서"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "From %(source_name)s : %(source_subject)s"
msgstr "%(source_name)s에서: %(source_subject)s"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr "향후 활동"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_mine
msgid "Generate new leads based on their country, industries, size, etc."
msgstr "국가, 산업분야, 크기, 기타 정보에 기반한 새로운 영업기회를 생성합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Generate new leads based on their country, industry, size, etc."
msgstr "국가, 산업분야, 크기, 기타 정보에 기반한 새로운 영업기회를 생성합니다."

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr "실패 원인 가져오기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr "팀이 영업 기회를 이 단계로 옮길 수 있는 요구 사항을 부여하십시오."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0 code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Go, go, go! Congrats for your first deal."
msgstr "아자! 아자! 첫 번째 거래를 축하드립니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "그룹별"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__2
msgid "High"
msgstr "높음"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__hours
msgid "Hours"
msgstr "시간"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr "ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예 : 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid ""
"If checked, forces salesman to be updated on updated opportunities even if "
"already set."
msgstr "선택하면, 이미 설정이 완료되었더라도 영업제안 업데이트 내용에 대해서 영업직원에게 강제 업데이트가 적용됩니다. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_res_partner__team_id
#: model:ir.model.fields,help:crm.field_res_users__team_id
msgid ""
"If set, this Sales Team will be used for sales and assignments related to "
"this partner"
msgstr "설정하면, 이 영업팀을 협력사와 관련한 영업 및 배정 작업에 투입합니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정하면, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "이메일 주소가 블랙리스트에 있는 경우, 해당 연락처는 더 이상 일괄 메일을 수신하지 않습니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"블랙리스트에 있는 전화번호가 없는 번호인 경우, 어느 목록에서도 해당 연락처에 대해서는 더 이상 대량 발송 SMS를 보내지 않게 됩니다."
" "

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr "가져오기 및 동기화"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Import Template for Leads & Opportunities"
msgstr "영업제안와 영업 기회용 서식 가져오기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Inactive"
msgstr "비활성"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
msgid "Include archived"
msgstr "보존 처리 포함"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__incorrect
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__incorrect
msgid "Incorrect"
msgstr "잘못됨"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"해당 핸드폰 번호가 블랙리스트에 오른 번호인지 여부를 나타냅니다. 모델에 핸드폰과 유선전화 필드 양쪽이 있는 경우 블랙리스트에 올라 있는"
" 번호를 구분하는데 사용합니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"해당 전화번호가 블랙리스트에 오른 번호인지 여부를 나타냅니다. 모델에 핸드폰과 유선전화 필드 양쪽이 있는 경우 블랙리스트에 올라 있는 "
"번호를 구분하는데 사용합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Internal Notes"
msgstr "내부 메모"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Interval type between each cron run (e.g. each 2 days or each 2 hours)"
msgstr "크론을 실행하는 간격 유형 (예: 2일마다 또는 2시간마다)"

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid ""
"Invalid repeat frequency. Consider changing frequency type instead of using "
"large numbers."
msgstr "반복 빈도수가 잘못되었습니다. 큰 숫자를 사용하는 것보다는 빈도 유형을 변경하는 것이 좋습니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_partner_visible
msgid "Is Partner Visible"
msgstr "협력사로 표시"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__is_won
msgid "Is Won Stage?"
msgstr "성공으로 표시되는 단계인가요?"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_automated_probability
msgid "Is automated probability?"
msgstr "영업가능성을 자동으로 변경하나요?"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr "직무 영역"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Job Position:"
msgstr "직책 :"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__kanban_state
msgid "Kanban State"
msgstr "칸반 상태"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr "Kpi Crm 영업제안 생성 값"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr "Kpi Crm 영업 기회 성공 값"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_active_count
msgid "Lang Active Count"
msgstr "활성 언어 수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_id
msgid "Language"
msgstr "사용 언어"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Language:"
msgstr "언어:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_action_last
msgid "Last Action"
msgstr "최근 작업"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Last Action:"
msgstr "최근 작업:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field____last_update
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason____last_update
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity____last_update
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan____last_update
#: model:ir.model.fields,field_description:crm.field_crm_stage____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr "최근 단계 업데이트"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr "지연된 활동"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__lead
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__lead
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead"
msgstr "영업제안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_enabled
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_enabled
msgid "Lead Assign"
msgstr "리드 할당"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Lead Assignment requested by %(user_name)s"
msgstr "%(user_name)s에서 요청한 영업제안 배정 "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_max
msgid "Lead Average Capacity"
msgstr "평균 영업제안 수용 능력"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Enrichment"
msgstr "영업제안 강화"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Generation"
msgstr "영업제안 생성"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Mining"
msgstr "영업제안 발굴"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_properties_definition
msgid "Lead Properties"
msgstr "영업제안 속성"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency
msgid "Lead Scoring Frequency"
msgstr "영업제안 점수 갱신 빈도"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr "영업제안 점수 갱신 빈도 필드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr "문자열에서 영업제안 점수 갱신 빈도 필드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr "영업제안 점수 매기기 시작일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr "문자열에 있는 영업제안 점수 매기기 시작 날짜"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_month_count
msgid "Lead assigned to this member those last 30 days"
msgstr "지난 30일 동안 해당 직원에게 할당된 영업제안"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Lead or Opportunity"
msgstr "영업제안 또는 영업기회"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"Lead/Opportunities automatic assignment is limited to managers or "
"administrators"
msgstr "영업제안/영업기회 자동 배정 기능은 매니저나 관리자 권한입니다."

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "영업제안/영업기회"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Lead/Opportunity created"
msgstr "생성된 영업제안/영업기회"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lost_reason.py:0
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#, python-format
msgid "Leads"
msgstr "영업제안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_month_count
msgid "Leads (30 days)"
msgstr "리즈 (30일)"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "영업제안 분석"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""
"영업제안 분석을 통해 처리 지체나 주당 영업제안 수와 같은 다양한 CRM 관련 정보를 확인할 수 있습니다. 다양한 그룹별로 영업제안 "
"분석을 정렬하여 정확한 세분화된 분석을 얻을 수 있습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "Leads Assigned"
msgstr "할당된 리즈"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__leads_count
msgid "Leads Count"
msgstr "영업제안 수"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr "영업제안 생성"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Leads are the qualification step before the creation of an opportunity."
msgstr "영업제안은 영업기회 생성 전 검증 단계입니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_activity
msgid "Leads or Opportunities"
msgstr "영업제안 또는 기회"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#: code:addons/crm/models/crm_team_member.py:0
#, python-format
msgid ""
"Leads team allocation should be done for at least 0.2 or maximum 30 work "
"days, not %.2f."
msgstr "영업제안은 영업일 기준 최소 0.2일에서 최대 30일 동안 배정되어야 합니다. %.2f은 잘못된 값입니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr "나에게 배정된 영업제안"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are not assigned"
msgstr "할당되지 않은 영업제안"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr "선택한 중복 항목이 있는 영업제안입니다. 목록이 비어 있으면 중복 항목이 발견되지 않았음을 의미합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr "중복 항목이 있는 영업제안(정보용)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr "영업제안/영업 기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "영업제안/영업기회 수"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Let's <b>Schedule an Activity.</b>"
msgstr "<b>활동을 계획</b>합시다."

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Let’s have a look at an Opportunity."
msgstr "영업기회를 둘러봅시다."

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__exist
msgid "Link to an existing customer"
msgstr "기존 고객 연결"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"연결된 협력사(선택 사항). 일반적으로 영업제안을 변환할 때 생성됩니다. 협력사는 이름, TIN, 이메일 또는 내부 참조로 찾을 수 "
"있습니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_code
msgid "Locale Code"
msgstr "로케일 코드"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Looks like nothing is planned."
msgstr "아무 일정도 잡혀 있지 않습니다"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid ""
"Looks like nothing is planned. :(<br><br><i>Tip : Schedule activities to "
"keep track of everything you have to do!</i>"
msgstr "아무것도 계획되지 않은것 같습니다. :(<br><br><i>팁 : 해야할 모든 일을 관리하기 위해 활동을 계획합니다!</i>"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Lost"
msgstr "실패"

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_lost.py:0
#, python-format
msgid "Lost Comment"
msgstr "실패 의견"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__lost_count
msgid "Lost Count"
msgstr "실패 수"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr "실패 원인"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Lost Reason:"
msgstr "실패 사유:"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr "실패 원인"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__0
msgid "Low"
msgstr "낮음"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr "견적 만들기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manage Recurring Plans"
msgstr "반복수익계획 관리"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_action
msgid ""
"Manual assign allow to trigger assignment from team form view using an "
"action button. Automatic configures a cron running repeatedly assignment in "
"all teams."
msgstr ""
"수동 배정은 팀 양식 화면에서 작업 버튼을 통해 배정 실행을 할 수 있습니다. 모든 팀에서 반복적으로 배정을 실행하도록 크론을 자동 "
"구성합니다."

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__manual
msgid "Manually"
msgstr "수동"

#. module: crm
#: model:ir.actions.server,name:crm.action_mark_as_lost
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as lost"
msgstr "실패로 표시"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as won"
msgstr "거래성사"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Marketing"
msgstr "마케팅"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Marketing:"
msgstr "마케팅:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__1
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr "중간"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Medium:"
msgstr "매체:"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Meeting scheduled at '%s'<br> Subject: %s <br> Duration: %s"
msgstr ""

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_ids
msgid "Meetings"
msgstr "회의"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team_member.py:0
#, python-format
msgid ""
"Member assignment domain for user %(user)s and team %(team)s is incorrectly "
"formatted"
msgstr "사용자 %(user)s 및 팀 %(team)s의 구성원 배정 도메인의 형식이 잘못되었습니다. "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr "병합"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr "영업제안/영업 기회 병합"

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr "영업 기회 병합"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr "각 파트너의 기존 영업제안/영업 기회와 병합"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__merge
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__merge
msgid "Merge with existing opportunities"
msgstr "기존 영업 기회와 병합"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr "메시지"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__minutes
msgid "Minutes"
msgstr "분"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mobile"
msgstr "모바일"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Mobile:"
msgstr "휴대전화 :"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_monthly
msgid "Monthly"
msgstr "월간"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_max
msgid "Monthly average leads capacity for all salesmen belonging to the team"
msgstr "해당 팀의 전체 영업직원의 월 평균 영업제안 수용량"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__is_membership_multi
msgid "Multi Teams"
msgstr "다중 팀 배정"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_my_activities
#: model:ir.ui.menu,name:crm.crm_lead_menu_my_activities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
msgid "My Activities"
msgstr "내 활동"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "My Deadline"
msgstr "나의 마감일"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr "나의 영업제안"

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr "나의 파이프라인"

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr "신규"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_open_lead_form
msgid "New Lead"
msgstr "새로운 리드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads"
msgstr "새로운 영업제안"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid "New Opportunities"
msgstr "신규 영업 기회"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr "신규 영업기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr "다음 업무 유형"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Next Run"
msgstr "다음 실행"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__green
msgid "Next activity is planned"
msgstr "다음 활동 계획"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__red
msgid "Next activity late"
msgstr "다음 활동 지연"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "No"
msgstr "아니오"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "No Subject"
msgstr "주제 없음"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to %(team_name)s team and its salespersons because no "
"unassigned lead matches its domain."
msgstr "해당 도메인에 맞는 미배정 영업제안이 없으므로 %(team_name)s팀 및 관련 영업직원에게 배정된 영업제안이 없습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to %(team_name)s team because it has no capacity. Add "
"capacity to its salespersons."
msgstr "수용량이 부족하기 때문에 %(team_name)s 팀에 배정된 영업제안이 없습니다. 영업직원 수용량을 추가하십시오."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No allocated leads to any team or salesperson. Check your Sales Teams and "
"Salespersons configuration as well as unassigned leads."
msgstr "팀이나 영업직원에게 배정된 영업제안이 없습니다. 미배정 영업제안과 함께 영업팀 및 영업직원 구성 항목을 확인하십시오."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "No data found!"
msgstr "데이터를 찾을 수 없습니다!"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "No data yet!"
msgstr "아직 정보가 없습니다!"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No lead assigned to salespersons because no unassigned lead matches their "
"domains."
msgstr "해당 도메인에 맞는 미배정 영업제안이 없으므로 영업직원에게 배정된 영업제안이 없습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No new lead allocated to %(team_name)s team because no unassigned lead "
"matches its domain."
msgstr "해당 도메인에 맞는 미배정 영업제안이 없으므로 %(team_name)s 팀에 새로운 영업제안이 배정되지 않았습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#, python-format
msgid ""
"No new lead allocated to the teams because no lead match their domains."
msgstr "해당 도메인에 맞는 영업제안이 없으므로 영업제안이 팀에 배정되지 않았습니다."

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__kanban_state__grey
msgid "No next activity planned"
msgstr "다음 활동 계획 없음"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "No opportunity to display!"
msgstr "기회가 없습니다!"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr "영업 사원 없음"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_normalized
msgid "Normalized Email"
msgstr "정규화된 이메일"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr "재고 부족"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr "메모"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Notes:"
msgstr "참고:"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Now, <b>add your Opportunity</b> to your Pipeline."
msgstr "이제 <b>영업기회</b>를 파이프라인에 추가합니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_number
msgid ""
"Number of interval type between each cron run (e.g. each 2 days or each 4 "
"days)"
msgstr "크론을 실행하는 간격 유형 수 (예: 2일 또는 4일 간격)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_count
msgid "Number of leads and opportunities assigned this last month."
msgstr "지난달 배정된 영업제안과 영업기회의 숫자"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류 메시지 수"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""
"Odoo는 판매 파이프라인을 추적하여 잠재 매출을 추적하고\n"
"                    향후 매출을 보다 정확하게 예측할 수 있도록 도와줍니다."

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid ""
"Odoo's artificial intelligence engine predicts the success rate of each "
"opportunity based on your history. You can always update the success rate "
"manually, but if you let Odoo do the job the score is updated while the "
"opportunity moves forward in your sales cycle."
msgstr ""
"Odoo의 인공지능 엔진은 귀하의 예전 기록을 바탕으로 영업기회별 성공률을 예측합니다. 수기로만 성공률을 업데이트할 수도 있지만, "
"Odoo를 사용하시면 영업 주기에서 영업기회가 진행되는 동안 계속하여 수치를 업데이트합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Open Opportunities"
msgstr "진행중인 영업 기회"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Open Opportunity"
msgstr "진행중인 영업 기회"

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr "윽. 실패 원인"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr "영업 기회"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr "영업 기회 분석"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""
"영업 기회 분석을 사용하면 예상 수익, 계획 비용, 마감 기한 또는 영업 기회 당 상호 작용 수와 같은 정보로 영업 기회에 즉시 접근 할"
" 수 있습니다. 이 보고서는 주로 판매 관리자가 판매 파이프라인의 채널을 정기적으로 검토하는 데 사용됩니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph_forecast
msgid "Opportunities Forecast"
msgstr "기회 예측"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr "영업 기회 수익"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr "영업 기회 성공"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr "나에게 배정된 영업 기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_count
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__opportunity
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunity"
msgstr "영업 기회"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr "영업 기회 작성됨"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr "영업 기회 실패"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_restored
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_restored
msgid "Opportunity Restored"
msgstr "영업기회 복구됨"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr "영업 기회 단계가 변경됨"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr "영업 기회 성공"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr "영업 기회 실패"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_restored
msgid "Opportunity restored"
msgstr "영업기회가 복구됨"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr "영업 기회 성공"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Organization / Contact"
msgstr "조직 / 연락처"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_3_years
msgid "Over 3 years"
msgstr "3년 이상"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_5_years
msgid "Over 5 years "
msgstr "5년 이상"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_team_overdue_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr "연체된 영업 기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_amount
msgid "Overdue Opportunities Revenues"
msgstr "기한이 초과된 건들의 수익"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Overdue Opportunity"
msgstr "기한이 초과된 영업기회"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_user_id
msgid "Owner"
msgstr "소유자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예 : 프로젝트(parent_model) 및"
" 태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_email_update
msgid "Partner Email will Update"
msgstr "협력사 이메일 업데이트 예정"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_phone_update
msgid "Partner Phone will Update"
msgstr "협력사 전화번호 업데이트 예정"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr "협력사가 블랙리스트에 있습니다"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Phone"
msgstr "전화번호"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "전화번호 블랙리스트"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_state
msgid "Phone Quality"
msgstr "전화 품질"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_mobile_search
msgid "Phone/Mobile"
msgstr "전화번호/휴대전화"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Phone:"
msgstr "전화 :"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
#, python-format
msgid "Pipeline"
msgstr "파이프라인"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
msgid "Pipeline Activities"
msgstr "파이프라인 활동"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr "파이프라인 분석"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__name
msgid "Plan Name"
msgstr "계획명"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"Please select more than one element (lead or opportunity) from the list "
"view."
msgstr "목록보기에서 두 개 이상의 요소(영업제안 또는 영업 기회)를 선택하십시오."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_fields
msgid "Pls Fields"
msgstr "PLS 필드"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_start_date
msgid "Pls Start Date"
msgstr "PLS 시작일"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모두 : 모든 사용자가 게시할 수 있습니다\n"
"- 파트너 : 인증 된 파트너만 게시할 수 있습니다\n"
"- 팔로워 : 관련 문서의 팔로워 또는 관련 채널의 참여자 만 게시할 수 있습니다\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_ids
msgid "Potential Duplicate Lead"
msgstr "잠재적 중복 영업제안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_count
msgid "Potential Duplicate Lead Count"
msgstr "잠재적 중복 리드 수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_field_labels
msgid "Predictive Lead Scoring Field Labels"
msgstr "영업제안 예측 점수 필드 라벨"

#. module: crm
#: model:ir.actions.server,name:crm.website_crm_score_cron_ir_actions_server
#: model:ir.cron,cron_name:crm.website_crm_score_cron
msgid "Predictive Lead Scoring: Recompute Automated Probabilities"
msgstr "영업제안 예측 점수: 자동으로 확률 재계산"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr "우선 순위"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Priority:"
msgstr "우선순위 :"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr "확률"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Probability (%)"
msgstr "확률 (%)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Probability:"
msgstr "확률:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lead_properties
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Properties"
msgstr "부동산"

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr "제안"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly_prorated
msgid "Prorated MRR"
msgstr "MRR 비례값"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__prorated_revenue
msgid "Prorated Revenue"
msgstr "비례 배분된 수익"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_forecast
msgid "Prorated Revenues"
msgstr "수익 안분값"

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr "자격 요건 확인"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "Ready to boost your sales? Let's have a look at your <b>Pipeline</b>."
msgstr "판매량을 늘릴 준비가 되셨나요? 이제 <b>파이프라인</b>을 살펴보겠습니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_plan
msgid "Recurring Plan"
msgstr "반복판매 계획"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_recurring_plan_action
#: model:ir.ui.menu,name:crm.crm_recurring_plan_menu_config
msgid "Recurring Plans"
msgstr "반복판매 계획"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Recurring Revenue"
msgstr "반복 수익"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_recurring_revenues
msgid "Recurring Revenues"
msgstr "반복 수익"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr "추천인"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Referred By:"
msgstr "추천인:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
msgid "Related Customer"
msgstr "관련된 고객"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_number
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Repeat every"
msgstr "계속 반복"

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#, python-format
msgid "Repeat frequency should be positive."
msgstr "반복 빈도수는 양수여야 합니다."

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__auto
msgid "Repeatedly"
msgstr "계속"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr "보고"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr "요구 사항"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Reschedule"
msgstr "일정 변경"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Restore"
msgstr "복원"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_use_auto_assignment
msgid "Rule-Based Assignment"
msgstr "배정 규칙"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Running"
msgstr "실행 중"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr "판매"

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model:ir.model.fields,field_description:crm.field_res_partner__team_id
#: model:ir.model.fields,field_description:crm.field_res_users__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr "영업팀"

#. module: crm
#: model:ir.model,name:crm.model_crm_team_member
msgid "Sales Team Member"
msgstr "영업 팀 구성원"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Sales Team Settings"
msgstr "영업팀 설정"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Sales Team:"
msgstr "영업팀:"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr "영업팀"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr "영업사원"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Salesperson:"
msgstr "영업 담당자:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salespersons"
msgstr "영업담당자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized
msgid "Sanitized Number"
msgstr "제거된 번호"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Schedule activities to keep track of everything you have to do."
msgstr "할 일들을 미리 계획하세요"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr "영업제안 검색"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr "영업 기회 검색"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Select Leads/Opportunities"
msgstr "영업제안/영업 기회 선택"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mail_compose
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send email"
msgstr "이메일 전송"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__sequence
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr "순서"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid ""
"Set Recurring Plans on Opportunities to display the contracts' renewal "
"periodicity<br>(e.g: Monthly, Yearly)."
msgstr "영업기회에 반복계획을 설정하여 계약의 갱신주기를 표시합니다<br>(예:월간, 연간)"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr "영업 기회 파이프라인에 새 단계 설정"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr "설정"

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr "영업제안 메뉴 표시"

#. module: crm
#: model:res.groups,name:crm.group_use_recurring_revenues
msgid "Show Recurring Revenues Menu"
msgstr "반복 수익 메뉴 표시"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr "다음 작업 날짜가 오늘 이전인 모든 기회 표시"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only lead"
msgstr "영업제안만 보기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only leads"
msgstr "영업제안만 보기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunities"
msgstr "영업기회만 보기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only opportunity"
msgstr "기회만 보기"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_optout
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_optout
msgid "Skip auto assignment"
msgstr "자동 할당 뛰어넘기"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Snooze 7d"
msgstr "7일후 다시알림"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr "원본"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Source:"
msgstr "소스 :"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr "이 단계를 사용하는 특정 팀. 다른 팀은 이 단계를 보거나 사용할 수 없습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#: code:addons/crm/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Stage"
msgstr "단계"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr "단계 변경됨"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr "단계명"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr "단계 검색"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr "단계 변경됨"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Stage:"
msgstr "단계:"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr "단계"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""
"단계를 통해 영업사원은 영업 기회에서 특정 기회가\n"
"            어떻게 배치되는지 쉽게 추적할 수 있습니다."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "Start scheduling activities on your opportunities"
msgstr "기회 관련 일정 계획을 시작하세요"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "State"
msgstr "시/도"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동에 기조한 상태입니다\n"
"기한초과: 이미 기한이 지났습니다\n"
"오늘: 활동 날짜가 오늘입니다\n"
"계획: 향후 활동입니다."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr "도로명"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street 2..."
msgstr "상세 주소"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street..."
msgstr "읍/면/동 도로명..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr "상세 주소"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Submit"
msgstr "제출"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr "하위 유형"

#. module: crm
#: model:ir.model,name:crm.model_ir_config_parameter
msgid "System Parameter"
msgstr "시스템 매개 변수"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "태그"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "태그"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Tags:"
msgstr "태그:"

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Teams"
msgstr "팀"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_member_config
msgid "Teams Members"
msgstr "팀 구성원"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid ""
"The contacts below have been added as followers of this lead\n"
"                        because they have been contacted less than 30 days ago on"
msgstr ""
"아래 연락처는 연락한 지 30일이 지나지 않았기 때문에\n"
"                          이 영업제안의 팔로워로 추가되었습니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr "이 채널과 연결된 이메일 주소입니다. 새로운 이메일을 수신하면 채널에 배정된 새 영업제안이 자동으로 작성됩니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "이메일 별칭 이름. <<EMAIL>>에 대한 이메일을 수신하도록 하려면 'jobs'라고 적습니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr "영업제안을 영업 기회로 전환하면서 만들어지는 미래의 협력사 이름"

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_recurring_plan_check_number_of_months
msgid "The number of month can't be negative."
msgstr "개월은 음의 값이 될수 없습니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"이 별칭으로 이메일을 받을 때 생성되는 레코드의 소유자. 이 필드를 시스템에 설정하지 않으면 보낸 사람 주소를 기준으로 올바른 소유자를 "
"찾기 위해 시도하거나 주소에서 해당하는 사용자를 찾지 못한 경우 관리자 계정을 사용합니다."

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_check_probability
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr "거래 마감 확률은 0 %에서 100 % 사이여야 합니다!"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "The success rate is computed based on"
msgstr "성공률은 다음을 기준으로 계산됩니다: "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid ""
"The success rate is computed based on the stage, but you can add more fields"
" in the statistical analysis."
msgstr "성공률은 단계를 기반으로 계산되지만 통계 분석에서 더 많은 필드를 추가 할 수 있습니다."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This analysis shows you how many leads have been created per month."
msgstr "이 분석 결과에서 월별로 생성된 영업제안 수를 확인할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "이 막대를 사용하면 예약된 활동을 기준으로 영업 기회를 필터링할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"This can be used to automatically assign leads to sales persons based on "
"rules"
msgstr "규칙에 따라 영업 담당자에게 영업제안을 자동으로 배정하는데 사용할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "This can be used to compute statistical probability to close a lead"
msgstr "영업제안을 마감하기 위한 통계적 확률을 계산하는 데 사용할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "이 메일은 대량메일에서 블랙리스트에 올라있습니다. 블랙리스트를 해제하세요."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "이 필드는 기본 이메일 필드에 이메일 주소 이상을 포함할 수 있으므로 이메일 주소를 검색하는 데 사용됩니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__lang_code
msgid "This field is used to set/get locales for user"
msgstr "이 필드는 사용자에 대한 로컬 설정을 설정하거나 가져 오는 데 사용됩니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"이것은 다른 캠페인 활동을 추적할 때 도움을 주는 이름입니다.  예시 : Fall_Drive, Christmas_Special"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "이것은 배송 방법입니다. 예시 : 엽서, 이메일 또는 배너광고"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "이것은 링크의 소스입니다. 예시 : 검색엔진, 다른 도메인 또는 이메일 목록의 이름"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr "이 전화번호는 SMS 마케팅에서 블랙리스트에 올라있습니다. 블랙리스트를 해제하려면 클릭하세요."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr "이 단계에 표시할 레코드가 없으면 칸반 화면은 접혀 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "This will assign leads to all members. Do you want to proceed?"
msgstr "이렇게 하면 모든 구성원에게 영업제안이 배정됩니다. 진행하시겠습니까?"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_0
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Tip: Convert incoming emails into opportunities"
msgstr "팁: 받은 메일을 영업기회로 전환시킵니다"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_1
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid "Tip: Did you know Odoo has built-in lead mining?"
msgstr "팁: Odoo에 영업제안 발굴 기능이 있다는 사실을 알고 있으셨나요?"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_4
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid "Tip: Do not waste time recording customers' data"
msgstr "도움말: 고객 정보 입력에 시간을 낭비하지 마십시오"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_3
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid "Tip: Manage your pipeline"
msgstr "도움말: 파이프라인 관리"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_2
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid "Tip: Opportunity win rate is predicted with AI"
msgstr "도움말: AI를 활용한 영업기회 성공률 예측"

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_5
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid "Tip: Turn a selection of opportunities into a map"
msgstr "도움말: 다양한 영업기회를 지도 화면으로 변환"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Title"
msgstr "제목"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"To prevent data loss, Leads and Opportunities can only be merged by groups "
"of %(max_length)s."
msgstr "데이터 손실을 막기 위해 영업제안이나 영업기회는 %(max_length)s 그룹으로만 통합할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr "오늘 활동"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr "높은 견적 제시"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Tracking"
msgstr "추적"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Trailing 12 months"
msgstr "향후 12개월"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try sending an email"
msgstr "이메일 보내기 시도"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Type"
msgstr "유형"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
msgid "Type is used to separate Leads and Opportunities"
msgstr "유형은 영업제안과 영업 기회를 분리하는 데 사용됩니다"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Type:"
msgstr "유형 :"

#. module: crm
#: model:ir.model,name:crm.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM 캠페인"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_company_ids
msgid "UX: Limit to lead company or all if no company"
msgstr "UX: 영업제안 대상 회사 또는 회사가 없는 경우 전체로 제한"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr "미지정"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Lead"
msgstr "미배정 영업제안"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Leads"
msgstr "미배정 영업제안"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
msgid "Upcoming Closings"
msgstr "예정된 마감"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_pls_update_action
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Update Probabilities"
msgstr "성공률 업데이트"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_pls_update
msgid "Update the probabilities"
msgstr "성공률 업데이트"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__use_leads
msgid "Use Leads"
msgstr "영업제안 사용"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Use Lost Reasons to report on why opportunities are lost (e.g.\"Undercut by "
"competitors\")."
msgstr "보고서에 영업기회 실패에 대한 실패 사유 기재 (예: 경쟁사의 가격 덤핑)"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__each_exist_or_create
msgid "Use existing partner or create"
msgstr "기존 협력사를 사용하거나 작성"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""
"영업 기회 또는 고객을 만들기 전에 자격 단계가 필요한 경우 \n"
"                         영업제안을 사용 하십시오. \n"
"                         귀하가 받은 명함, 귀하의 웹 사이트에 기입된 연락처 양식, 또는 \n"
"                         귀하가 가져온 부적격의 잠재 고객, 등."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""
"영업 기회 또는 고객을 만들기 전에 자격 증명 단계가 필요한 경우 영업제안을 사용합니다. 받은 명함, 웹 사이트에 기재된 연락처 양식 "
"또는 가져온 부적격 잠재 고객의 파일 등이 될 수 있습니다. 자격을 갖추면 잠재 고객을 비즈니스 기회 및 주소록에서 신규 고객으로 전환할"
" 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Use the <i>New</i> button, or send an email to"
msgstr "<i>신규</i> 버튼을 누르거나, 다음 주소로 이메일을 보내주세요."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid ""
"Use the <i>New</i> button, or send an email to %s to test the email gateway."
msgstr "<i>신규</i> 버튼을 사용하거나 %s로 이메일을 보내 이메일 게이트웨이를 테스트할 수 있습니다. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid ""
"Use the New button, or configure an email alias to test the email gateway."
msgstr "신규 버튼을 누르거나 이메일 별칭을 설정하여 이메일 게이트웨이를 테스트할 수 있습니다."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid "Use this menu to have an overview of your Pipeline."
msgstr "이 메뉴에서 파이프라인 전체보기를 할 수 있습니다."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr "단계를 나열하기 위해 사용됩니다. 낮을수록 좋습니다."

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "User"
msgstr "사용자"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_company_ids
msgid "User Company"
msgstr "사용자 회사"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__value
msgid "Value"
msgstr "가치"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__variable
msgid "Variable"
msgstr "가변"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__3
msgid "Very High"
msgstr "매우 높음"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Visits to Leads"
msgstr "영업제안을 위한 방문"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr "전문인력/기술 없음"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr "웹사이트"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr "연락처의 웹사이트"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Website:"
msgstr "웹사이트:"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__weeks
msgid "Weeks"
msgstr "주"

#. module: crm
#: model:mail.template,name:crm.mail_template_demo_crm_lead
msgid "Welcome Demo"
msgstr "첫 제품 시연"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "What went wrong ?"
msgstr ""

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0 model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#, python-format
msgid "Won"
msgstr "성공"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__won_count
msgid "Won Count"
msgstr "성공한 건수"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr "영업 기회 대상 성공"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Yeah! Deal of the last 7 days for the team."
msgstr "아자! 팀에 대한 지난 7 일간의 거래."

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_yearly
msgid "Yearly"
msgstr "매년"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "Yes"
msgstr "예"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
#, python-format
msgid "You can make your opportunity advance through your pipeline from here."
msgstr "여기에서 파이프라인을 통해 영업기회를 발전시킬 수 있습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You don't have the access needed to run this cron."
msgstr "이 명령을 실행할 권한이 없습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 30 days."
msgstr "지난 30일 동안 개인 신기록을 달성했습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "You just beat your personal record for the past 7 days."
msgstr "지난 7일 동안 개인 신기록을 달성했습니다."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""
"영업 기회를 통해 회의 및 전화 통화를 계획하고, 견적으로 변환하고,\n"
"                    관련 문서를 첨부하고, 모든 업무 협의를 추적하는 등의\n"
"                    작업을 수행할 수 있습니다."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "ZIP"
msgstr "우편번호"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr "우편번호"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "e.g. Negotiation"
msgstr "예. 협상"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. Product Pricing"
msgstr "예 : 제품 가격"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "e.g. Too expensive"
msgstr "예. 너무 비싸다"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. https://www.odoo.com"
msgstr "예 : https://www.odoo.com"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "for the leads created as of the"
msgstr "영업제안 생성 기준일:"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "generate opportunities in your pipeline?<br>"
msgstr "파이프라인에서 영업기회를 발굴합니까?<br>"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr "team_count"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "to test the email gateway."
msgstr "이메일 게이트웨이를 테스트합니다."

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "to your CRM. This email address is configurable by sales team members."
msgstr "CRM에 해당합니다. 영업팀에서 이 이메일을 설정할 수 있습니다."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#, python-format
msgid "unknown"
msgstr "알 수 없음"
