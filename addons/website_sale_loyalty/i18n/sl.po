# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_loyalty
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "A coupon is needed for coupon programs."
msgstr ""

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
#, python-format
msgid "A coupon with the same code was found."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "All websites"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__ecommerce_ok
msgid "Available on Website"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Claim"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Costs"
msgstr "Stroški"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "Could not apply the promo code:"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__coupon_id
msgid "Coupon"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_sale_order__disabled_auto_rewards
msgid "Disabled Auto Rewards"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.reduction_coupon_code
msgid "Discount code or gift card"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discount:"
msgstr "Popust:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discounted amount"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Done"
msgstr "Opravljeno"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Generate Short Link"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.sale_coupon_result
msgid "Gift card or discount code..."
msgstr "Darilna kartica ali koda za popust"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__id
msgid "ID"
msgstr "ID"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Invalid or expired promo code."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share____last_update
msgid "Last Modified on"
msgstr "Zadnjič spremenjeno"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_loyalty
msgid "Loyalty"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Program zvestobe"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.res_config_settings_view_form_inherit_website_sale_loyalty
msgid "Loyalty Programs"
msgstr "Programi zvestobe"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Pay with eWallet"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_id
msgid "Program"
msgstr "Program"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_website_id
msgid "Program Website"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "Promocijska koda"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "Provide either a coupon or a program."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__redirect
msgid "Redirect"
msgstr "Preusmeri"

#. module: website_sale_loyalty
#: model:ir.model.fields,help:website_sale_loyalty.field_coupon_share__program_website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_rule__website_id
msgid "Restrict publishing to this website."
msgstr "Omejitev objav na to spletno stran."

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Prodajni nalog"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Postavka prodajnega naloga"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_card_view_tree_inherit_website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_tree_inherit_website_sale_loyalty
#, python-format
msgid "Share"
msgstr "Souporaba"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__share_link
msgid "Share Link"
msgstr "Deli povezavo"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Share Loyalty Card"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.snippet_options
msgid "Show Discount in Subtotal"
msgstr ""

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/controllers/main.py:0
#, python-format
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "The following promo code was applied on your order:"
msgstr ""

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
#, python-format
msgid "The promo code must be unique."
msgstr ""

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "The shared website should correspond to the website of the program."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_rule__website_id
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "Website"
msgstr "Spletna stran"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have"
msgstr "Imate"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have successfully applied the following code:"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "in your ewallet"
msgstr ""
