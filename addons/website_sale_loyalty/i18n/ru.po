# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_loyalty
# 
# Translators:
# <PERSON> <ye<PERSON><PERSON><PERSON><PERSON>@itpp.dev>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# Константин <PERSON>н <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON>, 2022
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-10-10 10:24+0000\n"
"PO-Revision-Date: 2022-09-22 05:57+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "A coupon is needed for coupon programs."
msgstr "Для участия в купонных программах необходим купон."

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
#, python-format
msgid "A coupon with the same code was found."
msgstr "Был найден купон с таким же кодом."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "All websites"
msgstr "Все сайты"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__ecommerce_ok
msgid "Available on Website"
msgstr "Доступно на сайте"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Claim"
msgstr "Заявка"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Costs"
msgstr "Затраты"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "Could not apply the promo code:"
msgstr "Не удалось применить промокод:"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__coupon_id
msgid "Coupon"
msgstr "Купон"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""
"Создавайте ссылки, которые применяют купон и перенаправляют на определенную "
"страницу"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_uid
msgid "Created by"
msgstr "Создал"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_date
msgid "Created on"
msgstr "Дата создания"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_sale_order__disabled_auto_rewards
msgid "Disabled Auto Rewards"
msgstr "Автонаграды для инвалидов"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr "Скидки и лояльность"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.reduction_coupon_code
msgid "Discount code or gift card"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discount:"
msgstr "Скидка:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discounted amount"
msgstr "сумма скидок"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Done"
msgstr "Выполнено"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Generate Short Link"
msgstr "Создать короткую ссылку"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.sale_coupon_result
msgid "Gift card or discount code..."
msgstr "Подарочная карта или код на скидку..."

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr "Подарочные карты и электронный кошелек"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__id
msgid "ID"
msgstr "Идентификатор"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Invalid or expired promo code."
msgstr "Недействительный или просроченный промокод."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share____last_update
msgid "Last Modified on"
msgstr "Последнее изменение"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Последний раз обновил"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_loyalty
msgid "Loyalty"
msgstr "Лояльность"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Купон лояльности"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Программа Лояльности"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.res_config_settings_view_form_inherit_website_sale_loyalty
msgid "Loyalty Programs"
msgstr "Программы лояльности"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Правило лояльности"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Pay with eWallet"
msgstr "Оплата с помощью электронного кошелька"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_id
msgid "Program"
msgstr "Программа"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_website_id
msgid "Program Website"
msgstr "Сайт программы"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "Промо код"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "Provide either a coupon or a program."
msgstr "Предоставьте либо купон, либо программу."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__redirect
msgid "Redirect"
msgstr "Перенаправление"

#. module: website_sale_loyalty
#: model:ir.model.fields,help:website_sale_loyalty.field_coupon_share__program_website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_rule__website_id
msgid "Restrict publishing to this website."
msgstr "Ограничить публикацию на этом сайте."

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_card_view_tree_inherit_website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_tree_inherit_website_sale_loyalty
#, python-format
msgid "Share"
msgstr "Поделиться"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__share_link
msgid "Share Link"
msgstr "Поделиться ссылкой"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Share Loyalty Card"
msgstr "Поделитесь картой лояльности"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.snippet_options
msgid "Show Discount in Subtotal"
msgstr "Показать скидку в промежуточном итоге"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/controllers/main.py:0
#, python-format
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""
"Купон будет автоматически применен, когда вы добавите товар в корзину."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "The following promo code was applied on your order:"
msgstr "Следующий промокод был применен к вашему заказу:"

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
#, python-format
msgid "The promo code must be unique."
msgstr "Промокод должен быть уникальным."

#. module: website_sale_loyalty
#: code:addons/website_sale_loyalty/wizard/sale_coupon_share.py:0
#, python-format
msgid "The shared website should correspond to the website of the program."
msgstr "Общий сайт должен соответствовать сайту программы."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_rule__website_id
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "Website"
msgstr "Вебсайт"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""
"Вы можете поделиться этой акцией со своими клиентами.\n"
"                            Она будет применена при оформлении заказа, когда покупатель воспользуется этой ссылкой."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have"
msgstr "Вы имеете"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have successfully applied the following code:"
msgstr "Вы успешно применили следующий код:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "in your ewallet"
msgstr "в вашем электронном кошельке"
