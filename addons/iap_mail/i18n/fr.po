# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap_mail
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:53+0000\n"
"Last-Translator: Man<PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Company type</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-building text-primary\"/>\n"
"                    <b>Type de société</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Founded</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-calendar text-primary\"/>\n"
"                    <b>Fondée</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies Used</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-cube text-primary\"/>\n"
"                    <b>Technologies utilisées</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>Email</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-envelope text-primary\"/>\n"
"                    <b>E-mail</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Timezone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-globe text-primary\"/>\n"
"                    <b>Fuseau horaire</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Sectors</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-industry text-primary\"/>\n"
"                    <b>Secteurs</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Estimated revenue</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-money text-primary\"/>\n"
"                    <b>Revenus estimés</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Phone</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-phone text-primary\"/>\n"
"                    <b>Téléphone</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-twitter text-primary\"/>\n"
"                    <b>Twitter</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employees</b>"
msgstr ""
"<i class=\"fa fa-fw me-2 fa-users text-primary\"/>\n"
"                    <b>Employés</b>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "<span> per year</span>"
msgstr "<span> par an</span>"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "Company Logo"
msgstr "Logo de la société"

#. module: iap_mail
#: model:ir.model,name:iap_mail.model_iap_account
msgid "IAP Account"
msgstr "Compte IAP"

#. module: iap_mail
#: model_terms:ir.ui.view,arch_db:iap_mail.enrich_company
msgid "followers"
msgstr "abonnés"
