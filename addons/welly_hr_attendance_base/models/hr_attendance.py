from dateutil.relativedelta import relativedelta

from odoo import Command, fields, models,api,exceptions,_
from datetime import timedelta,timezone,time,datetime   
from odoo.tools import format_datetime


class HrAttendance(models.Model):
    _name = 'hr.attendance'
    _inherit = ['hr.attendance', 'mail.thread', 'mail.activity.mixin']
    company_id = fields.Many2one('res.company', string='Công ty', related="device_id.company_id", store=True)
    device_id = fields.Many2one('device.device', string='Thiết bị')
    check_out = fields.Datetime(string="Check Out", tracking=True)
    check_in = fields.Datetime(string="Check In", tracking=True)
    date = fields.Date(string="Ngày", compute='_compute_date', store=True)

    # SQL Constraints
    _sql_constraints = [
        ('unique_employee_date', 'UNIQUE (employee_id, date)', 'Mỗi nhân viên chỉ được chấm công 1 lần trong ngày')
    ]
    
    
    @api.depends('check_in')
    def _compute_date(self):
        for record in self:
            record.date = record.check_in.astimezone(tz=timezone(timedelta(hours=7))).date()

    # override hàm của odoo để handle trường hợp máy face gửi dữ liệu bất đồng bộ lên
    @api.constrains('check_in', 'check_out')
    def _check_validity_check_in_check_out(self):
        """ nếu thời gian check_in lớn hơn thời gian check_out thì sẽ cập nhật thời gian check_in = check_out tại thời điểm đó """
        for attendance in self:
            if attendance.check_in and attendance.check_out:
                if attendance.check_out < attendance.check_in:
                    attendance.check_in = attendance.check_out

    # Chặn không cho xoá bản ghi chấm công
    def unlink(self):
        raise exceptions.ValidationError(_("Không được xoá bản ghi chấm công"))
    
    @api.model_create_single
    def create(self, values:dict):
        # Thêm date_number vào values theo dạng yyyyMMdd
        _time:datetime = values.get('time')
        if not _time:
            new_record = super(HrAttendance, self).create(values)
            new_record.notify_checkin()
            return new_record
        # Tính thời gian bắt đầu của ngày dựa trên 'time_in'
        time_utc_7 = _time + timedelta(hours=7)
        today_start = datetime.combine(time_utc_7.date(), time(0, 0)) - timedelta(hours=7)
        today_end = datetime.combine(time_utc_7.date(), time(23, 59, 59)) - timedelta(hours=7)
        # Tìm xem partner_id đã checkin chưa
        checkin = self.search([
            ('employee_id', '=', values.get('employee_id')),
            ('check_in', '>=', fields.Datetime.to_string(today_start)),
            ('check_in', '<=', fields.Datetime.to_string(today_end))
        ], order='check_in desc', limit=1)
        if checkin:
            if _time < checkin.check_in:
                checkin.check_out = checkin.check_in
                checkin.check_in = _time
            elif not checkin.check_out:
                checkin.check_out = _time
            elif _time > checkin.check_out:
                checkin.check_out = _time
            checkin.notify_checkin()
            return checkin
        values['check_in'] = _time
        del values['time']
        record = super(HrAttendance, self).create(values)
        
        # Gửi thông báo checkin cho client
        record.notify_checkin()
        return record
    
    # Hiển thị popup thông báo checkin cho client
    def notify_checkin(self):
        self.ensure_one()
        bus = self.env['bus.bus']
        employee_id = self.employee_id
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        image = self.env['ir.attachment'].search([('res_model', '=', 'hr.employee'), ('res_id', '=', employee_id.id), ('res_field', '=', 'image_1920')], limit=1)
        image_url = ""
        if image:
            image_url = f"{base_url}/web/image/{image.id}/image_1920"
        tz = timezone(timedelta(hours=7))
        msg = {
            'member': {
                'id': employee_id.id,
                'name': employee_id.display_name or '',
                'dob': employee_id.birthday.strftime("%d-%m-%Y %H:%M") if employee_id.birthday else '',
                'phone': employee_id.phone or '',
                'avatar': image_url,
                'gender': dict(employee_id.fields_get(allfields=['gender'])['gender']['selection'])[employee_id.gender] if employee_id.gender else '',
            },
            'check_in': self.check_in.astimezone(tz).strftime("%H:%M") if self.check_in else  '',
            'check_out': self.check_out.astimezone(tz).strftime("%H:%M") if self.check_out else  '',
            'worked_hours': self._get_work_hours_hm(),
        }
        
        # Tạo thông báo cho client
        notifications = []
        users = self.env['res.users'].search(
                    [('groups_id', 'in', [self.env.ref('hr_attendance.group_hr_attendance_manager').id])]).mapped('partner_id')
        for p in users:
            notifications.append((
                p, 'web.notify.hr.attendance', msg
            ))
        bus._sendmany(notifications)
        
    def _get_work_hours_hm(self):
        # Tính số giờ và số phút
        hours = int(self.worked_hours)
        minutes = int((self.worked_hours - hours) * 60)
        
        # Định dạng chuỗi giờ:phút
        return f"{hours:02d}:{minutes:02d}"
    
    def _check_validity(self):
        """ Verifies the validity of the attendance record compared to the others from the same employee.
            For the same employee we must have :
                * maximum 1 "open" attendance record (without check_out)
                * no overlapping time slices with previous employee records
        """
        for attendance in self:
            today_start = datetime.combine(attendance.check_in.date(), time(0, 0))
            # we take the latest attendance before our check_in time and check it doesn't overlap with ours
            last_attendance_before_check_in = self.env['hr.attendance'].search([
                ('employee_id', '=', attendance.employee_id.id),
                ('check_in', '<=', attendance.check_in),
                ('check_in', '>=', fields.Datetime.to_string(today_start)),
                ('id', '!=', attendance.id),
            ], order='check_in desc', limit=1)
            if last_attendance_before_check_in and last_attendance_before_check_in.check_out and last_attendance_before_check_in.check_out > attendance.check_in:
                raise exceptions.ValidationError(_("Cannot create new attendance record for %(empl_name)s, the employee was already checked in on %(datetime)s") % {
                    'empl_name': attendance.employee_id.name,
                    'datetime': format_datetime(self.env, attendance.check_in, dt_format=False),
                })

            if not attendance.check_out:
                # if our attendance is "open" (no check_out), we verify there is no other "open" attendance
                no_check_out_attendances = self.env['hr.attendance'].search([
                    ('employee_id', '=', attendance.employee_id.id),
                    ('check_out', '=', False),
                    ('check_in', '>=', fields.Datetime.to_string(today_start)),
                    ('id', '!=', attendance.id),
                ], order='check_in desc', limit=1)
                if no_check_out_attendances:
                    raise exceptions.ValidationError(_("Cannot create new attendance record for %(empl_name)s, the employee hasn't checked out since %(datetime)s") % {
                        'empl_name': attendance.employee_id.name,
                        'datetime': format_datetime(self.env, no_check_out_attendances.check_in, dt_format=False),
                    })
            else:
                # we verify that the latest attendance with check_in time before our check_out time
                # is the same as the one before our check_in time computed before, otherwise it overlaps
                last_attendance_before_check_out = self.env['hr.attendance'].search([
                    ('employee_id', '=', attendance.employee_id.id),
                    ('check_in', '<', attendance.check_out),
                    ('check_in', '>=', fields.Datetime.to_string(today_start)),
                    ('id', '!=', attendance.id),
                ], order='check_in desc', limit=1)
                if last_attendance_before_check_out and last_attendance_before_check_in != last_attendance_before_check_out:
                    raise exceptions.ValidationError(_("Cannot create new attendance record for %(empl_name)s, the employee was already checked in on %(datetime)s") % {
                        'empl_name': attendance.employee_id.name,
                        'datetime': format_datetime(self.env, last_attendance_before_check_out.check_in, dt_format=False),
                    })

    def _get_last_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.now(timezone.utc)

        # Tính toán ngày đầu tiên của tháng hiện tại và trừ đi 1 tháng
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_last_month_utc = (first_day_of_this_month - relativedelta(months=1) - relativedelta(hours=7))

        # Tính toán ngày cuối cùng của tháng trước
        last_day_of_last_month_utc = first_day_of_this_month - timedelta(seconds=1) - timedelta(hours=7)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('check_in', '>=', first_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('check_in', '<=', last_day_of_last_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]

    def _get_current_month_domain(self):
        # Lấy thời gian hiện tại theo UTC
        today = datetime.now(timezone.utc)

        # Tính toán ngày đầu tiên của tháng hiện tại
        first_day_of_this_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        first_day_of_this_month_utc = first_day_of_this_month - relativedelta(hours=7)

        # Tính toán ngày đầu tiên của tháng sau
        first_day_of_next_month = (first_day_of_this_month + relativedelta(months=1)).replace(day=1)
        first_day_of_next_month_utc = first_day_of_next_month - relativedelta(hours=7)

        # Ngày cuối cùng của tháng này là một ngày trước ngày đầu tiên của tháng sau
        last_day_of_this_month_utc = first_day_of_next_month_utc - relativedelta(seconds=1)

        # Trả về domain để lọc các bản ghi trong khoảng thời gian này
        return [('&'),
                ('check_in', '>=', first_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S')),
                ('check_in', '<=', last_day_of_this_month_utc.strftime('%Y-%m-%d %H:%M:%S'))]

    # override hàm search để thêm bộ lọc tùy chỉnh
    @api.model
    def search(self, domain, offset=0, limit=None, order=None, count=False):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'check_in' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'check_in' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()
        return super(HrAttendance, self).search(domain, offset, limit, order, count)

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        # Kiểm tra nếu domain chứa 'last_month'
        for index, arg in enumerate(domain):
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'check_in' and arg[2] == 'last_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_last_month_domain()
            if isinstance(arg, (list, tuple)) and len(arg) == 3 and arg[0] == 'check_in' and arg[2] == 'current_month':
                # Thay thế với domain tháng trước
                domain[index:index + 1] = self._get_current_month_domain()
        return super(HrAttendance, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)
