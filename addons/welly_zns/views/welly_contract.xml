<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_contract_form_inherit_zns" model="ir.ui.view">
        <field name="name">welly.contract.form.inherit.zns</field>
        <field name="model">welly.contract</field>
        <field name="inherit_id" ref="welly_base.view_welly_contract_form"/>
        <field name="arch" type="xml">
            <!-- Thêm trường is_sent_zns vào form view -->
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="is_sent_zns" invisible="1"/>
            </xpath>
            <xpath expr="//button[@name='button_send_request']" position="after">
                <button name="button_sign_and_send_zns" type="object" string="Xác nhận &amp; ZNS"
                        class="btn-primary"
                        data-hotkey="z"
                        groups="welly_base.group_admin_club,welly_base.group_coo,welly_base.group_welly_account"
                        title="<PERSON><PERSON><PERSON> nhận và gửi thông báo qua <PERSON>alo cho khách hàng về việc hóa đơn đã thanh toán"
                        attrs="{'invisible': ['|', ('state', '!=', 'confirm_admin'), ('is_sent_zns', '=', True)]}"/>
                <!-- Thêm nút để thử gửi ZNS -->
                <button name="button_try_send_zns" type="object" string="Gửi lại ZNS"
                        class="btn-secondary"
                        data-hotkey="y"
                        groups="welly_base.group_admin_club,welly_base.group_coo,welly_base.group_welly_account"
                        title="Gửi thông báo qua Zalo cho khách hàng về việc hóa đơn đã thanh toán"
                        attrs="{'invisible': ['|', ('state', 'in', ('draft', 'confirm_admin')), ('is_sent_zns', '=', True)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
