import logging
import re
from datetime import datetime
import json
import requests
import uuid
from odoo import models, fields, api

_logger = logging.getLogger(__name__)


class ZnsConfig(models.Model):
    _name = 'zns.config'
    _description = '<PERSON><PERSON>u hình thông tin Zalo Notification Service'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    end_point = fields.Char(string='Đường dẫn gửi tin nhắn', required=True, tracking=True)
    token_url = fields.Char(string='Đường dẫn lấy access token', required=True, tracking=True)
    app_id = fields.Char(string='Application ID', required=True, tracking=True)
    app_secret = fields.Char(string='Secret Key', required=True)
    template_id = fields.Char(string='Mẫu gửi tin nhắn biên lai', required=True, tracking=True)
    template_id_contract_pt = fields.Char(string='Mẫu gửi tin nhắn hợp đồng PT', tracking=True)
    template_id_contract_member = fields.Char(string='Mẫu gửi tin nhắn hợp đồng <PERSON> Viên', tracking=True)
    template_id_contract_turn_card = fields.Char(string='Mẫu gửi tin nhắn hợp đồng <PERSON>', tracking=True)
    access_token = fields.Char(string='Access Token')
    refresh_token = fields.Char(string='Refresh Token', required=True)
    token_updated_date = fields.Datetime(string='Ngày cập nhật token')

    def refresh_access_token(self):
        """Làm mới access token bằng refresh token"""
        self.ensure_one()

        if not self.refresh_token:
            _logger.error("Không có refresh token để làm mới access token")
            return False

        payload = {
            "app_id": self.app_id,
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token"
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "secret_key": self.app_secret
        }

        try:
            response = requests.post(self.token_url, data=payload, headers=headers)

            if response.status_code == 200:
                token_data = response.json()
                if token_data.get("access_token"):
                    self.write({
                        'access_token': token_data.get("access_token"),
                        'refresh_token': token_data.get("refresh_token", self.refresh_token),
                        'token_updated_date': fields.Datetime.now(),
                    })
                    self.env['mail.message'].create({
                        'model': 'zns.config',
                        'res_id': self.id,
                        'message_type': 'comment',
                        'body': "Làm mới ZNS access token thành công",
                    })
                    return True
                else:
                    self.env['mail.message'].create({
                        'model': 'zns.config',
                        'res_id': self.id,
                        'message_type': 'comment',
                        'body': f"Lỗi khi làm mới ZNS token: {response.status_code}, {response.text}",
                    })
                    return False
            else:
                self.env['mail.message'].create({
                    'model': 'zns.config',
                    'res_id': self.id,
                    'message_type': 'comment',
                    'body': f"Lỗi khi làm mới ZNS token: {response.status_code}, {response.text}",
                })
                return False

        except Exception as e:
            _logger.exception(f"Lỗi khi kết nối đến Zalo OAuth: {str(e)}")
            return False

    @api.model
    def get_zns_config(self):
        """Lấy cấu hình ZNS hiện tại (chỉ có 1 bản ghi active)"""
        return self.search([(1, '=', 1)], limit=1)

    @api.model
    def cron_refresh_access_token(self):
        """Hàm được gọi bởi cron job để làm mới access token"""
        zns_config = self.get_zns_config()
        if zns_config:
            return zns_config.refresh_access_token()
        else:
            self.env['mail.message'].create({
                'model': 'zns.config',
                'res_id': self.id,
                'message_type': 'comment',
                'body': f"Không tìm thấy cấu hình ZNS nào hoạt động",
            })
            return False

    def format_phone_number(self, phone):
        """Quy đổi số điện thoại từ format 0xxx thành 84xxx"""
        is_phone_valid, is_phone_has_sub = False, False
        if not phone:
            return phone, is_phone_valid, False
        # Loại bỏ các ký tự không phải số
        if re.search(r'\D', phone):
            phone = re.sub(r'\D', '', phone)
        else:
            is_phone_has_sub = True
        # Kiểm tra format và chuyển đổi
        if phone.startswith('0'):
            phone = '84' + phone[1:]
            is_phone_valid = True
        elif phone.startswith('84'):
            is_phone_valid = True
        elif len(phone) == 9:
            # Nếu số điện thoại có 9 ký tự và không bắt đầu bằng 0 hoặc 84
            phone = '84' + phone
        # Kiểm tra độ dài sau khi đã chuyển đổi
        if len(phone) != 11:
            return phone, is_phone_valid, False
        is_phone_valid = is_phone_has_sub if not is_phone_has_sub else is_phone_valid
        return phone, is_phone_valid, True

    def format_date(self, date):
        """Format date theo định dạng dd/MM/yyyy"""
        if not date:
            return ''
        if isinstance(date, str):
            try:
                date = datetime.strptime(date, '%Y-%m-%d')
            except ValueError:
                return date
        return date.strftime('%d/%m/%Y')

    def format_currency(self, amount):
        """Format số tiền thành chuỗi có dấu phân cách nghìn và đơn vị tiền tệ"""
        if not amount:
            return "0 VNĐ"
        amount_int = int(amount)
        formatted_amount = "{:,.0f}".format(amount_int).replace(",", ".")
        return f"{formatted_amount} VNĐ"

    def get_zns_error_message(self, error_code):
        """Lấy thông báo lỗi chi tiết từ mã lỗi ZNS"""
        zns_error_codes = {
            -100: "Xảy ra lỗi không xác định, vui lòng thử lại sau",
            -101: "Ứng dụng gửi ZNS không hợp lệ",
            -102: "Ứng dụng gửi ZNS không tồn tại",
            -103: "Ứng dụng chưa được kích hoạt",
            -104: "Secret key của ứng dụng không hợp lệ",
            -105: "Ứng dụng gửi ZNS chưa được liên kết với OA nào",
            -106: "Phương thức không được hỗ trợ",
            -107: "ID thông báo không hợp lệ",
            -108: "Số điện thoại không hợp lệ",
            -109: "ID mẫu ZNS không hợp lệ",
            -110: "Phiên bản Zalo app không được hỗ trợ",
            -111: "Mẫu ZNS không có dữ liệu",
            -112: "Dữ liệu mẫu ZNS không hợp lệ",
            -113: "Button không hợp lệ",
            -114: "Người dùng không nhận được ZNS",
            -115: "Tài khoản ZNS không đủ số dư",
            -116: "Nội dung không hợp lệ",
            -117: "OA chưa được cấp quyền sử dụng mẫu ZNS này",
            -118: "Tài khoản Zalo không tồn tại hoặc đã bị vô hiệu hoá",
            -119: "Tài khoản không thể nhận ZNS",
            -120: "OA chưa được cấp quyền sử dụng tính năng này",
            -121: "Mẫu ZNS không có nội dung",
            -122: "Body request không đúng định dạng JSON",
            -123: "Giải mã nội dung thông báo RSA thất bại",
            -124: "Mã truy cập không hợp lệ",
            -125: "ID Official Account không hợp lệ",
            -131: "Mẫu ZNS chưa được phê duyệt",
            -133: "Mẫu ZNS này không được phép gửi vào ban đêm (từ 22h-6h)",
            -134: "Người dùng chưa phản hồi gợi ý nhận ZNS từ OA",
            -135: "OA chưa có quyền gửi ZNS",
            -141: "Người dùng từ chối nhận ZNS từ Official Account",
            -144: "OA đã vượt giới hạn gửi ZNS trong ngày",
            -146: "Mẫu ZNS này đã bị vô hiệu hoá do chất lượng gửi thấp",
            -147: "Mẫu ZNS đã vượt giới hạn gửi trong ngày",
        }
        return zns_error_codes.get(error_code, "Lỗi không xác định")


class ZnsMixin(models.AbstractModel):
    """Mixin để thêm chức năng gửi ZNS cho các model"""
    _name = 'zns.mixin'
    _description = 'ZNS Mixin'

    is_sent_zns = fields.Boolean('Đã gửi thông báo qua ZALO', default=False)
    error_sent_zns = fields.Boolean('Lỗi gửi ZNS', default=False)

    def _get_zns_phone_number(self):
        """Override trong từng model để lấy số điện thoại"""
        raise NotImplementedError("Cần override method _get_zns_phone_number")

    def _get_zns_customer_name(self):
        """Override trong từng model để lấy tên khách hàng"""
        raise NotImplementedError("Cần override method _get_zns_customer_name")

    def _get_zns_template_id(self):
        """Override trong từng model để lấy template ID phù hợp"""
        raise NotImplementedError("Cần override method _get_zns_template_id")

    def _prepare_zns_template_data(self):
        """Override trong từng model để chuẩn bị dữ liệu template"""
        raise NotImplementedError("Cần override method _prepare_zns_template_data")

    def _log_zns_result(self, success=True, message="", phone="", template_data=None):
        """Ghi log kết quả gửi ZNS"""
        if success:
            log_message = f"""
            <div style='color: #0180a5; margin-bottom: 10px;'>
                <strong>✅ Đã gửi thông báo ZNS thành công</strong><br/>
                📞 Khách hàng: {self._get_zns_customer_name()}<br/>
                📱 SĐT: {phone}<br/>
                {self._format_success_log_details(template_data)}
            </div>
            """
        else:
            log_message = f"""
            <div style='color: #F7374F; margin-bottom: 10px;'>
                <strong>❌ Lỗi gửi ZNS</strong><br/>
                {message}
            </div>
            """

        self.message_post(
            body=log_message,
            message_type='comment',
            subtype_xmlid='mail.mt_note'
        )

    def _format_success_log_details(self, template_data):
        """Format chi tiết log thành công - override trong từng model"""
        return ""

    def send_zns(self):
        """Gửi thông báo ZNS"""
        self.ensure_one()

        try:
            # Lấy cấu hình ZNS
            zns_config = self.env['zns.config'].get_zns_config()

            # Kiểm tra đã gửi chưa
            if self.is_sent_zns:
                return

            # Lấy số điện thoại
            phone = self._get_zns_phone_number()
            if not phone:
                self._log_zns_result(
                    success=False,
                    message="Khách hàng chưa có số điện thoại"
                )
                return

            # Kiểm tra và định dạng số điện thoại
            phone_sent, is_phone_valid, is_valid = zns_config.format_phone_number(phone)

            if not is_valid:
                self._log_zns_result(
                    success=False,
                    message=f"Số điện thoại ({phone}) không đúng định dạng"
                )
                self.error_sent_zns = True
                return

            # Lấy template ID phù hợp
            template_id = self._get_zns_template_id()
            if not template_id:
                self._log_zns_result(
                    success=False,
                    message="Không tìm thấy template ID phù hợp"
                )
                return

            # Chuẩn bị dữ liệu template
            template_data = self._prepare_zns_template_data()

            # Tạo tracking ID
            tracking_id = str(uuid.uuid4())

            # Chuẩn bị payload
            payload = {
                "phone": phone_sent,
                "template_id": template_id,
                "template_data": template_data,
                "tracking_id": tracking_id
            }

            # Chuẩn bị headers
            headers = {
                "Content-Type": "application/json",
                "access_token": zns_config.access_token
            }

            # Gửi request đến Zalo API
            response = requests.post(
                zns_config.end_point,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )

            response_data = response.json()

            # Kiểm tra kết quả
            if response.status_code == 200 and response_data.get('error', 0) == 0:
                # Gửi thành công
                self.write({
                    'is_sent_zns': True,
                    'error_sent_zns': False
                })
                # Ghi log thành công
                display_phone = '0' + phone_sent[2:] if phone_sent.startswith('84') else phone_sent
                self._log_zns_result(
                    success=True,
                    phone=display_phone,
                    template_data=template_data
                )

                _logger.info(f"ZNS sent successfully to {phone_sent} for {self._name} {self.id}")

            else:
                # Gửi thất bại
                error_code = response_data.get('error')
                error_message = response_data.get('message', 'Unknown error')
                error_detail = zns_config.get_zns_error_message(error_code)

                self.write({
                    'error_sent_zns': True
                })

                self._log_zns_result(
                    success=False,
                    message=f"Mã lỗi: [{error_code}]<br/>Nội dung: {error_detail}<br/>Message: {error_message}"
                )

                _logger.error(f"ZNS send failed: [{error_code}] {error_detail} - {error_message}")

        except requests.exceptions.Timeout:
            error_msg = "Timeout khi gửi ZNS"
            self.write({
                'error_sent_zns': True
            })
            self._log_zns_result(success=False, message=error_msg)
            _logger.error(f"ZNS timeout for {self._name} {self.id}")

        except requests.exceptions.RequestException as e:
            error_msg = f"Lỗi kết nối ZNS: {str(e)}"
            self.write({
                'error_sent_zns': True
            })
            self._log_zns_result(success=False, message=error_msg)
            _logger.error(f"ZNS connection error for {self._name} {self.id}: {e}")

        except Exception as e:
            error_msg = f"Lỗi không xác định: {str(e)}"
            self.write({
                'error_sent_zns': True
            })
            self._log_zns_result(success=False, message=error_msg)
            _logger.error(f"Unexpected error sending ZNS for {self._name} {self.id}: {e}")
