import logging

from odoo import models

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _name = "account.move"
    _inherit = ['account.move', 'zns.mixin']

    def _get_zns_phone_number(self):
        """<PERSON><PERSON>y số điện thoại từ partner"""
        return self.partner_id.phone

    def _get_zns_customer_name(self):
        """<PERSON><PERSON>y tên khách hàng"""
        return self.partner_id.name

    def _get_zns_template_id(self):
        """lấy template ID phù hợp"""
        zns_config = self.env['zns.config'].get_zns_config()
        return zns_config.template_id

    def _prepare_zns_template_data(self):
        """Chuẩn bị dữ liệu cho template ZNS hóa đơn"""
        self.ensure_one()
        zns_config = self.env['zns.config'].get_zns_config()

        # L<PERSON><PERSON> thông tin hình thức thanh toán
        payment_type_display = ''
        if hasattr(self, 'payment_type_welly') and self.payment_type_welly:
            payment_type_selection = dict(
                self.fields_get(allfields=['payment_type_welly'])['payment_type_welly']['selection']
            )
            payment_type_display = payment_type_selection.get(self.payment_type_welly, 'Trả trước sử dụng sau')
        else:
            payment_type_display = 'Trả trước sử dụng sau'

        return {
            "ten": self.partner_id.name or '',
            "goi_dich_vu": getattr(self, 'sale_order_template_name_print', '') or '',
            "ma_bl": self.name or '',
            "thoi_han": f"{zns_config.format_date(getattr(self, 'date_start', None))} - {zns_config.format_date(getattr(self, 'date_end', None))}",
            "ngay_thanh_toan": zns_config.format_date(self.invoice_date),
            "hinh_thuc_dang_ky": getattr(self, 'registration_form_name_print', '') or '',
            "qua_tang": getattr(self, 'welly_gift_name_print', '') or "Không có",
            "tong_tien": zns_config.format_currency(getattr(self, 'amount_paid', 0) or self.amount_total),
            "hinh_thuc_thanh_toan": payment_type_display
        }

    def _format_success_log_details(self, template_data):
        """Format chi tiết log thành công cho hóa đơn"""
        return f"""
        Gói dịch vụ: {template_data.get('goi_dich_vu', '')}<br/>
        Thời hạn: {template_data.get('thoi_han', '')}<br/>
        Ngày thanh toán: {template_data.get('thoi_han', '')}<br/>
        Hình thức đăng ký: {template_data.get('hinh_thuc_dang_ky', '')}<br/>
        Quà tặng: {template_data.get('qua_tang', '')}<br/>
        Tổng tiền: {template_data.get('tong_tien', '')}<br/>
        Hình thức thanh toán: {template_data.get('hinh_thuc_thanh_toan', '')}
        """

    def button_sign_and_send_zns(self):
        """Phê duyệt hóa đơn và gửi thông báo ZNS cho khách hàng"""
        # Lấy cấu hình ZNS
        zns_config = self.env['zns.config'].search([(1, '=', 1)], limit=1)
        if not zns_config:
            return self.button_sign()
        for rec in self:
            rec.send_zns()
        return self.button_sign()

    def button_try_send_zns(self):
        """Thử gửi lại ZNS cho các hóa đơn đã phê duyệt nhưng chưa gửi"""
        for rec in self:
            if rec.approval_state == 'approved' and not rec.is_sent_zns:
                rec.send_zns()
