import logging

from odoo import models

_logger = logging.getLogger(__name__)


class WellyContract(models.Model):
    _name = 'welly.contract'
    _inherit = ['welly.contract', 'zns.mixin']

    def _get_zns_phone_number(self):
        """<PERSON><PERSON><PERSON> số điện thoại từ partner"""
        return self.partner_id.phone

    def _get_zns_customer_name(self):
        """<PERSON><PERSON><PERSON> tên khách hàng"""
        return self.partner_id.name

    def _get_zns_template_id(self):
        """Lấy template ID phù hợp dựa trên loại hợp đồng"""
        # Mapping service_type với template_id
        zns_config = self.env['zns.config'].get_zns_config()
        template_mapping = {
            'pt': zns_config.template_id_contract_pt,
            'member': zns_config.template_id_contract_member,
            'turn_card': zns_config.template_id_contract_turn_card,
        }

        service_type = getattr(self, 'service_type', None)
        template_id = template_mapping.get(service_type)

        # Fallback về template mặc định nếu không tìm thấy
        if not template_id:
            return None

        return template_id

    def _prepare_zns_template_data(self):
        """Chuẩn bị dữ liệu cho template ZNS hợp đồng theo từng loại dịch vụ"""
        self.ensure_one()
        zns_config = self.env['zns.config'].get_zns_config()

        # Dữ liệu chung cho tất cả loại dịch vụ
        base_data = {
            "ten": self.partner_id.name or '',
            "ma_hoi_vien": self.name or '',
            "ngay_kich_hoat": f"{zns_config.format_date(self.date_start)}",
            "ngay_het_han": f"{zns_config.format_date(self.date_end)}",
            "goi_dich_vu": self.sale_order_template_name_print or '',
            "qua_tang": self.welly_gift_name_print or "Không có",
            "tong_tien": zns_config.format_currency(self.pay_amount),
        }

        service_type = getattr(self, 'service_type', None)

        if service_type == 'turn_card':
            # Loại dịch vụ: Tích lượt
            template_data = base_data.copy()
            template_data.update({
                "so_luot": getattr(self, 'session_number', 0) or 0
            })

        elif service_type == 'pt':
            # Loại dịch vụ: PT
            template_data = base_data.copy()

            template_data.update({
                "so_buoi": getattr(self, 'session_number', 0) or 0,
                "huan_luyen_vien": getattr(self, 'coach_name_print', "Linh hoạt") or "Linh hoạt"
            })

        elif service_type == 'member':
            # Loại dịch vụ: Hội viên
            template_data = base_data.copy()
            # Lấy thời hạn display
            thoi_han_selection = dict(
                self.fields_get(allfields=['valid_time_type'])['valid_time_type']['selection']
            ) if hasattr(self, 'valid_time_type') else {}
            thoi_han_display = thoi_han_selection.get(getattr(self, 'valid_time_type', ''), '')

            # Lấy danh sách nhân viên kinh doanh
            marketing_staff_names = ', '.join(self.marketing_staff_id.mapped('name')) if self.marketing_staff_id else 'Welly'

            template_data.update({
                "thoi_han": f"{getattr(self, 'valid_time', 0) or 0} {thoi_han_display}",
                "nv_tu_van": marketing_staff_names or '',
            })

        else:
            # Fallback cho các loại dịch vụ khác
            template_data = base_data.copy()

        return template_data

    def _format_success_log_details(self, template_data):
        """Format chi tiết log thành công cho hợp đồng theo từng loại dịch vụ"""
        service_type = getattr(self, 'service_type', None)

        # Thông tin chung
        common_info = f"""
        Mã hội viên: {template_data.get('ma_hoi_vien', '')}<br/>
        Gói dịch vụ: {template_data.get('goi_dich_vu', '')}<br/>
        Ngày kích hoạt: {template_data.get('ngay_kich_hoat', '')}<br/>
        Ngày hết hạn: {template_data.get('ngay_het_han', '')}<br/>
        Quà tặng: {template_data.get('qua_tang', '')}<br/>
        Tổng tiền: {template_data.get('tong_tien', '')}<br/>
        """

        if service_type == 'turn_card':
            # Loại dịch vụ: Tích lượt
            specific_info = f"""
            Số lượt: {template_data.get('so_luot', '')}<br/>
            """

        elif service_type == 'pt':
            # Loại dịch vụ: PT
            specific_info = f"""
            Số buổi: {template_data.get('so_buoi', '')}<br/>
            Huấn luyện viên: {template_data.get('huan_luyen_vien', '')}<br/>
            """

        elif service_type == 'member':
            # Loại dịch vụ: Hội viên
            specific_info = f"""
            Thời hạn: {template_data.get('thoi_han', '')}<br/>
            Nhân viên tư vấn: {template_data.get('nv_tu_van', '')}<br/>
            """

        else:
            # Fallback cho các loại dịch vụ khác
            specific_info = f"""
            Thời hạn: {template_data.get('thoi_han', '')}<br/>
            Huấn luyện viên: {template_data.get('huan_luyen_vien', '')}<br/>
            """

        return common_info + specific_info

    def button_sign_and_send_zns(self):
        """Phê duyệt hợp đồng và gửi thông báo ZNS cho khách hàng"""
        # Lấy cấu hình ZNS
        zns_config = self.env['zns.config'].search([(1, '=', 1)], limit=1)
        if not zns_config:
            return self.button_confirm_admin()
        for rec in self:
            rec.send_zns()
        return self.button_confirm_admin()

    def button_try_send_zns(self):
        """Thử gửi lại ZNS cho các hợp đồng đã phê duyệt nhưng chưa gửi"""
        for rec in self:
            if rec.state not in ('draft', 'confirm_admin') and not rec.is_sent_zns:
                rec.send_zns()
