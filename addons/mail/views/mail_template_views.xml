<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="email_template_form">
            <field name="name">email.template.form</field>
            <field name="model">mail.template</field>
            <field name="arch" type="xml">
                <form string="Templates">
                    <header>
                        <field name="template_fs" invisible="1"/>
                        <button string="Reset Template"
                                name="%(mail_template_reset_action)d" type="action"
                                groups="mail.group_mail_template_editor"
                                attrs="{'invisible': [('template_fs', '=', False)]}"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <field name="ref_ir_act_window" invisible="1"/>
                            <button class="oe_stat_button"
                                    groups="base.group_no_one"
                                    name="create_action" type="object"
                                    attrs="{'invisible':[('ref_ir_act_window','!=',False)]}" icon="fa-plus"
                                    help="Display an option on related documents to open a composition wizard with this template">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Add</span>
                                    <span class="o_stat_text">Context Action</span>
                                </div>
                            </button>
                            <button name="unlink_action" type="object"
                                    groups="base.group_no_one"
                                    class="oe_stat_button" icon="fa-minus"
                                    attrs="{'invisible':[('ref_ir_act_window','=',False)]}"
                                    help="Remove the contextual action to use this template on related documents" widget="statinfo">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_text">Remove</span>
                                    <span class="o_stat_text">Context Action</span>
                                </div>
                            </button>
                            <button class="oe_stat_button" name="%(mail_template_preview_action)d" icon="fa-search-plus" string="Preview"
                                    type="action" target="new"/>
                        </div>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1><field name="name" class="w-100"
                                required="1" placeholder='e.g. "Welcome email"'/></h1>
                            <group>
                                <field name="model_id" placeholder="e.g. Contact" required="1" options="{'no_create': True}"/>
                                <field name="subject" placeholder='e.g. "Welcome to MyCompany" or "Nice to meet you, {{ object.name }}"'/>
                                <field name="model" invisible="1"/>
                                <field name="description"/>
                            </group>
                        </div>
                        <notebook>
                            <page string="Content" name="content">
                                <field name="can_write" invisible="1"/>
                                <field name="body_html" widget="html" class="oe-bordered-editor"
                                    options="{'style-inline': true, 'codeview': true, 'dynamic_placeholder': true}"
                                    attrs="{'readonly': [('can_write', '=', False), ('id', '!=', False)]}"/>
                                <field name="attachment_ids" widget="many2many_binary"/>
                            </page>
                            <page string="Email Configuration" name="email_configuration">
                                <group>
                                    <field name="email_from"
                                            placeholder="Override author's email"/>
                                    <field name="use_default_to"/>
                                    <field name="email_to" attrs="{'invisible': [('use_default_to', '=', True)]}"
                                            placeholder="Comma-separated recipient addresses"/>
                                    <field name="partner_to" attrs="{'invisible': [('use_default_to', '=', True)]}"
                                            placeholder="Comma-separated ids of recipient partners"/>
                                    <field name="email_cc" attrs="{'invisible': [('use_default_to', '=', True)]}"
                                            placeholder="Comma-separated carbon copy recipients addresses"/>
                                    <field name="reply_to"
                                            placeholder="Email address to which replies will be redirected when sending emails in mass"/>
                                    <field name="scheduled_date" string="Scheduled Send Date"/>
                                </group>
                            </page>
                            <page string="Settings" name="email_settings">
                                <group>
                                    <field name="lang" placeholder="{{ object.partner_id.lang }}"/>
                                    <field name="mail_server_id"/>
                                    <field name="auto_delete"/>
                                    <field name="report_template" domain="[('model','=',model)]"/>
                                    <field name="report_name" attrs="{'invisible':[('report_template','=',False)]}"/>
                                </group>
                            </page>
                        </notebook>
                       </sheet>
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="email_template_tree">
            <field name="name">email.template.tree</field>
            <field name="model">mail.template</field>
            <field name="arch" type="xml">
                <tree string="Templates">
                    <field name="mail_server_id" invisible="1"/>
                    <field name="name"/>
                    <field name="model_id" groups="base.group_no_one"/>
                    <field name="description"/>
                    <field name="subject" optional="hidden"/>
                    <field name="email_from" optional="hidden"/>
                    <field name="email_to" optional="hidden"/>
                    <field name="partner_to" optional="hidden"/>
                    <field name="report_name" optional="hidden"/>
                </tree>
            </field>
        </record>

        <record id="view_email_template_search" model="ir.ui.view">
            <field name="name">email.template.search</field>
            <field name="model">mail.template</field>
            <field name="arch" type="xml">
                <search string="Templates">
                    <field name="name" filter_domain="['|', '|', '|',('name','ilike',self), ('report_name','ilike',self), ('subject','ilike',self), ('email_to','ilike',self)]" string="Templates"/>
                    <field name="lang"/>
                    <field name="model_id"/>
                    <filter name="base_templates" string="Base Templates" domain="[('template_category', '=', 'base_template')]"/>
                    <filter name="custom_templates" string="Custom Templates" domain="[('template_category', '=', 'custom_template')]"/>
                    <group expand="0" string="Group by...">
                        <filter string="SMTP Server" name="smtpserver" domain="[]" context="{'group_by':'mail_server_id'}"/>
                        <filter string="Model" name="model" domain="[]" context="{'group_by':'model_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_email_template_tree_all">
            <field name="name">Email Templates</field>
            <field name="res_model">mail.template</field>
            <field name="view_mode">form,tree</field>
            <field name="view_id" ref="email_template_tree" />
            <field name="search_view_id" ref="view_email_template_search"/>
            <field name="context">{'search_default_base_templates': 1}</field>
        </record>

    </data>
</odoo>
