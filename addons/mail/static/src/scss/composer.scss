@font-face {
    font-family: 'emojifont';
    src: local('Segoe UI'),
         local('Apple Color Emoji'),
         local('Android Emoji'),
         local('Noto Color Emoji'),
         local('Twitter Color Emoji'),
         local('Twitter Color'),
         local('EmojiOne Color'),
         local('EmojiOne'),
         local(EmojiSymbols),
         local(Symbola);
}

// Emoji
.o_mail_emoji {
    display: inline-block;
    padding: 0;
    font-size: 1.3rem;
    font-family: emojifont;
}

@mixin o-viewer-black-btn {
    background-color: rgba(black, 0.4);
    color: rgba(map-get($theme-colors, 'light'), 0.7);

    &:hover {
        background-color: rgba(black, 0.6);
        color: white;
    }

    &.disabled {
        color: map-get($grays, '600');
        background: none;
    }
}
.o_modal_fullscreen {
    z-index: $o-mail-thread-window-zindex + 1;

    .arrow {
        @include o-position-absolute(50%, $grid-gutter-width*0.5);
        border-radius: 100%;
        padding: 12px 16px 11px 18px;
        @include o-viewer-black-btn;
    }

    .arrow-left {
        left: $grid-gutter-width*0.5;
        right: auto;
        padding: 12px 18px 11px 16px;
    }
}

.o_document_viewer_topbar_button {
    display: flex;
    align-items: center;
    align-self: stretch;

    &:hover {
        background-color: rgba($white, 0.1);
    }
}

.o_mail_composer_form {
    .oe-bordered-editor[name=body] .o_readonly {
        border: 1px solid $o-gray-300;
        padding: 4px;
    }
}
