import base64
import json
import logging
from datetime import datetime, timezone, timedelta
from bs4 import BeautifulSoup
from passlib.context import CryptContext
from odoo.tools import config
from odoo.sql_db import db_connect
from odoo import http, fields, api
from odoo.exceptions import AccessDenied
from odoo.http import request
import random
import string
pwd_context = CryptContext(schemes=["pbkdf2_sha512", "plaintext"], deprecated="auto")

_logger = logging.getLogger(__name__)


# Basic authen for User
def get_basic_authen_info_from_header():
    # Lấy header Authorization
    auth_header = request.httprequest.headers.get('Authorization')
    if auth_header and auth_header.startswith('Basic '):
        token = auth_header[6:]
    else:
        return None, "Token is missing or invalid"

    # Giải mã token Base64 để lấy username và password
    try:
        decoded_token = base64.b64decode(token).decode('utf-8')
        username, password = decoded_token.split(':', 1)
    except Exception as e:
        return None, f"Invalid token: {str(e)}"

    # X<PERSON><PERSON> thực người dùng
    try:
        uid = request.env['res.users'].authenticate_user_for_mywelly(username, password)
    except AccessDenied as e:
        return None, str(e)

    return uid, None


def get_user_name_passwd():
    auth_header = request.httprequest.headers.get('Authorization')
    if auth_header and auth_header.startswith('Basic '):
        token = auth_header[6:]
    else:
        return None, "Token is missing or invalid"

    # Giải mã token Base64 để lấy username và password
    try:
        decoded_token = base64.b64decode(token).decode('utf-8')
        username, password = decoded_token.split(':', 1)
    except Exception as e:
        return None, f"Invalid token: {str(e)}"
    return username, password

def get_data_and_log_request(request):
    data = json.loads(request.httprequest.data)
    _logger.info(f"Request: {data}  - Header: {request.httprequest.headers}")
    return data

class BaseResponse:

    @staticmethod
    def success(data=None, message="SUCCESS", index=None):
        response = {
            "errorCode": 0,
            "message": message,
            "data": data
        }

        if index:
            response["index"] = index
        # _logger.info(f"Response: {response}")
        ex = json.dumps(response)
        response = request.make_response(ex, status=200)
        response.headers['Content-Type'] = 'application/json'
        return response

    @staticmethod
    def error(error_code, message="Error", data=None):
        data = {
            "errorCode": error_code,
            "message": message,
            "data": data
        }
        _logger.info(f"Response: {data}")
        response = None
        if error_code == 1 or error_code == 400 or error_code == 404:
            response = request.make_response(json.dumps(data), status=200)
        else:
            response = request.make_response(json.dumps(data), status=error_code)
        response.headers['Content-Type'] = 'application/json'
        return response


class AuthController(http.Controller):

    @http.route('/api/user/login', type='http', auth='public', methods=['POST'], csrf=False)
    def api_login(self, **kwargs):
        data = get_data_and_log_request(request)
        username = data.get('username')
        password = data.get('password')
        sub_domain = data.get('sub_domain')

        if not username or not password:
            return BaseResponse.error(error_code=1, message="Username, password are required")
        if not sub_domain:
            db = request.session.db
            # Xác thực người dùng
            try:
                uid = request.env['res.users'].authenticate(db, username, password, {})
            except AccessDenied:
                uid = False

            if not uid:
                return BaseResponse.error(error_code=1, message="Sai tên đăng nhập hoặc mật khẩu")

            # Tạo token Basic Authentication
            token = base64.b64encode(f"{username}:{password}".encode('utf-8')).decode('utf-8')
            return BaseResponse.success(data=f'Basic {token}')

        db_use = config.get('use_mywelly') if config.get('use_mywelly') else None
        if not db_use:
            return BaseResponse.error(error_code=1, message='Chưa cấu hình use_mywelly')
        db_list = db_use.split(',')
        if sub_domain not in db_list:
            return BaseResponse.error(error_code=1, message='Tên miền Club chưa chính xác hoặc chưa được cấu hình để sử dụng App, liên hệ quản trị viên.')
        try:
            # Kết nối tới database tương ứng
            conn = db_connect(sub_domain)
            with conn.cursor() as cr:
                env = api.Environment(cr, 1, {})
                # Xác thực người dùng
                try:
                    uid = env['res.users'].sudo().authenticate(sub_domain, username, password, {})
                except AccessDenied:
                    uid = False

                if not uid:
                    return BaseResponse.error(error_code=1, message="Sai tên đăng nhập hoặc mật khẩu")

                # Tạo token Basic Authentication
                token = base64.b64encode(f"{username}:{password}".encode('utf-8')).decode('utf-8')

                hidden_value = env['ir.config_parameter'].sudo().get_param('hidden_feature_mywelly')
                endpoint = env['ir.config_parameter'].sudo().get_param('mywelly_host')
                mywelly_address_name = env['ir.config_parameter'].sudo().get_param('mywelly_address_name')
                company = env['res.company'].search([], order='id asc', limit=1)
                result = {
                    'company_name': f'{mywelly_address_name if mywelly_address_name else ""}',
                    'company_address': f'{company.street if mywelly_address_name else ""}',
                    'company_hotline': f'{company.phone.replace(" ", "") if company.phone else ""}',
                    'company_email': f'{company.email if company.email else ""}',
                    'endpoint': endpoint if endpoint else "",
                    'hidden_feature': hidden_value if hidden_value else "",
                    'sub_domain': sub_domain,
                    'token': f'Basic {token}'
                }
                return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/user/registerPublicUser', type='http', auth='public', methods=['POST'], csrf=False)
    def register_public_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            # Kiểm tra các tham số bắt buộc
            required_fields = ['name', 'username', 'password']
            for field in required_fields:
                if field not in data:
                    return BaseResponse.error(error_code=1, message=f'Missing required field: {field}')
            name = data['name']
            email = data['username']
            password = data['password']

            # Kiểm tra xem email đã tồn tại chưa
            existing_user = request.env['res.users'].sudo().search([('login', '=', email)], limit=1)
            if existing_user:
                return BaseResponse.error(error_code=1, message=f'tài khoản đã tồn tại')

            # Tạo người dùng mới với nhóm 'public'
            public_group = request.env.ref('base.group_public')
            new_user = request.env['res.users'].sudo().create({
                'name': name,
                'login': email,
                'password': password,
                'groups_id': [(6, 0, [public_group.id])]
            })
            token = base64.b64encode(f"{email}:{password}".encode('utf-8')).decode('utf-8')
            return BaseResponse.success(data=f'Basic {token}')
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/user/changePassword', type='http', auth='public', methods=['POST'], csrf=False)
    def api_change_password(self, **kwargs):
        data = get_data_and_log_request(request)
        user_id, error = get_basic_authen_info_from_header()
        if error:
            return BaseResponse.error(error_code=401, message=error)
        old_password = data.get('old_password')
        new_password = data.get('new_password')

        if not old_password or not new_password:
            return BaseResponse.error(error_code=1, message="old_password and new_password are required")
        user_name, passwd = get_user_name_passwd()
        if old_password != passwd:
            return BaseResponse.error(error_code=1, message="Mật khẩu cũ không đúng")
        user = request.env['res.users'].sudo().browse(user_id)
        user.password = new_password

        token = base64.b64encode(f"{user_name}:{new_password}".encode('utf-8')).decode('utf-8')
        return BaseResponse.success(data=f'Basic {token}')

    # api lấy thông tin partner
    @http.route('/api/user/getInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_user_info(self, **post):
        try:
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            user_record = request.env['res.users'].sudo().browse(user_id)
            partner = user_record.partner_id
            if not partner:
                return BaseResponse.error(error_code=1, message="Partner not found")
            avatar = None

            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            image = request.env['ir.attachment'].sudo().search(
                [('res_model', '=', 'res.partner'), ('res_id', '=', partner.id),
                 ('res_field', '=', 'avatar_mywelly')], limit=1)
            if image:
                access_token = image.generate_access_token()[0]
                avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"

            response_data = {
                'display_name': partner.display_name or None,
                'username': user_record.login,
                'avatar': avatar
            }

            return BaseResponse.success(data=response_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/user/updateAvatar', type='http', auth='public', methods=['POST'], csrf=False)
    def update_avatar_user(self, **post):
        try:
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            # Kiểm tra file được gửi lên
            if 'avatar_file' not in request.httprequest.files:
                return BaseResponse.error(error_code=1, message=str("avatar_file cần thiết"))

            # Lấy file object
            file_object = request.httprequest.files['avatar_file']
            if file_object.filename == '':
                return BaseResponse.error(error_code=1, message=str("file rỗng"))

            # Đọc nội dung file và mã hóa base64
            file_content = base64.b64encode(file_object.read())

            # Tìm bản ghi partner và cập nhật ảnh đại diện
            user_record = request.env['res.users'].sudo().browse(user_id)
            partner_record = user_record.partner_id
            partner_record.avatar_mywelly = file_content
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api update thông tin partner
    @http.route('/api/user/updateInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def update_user_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            display_name = data.get('display_name')
            phone = data.get('phone')
            email = data.get('email')
            partner_id_number = data.get('partner_id_number')
            birthdate = data.get('birthdate')
            gender = data.get('gender')
            nationality_id = data.get('nationality_id')
            street = data.get('address')
            fcm_token = data.get('fcm_token')
            is_deleted = data.get('is_deleted')

            user_record = request.env['res.users'].sudo().browse(user_id)
            partner_record = user_record.partner_id

            update_partner = {}
            if display_name and display_name != partner_record.display_name:
                update_partner.update({'display_name': display_name})
                update_partner.update({'name': display_name})
            if phone and phone != partner_record.phone:
                update_partner.update({'phone': phone})
            if email and email != partner_record.email:
                update_partner.update({'email': email})
            if partner_id_number and partner_id_number != partner_record.partner_id_number:
                update_partner.update({'partner_id_number': partner_id_number})
            if birthdate:
                birthdate_format = "%d-%m-%Y"
                birthdate = datetime.strptime(birthdate, birthdate_format).date()
                update_partner.update({'birthdate': birthdate})
            if gender:
                update_partner.update({'gender': gender})
            if nationality_id:
                update_partner.update({'nationality_id': int(nationality_id)})
            if street:
                update_partner.update({'street': street})
            if fcm_token:
                update_partner.update({'fcm_token': fcm_token})
            if is_deleted and isinstance(is_deleted, bool):
                update_partner.update({'is_deleted_welly_id': is_deleted})
            partner_record.update(update_partner)
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api disable tài khoản
    @http.route('/api/user/disable', type='http', auth='public', methods=['POST'], csrf=False)
    def disable_user(self, **kwargs):
        try:
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            characters = string.ascii_letters + string.digits  # Chỉ bao gồm chữ cái và chữ số
            password = ''.join(random.choice(characters) for i in range(12))
            user = request.env['res.users'].sudo().browse(user_id)
            user.password = password
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api Lấy list Thông báo của user
    @http.route('/api/user/getListNotification', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_notification_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # dữ liệu nhận vào client có thể có nhiều type phân cách bởi dấu , ví dụ: type: "training,event"
            type = data.get('type')
            page = data.get('page', 1)
            size = data.get('size', 10)

            user_record = request.env['res.users'].sudo().browse(user_id)
            partner_id = user_record.partner_id.id

            noti_r = request.env['notification.queue'].sudo()

            # build query
            query_type = (1, '=', 1)
            if type:
                query_type = ('type', 'in', type.split(','))
            domain = [
                ('partner_id', '=', int(partner_id)),
                ('type', 'not in', ['training_telegram']),
                query_type
            ]
            # phân trang
            offset = (page - 1) * size
            total_records = noti_r.search_count(domain)
            total_page = (total_records + size - 1) // size
            # lấy tổng noti chưa đọc
            domain_unread = [
                ('partner_id', '=', int(partner_id)),
                ('type', 'not in', ['training_telegram']),
                ('is_read', '=', False)
            ]
            total_unread = noti_r.search_count(domain_unread)
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records,
                "total_unread": total_unread
            }

            # query và lọc response
            notification_records = noti_r.search_read(
                domain=domain,
                fields=['id', 'title', 'content_summary', 'type', 'image_title', 'heading', 'content_detail',
                        'is_read', 'read_date', 'calendar_id', 'contract_id', 'create_date', 'partner_id'],
                order='create_date desc',
                offset=offset,
                limit=size
            )
            if not notification_records:
                return BaseResponse.success(data=[])

            # lọc response
            for record in notification_records:
                record['partner_id'] = record['partner_id'][0] if record['partner_id'] else None
                record['title'] = record['title'] or None
                record['content_summary'] = record['content_summary'] or None
                record['type'] = record['type'] or None
                record['image_title'] = record['image_title'] or None
                record['heading'] = record['heading'] or None
                record['content_detail'] = record['content_detail'] or None
                record['read_date'] = self._convert_utc_to_timestamp_milliseconds(record['read_date']) if record[
                    'read_date'] else None
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds(record['create_date']) if record[
                    'create_date'] else None
                record['calendar_id'] = record['calendar_id'][0] if record['calendar_id'] else None
                record['contract_id'] = record['contract_id'][0] if record['contract_id'] else None
            return BaseResponse.success(data=notification_records, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin chi tiết Notification và update thành is_read
    @http.route('/api/user/getNotificationDetail', type='http', auth='public', methods=['POST'], csrf=False)
    def get_notification_detail_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            user_record = request.env['res.users'].sudo().browse(user_id)
            partner_id = user_record.partner_id.id

            notification_id = data.get('notification_id')
            if not notification_id:
                return BaseResponse.error(error_code=1, message="notification_id required")

            noti_r = request.env['notification.queue'].sudo().search(domain=[
                ('id', '=', int(notification_id)),
                ('partner_id', '=', int(partner_id))
            ])

            if not noti_r:
                return BaseResponse.error(error_code=1, message="Notification not found")

            response_data = {
                'title': noti_r.title or None,
                'content_summary': noti_r.content_summary or None,
                'type': noti_r.type or None,
                'image_title': noti_r.image_title or None,
                'heading': noti_r.heading or None,
                'content_detail': noti_r.content_detail or None,
                'is_read': noti_r.is_read,
                'read_date': self._convert_utc_to_timestamp_milliseconds(
                    noti_r.read_date) if noti_r.read_date else None,
                'create_date': self._convert_utc_to_timestamp_milliseconds(
                    noti_r.create_date) if noti_r.create_date else None,
                'calendar_id': noti_r.calendar_id.id if noti_r.calendar_id else None,
                'contract_id': noti_r.contract_id.id if noti_r.contract_id else None,
            }
            noti_r.update({'is_read': True,
                           'read_date': fields.Datetime.now()})
            return BaseResponse.success(data=response_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/uploadImage', type='http', auth='public', methods=['POST'], csrf=False)
    def upload_images(self, **kwargs):
        try:
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            event_id = request.params.get('event_id')

            if 'files' not in request.httprequest.files or not event_id:
                return BaseResponse.error(error_code=1, message=str("files, event_id required"))

            if not event_id.isdigit():
                return BaseResponse.error(error_code=1, message=str("event_id chỉ được chứa số"))
            event = request.env['calendar.event'].sudo().browse(int(event_id))

            if not event:
                return BaseResponse.error(error_code=1, message=str("Lịch không tồn tại"))

            # if event.state not in ['draft', 'await_confirm']:
            #     return BaseResponse.error(error_code=1, message=str("Trạng thái của lịch không thể upload ảnh"))

            # Lấy files tải lên
            uploaded_files = request.httprequest.files.getlist('files')

            # Kiểm tra xem `uploaded_files` có file nào không
            if not uploaded_files or all(not file.filename for file in uploaded_files):
                return BaseResponse.error(error_code=1, message='Không có file nào được tải lên')

            # Validate and process each file
            allowed_mimetypes = [
                'image/jpeg',  # JPEG
                'image/png',  # PNG
                'image/gif',  # GIF
                'image/bmp',  # BMP
                'image/webp',  # WebP
                'image/tiff',  # TIFF
                'image/svg+xml'  # SVG
            ]
            max_file_size = 25 * 1024 * 1024

            ids = []

            for uploaded_file in uploaded_files:
                # Validate file type
                if uploaded_file.content_type not in allowed_mimetypes:
                    return BaseResponse.error(
                        error_code=1,
                        message=f"Định dạng ảnh không hợp lệ: {uploaded_file.filename}."
                    )

                file_content = uploaded_file.read()
                file_name = uploaded_file.filename

                file_base64 = base64.b64encode(file_content)

                attachment = request.env['ir.attachment'].with_user(user_id).create({
                    'name': file_name,
                    'type': 'binary',
                    'datas': file_base64,
                    'mimetype': uploaded_file.content_type,
                    'res_model': 'calendar.event',
                    'res_id': event_id,
                })

                # Append the result
                ids.append(attachment.id)
            event_att_ids = event.attachment_ids.ids
            if event_att_ids:
                ids.extend(event_att_ids)
            if ids:
                event.with_user(user_id).write({'attachment_ids': [[6, False, ids]]})

            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/deleteImage', type='http', auth='public', methods=['POST'], csrf=False)
    def delete_image(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            image_ids = data.get('image_ids')
            event_id = data.get('calendar_id')
            if not event_id:
                return BaseResponse.error(error_code=1, message="calendar_id required")
            if not image_ids:
                return BaseResponse.error(error_code=1, message="image_ids required")
            event = request.env['calendar.event'].sudo().browse(int(event_id))
            if not event:
                return BaseResponse.error(error_code=1, message="Lịch không tồn tại")
            if event.state not in ['draft', 'await_confirm']:
                return BaseResponse.error(error_code=1, message="Trạng thái lịch không được xoá ảnh")
            attachments = event.attachment_ids
            attachment_ids = attachments.ids
            invalid_image_ids = [image_id for image_id in image_ids if image_id not in attachment_ids]
            if invalid_image_ids:
                return BaseResponse.error(error_code=1, message=f"Ảnh có các ID sau không thuộc event: {invalid_image_ids}")
            event.with_user(user_id).write({'attachment_ids': [(3, image_id, 0) for image_id in image_ids]})
            attachments_to_delete = request.env['ir.attachment'].sudo().search([('id', 'in', image_ids)])
            if attachments_to_delete:
                attachments_to_delete.with_user(1).unlink()
                return BaseResponse.success(data={'deleted_ids': image_ids})
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/createCalendarForUser', type='http', auth='public', methods=['POST'], csrf=False)
    def create_calendar_for_pt(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            location_id = data.get('location_id')
            exercise_id = data.get('exercise_id')
            service_id = data.get('service_id')
            tag_id = data.get('tag_id')
            start_time = data.get('start')
            stop_time = data.get('stop')
            note = data.get('note')
            session_type = data.get('session_type')
            partners = data.get('partners')
            name = data.get('name')
            is_recurrency = data.get('is_recurrency', False)  # Mặc định False
            days = data.get('days', [])  # Mảng các ngày

            if not location_id or not start_time or not stop_time or not exercise_id or not service_id:
                return BaseResponse.error(error_code=1,
                                          message="location_id, service_id, exercise_id, start_time, stop_time required")

            # Kiểm tra is_recurrency và days
            if is_recurrency and not days:
                return BaseResponse.error(error_code=1,
                                          message="Khi tạo lịch hàng loạt, bạn phải chọn ít nhất một ngày hợp lệ")

            user_record = request.env['res.users'].sudo().browse(user_id)

            # check quyền có phải pt hay pt_manager không
            if not user_record.has_group('welly_base.group_pt') and not user_record.has_group('welly_base.group_pt_manager'):
                return BaseResponse.error(error_code=1,
                                          message="Tài khoản không có quyền tạo lịch PT")

            # Convert start_time và stop_time ban đầu
            start_utc = self.convert_timestamp_milliseconds_to_utc(start_time)
            stop_utc = self.convert_timestamp_milliseconds_to_utc(stop_time)

            if start_utc >= stop_utc:
                return BaseResponse.error(error_code=1,
                                          message="Thời gian kết thúc phải sau thời gian bắt đầu.")

            start_utc7 = start_utc + timedelta(hours=7)
            stop_utc7 = stop_utc + timedelta(hours=7)

            # Nếu không phải tạo hàng loạt, kiểm tra cùng ngày như cũ
            if not is_recurrency and stop_utc7.date() != start_utc7.date():
                return BaseResponse.error(error_code=1,
                                          message="Thời gian bắt đầu và kết thúc phải cùng ngày.")

            now_utc = datetime.now(timezone.utc)

            # Tạo mảng các thời gian cần tạo lịch
            datetime_list = []

            if is_recurrency:
                # Lấy time từ start_utc7 và stop_utc7
                start_time_only = start_utc7.time()
                stop_time_only = stop_utc7.time()

                # Convert mảng days thành datetime
                for day_str in days:
                    try:
                        # Parse ngày từ string (dd/mm/yyyy)
                        day_date = datetime.strptime(day_str, "%d/%m/%Y").date()

                        # Tạo datetime cho start và stop với ngày mới và time cũ
                        start_datetime = datetime.combine(day_date, start_time_only)
                        stop_datetime = datetime.combine(day_date, stop_time_only)

                        # Convert về UTC (trừ 7 giờ)
                        start_utc_new = start_datetime - timedelta(hours=7)
                        stop_utc_new = stop_datetime - timedelta(hours=7)

                        # Thêm timezone info
                        start_utc_new = start_utc_new.replace(tzinfo=timezone.utc)
                        stop_utc_new = stop_utc_new.replace(tzinfo=timezone.utc)

                        datetime_list.append({
                            'start_utc': start_utc_new,
                            'stop_utc': stop_utc_new,
                            'date_str': day_str
                        })
                    except ValueError:
                        return BaseResponse.error(error_code=1,
                                                  message=f"Định dạng ngày không hợp lệ: {day_str}. Vui lòng sử dụng định dạng dd/mm/yyyy")
            else:
                # Tạo lịch đơn như cũ
                datetime_list.append({
                    'start_utc': start_utc,
                    'stop_utc': stop_utc,
                    'date_str': start_utc7.strftime("%d/%m/%Y")
                })

            # Tạo các lịch tập
            created_events = []

            for dt_info in datetime_list:
                start_time_utc = fields.Datetime.to_string(dt_info['start_utc'])
                stop_time_utc = fields.Datetime.to_string(dt_info['stop_utc'])

                data_create_event = {
                    'name': f'Lịch của PT : {user_record.partner_id.display_name}',
                    'start': start_time_utc,
                    'stop': stop_time_utc,
                    'event_type': 'pt',
                    'pt_id': user_id,
                    'exercise_id': exercise_id,
                    'location_id': location_id,
                    'partner_ids': [[6, False, []]],
                    'is_app_booking': True
                }

                if dt_info['stop_utc'] < now_utc:
                    data_create_event.update({
                        'is_past_booking': True,
                    })
                if tag_id:
                    data_create_event.update({
                        "categ_ids": [(4, int(tag_id))],
                    })
                if note:
                    data_create_event.update({
                        'notes': note,
                    })
                if session_type:
                    data_create_event.update({
                        'session_type': session_type,
                    })
                if service_id:
                    data_create_event.update({
                        'service_id': service_id,
                    })
                if name:
                    data_create_event.update({
                        'name': str(name),
                    })

                event = request.env['calendar.event'].with_user(user_id).create(data_create_event)
                created_events.append({
                    'event': event,
                    'date_str': dt_info['date_str'],
                    'start_utc': dt_info['start_utc'],
                    'stop_utc': dt_info['stop_utc']
                })

                # Thêm partners cho từng event
                for partner in partners:
                    partner_id = partner.get('id')
                    event.update({
                        'partner_ids': [(4, int(partner_id))]
                    })
                    attendee = request.env['calendar.attendee'].sudo().search([
                        ('partner_id', '=', int(partner_id)),
                        ('event_id', '=', int(event.id)),
                    ], order='id ASC', limit=1)
                    if attendee:
                        data_attendee = {
                            'partner_id': partner_id,
                            'state': 'tentative',
                            'contract_id': partner.get('contract_id'),
                        }
                        event.with_user(user_id).write({'attendee_ids': [[1, attendee.id, data_attendee]]})

            # Gửi thông báo firebase (chỉ một lần)
            if created_events and partners:
                for partner in partners:
                    partner_id = partner.get('id')
                    notification_q = request.env['notification.queue'].sudo()

                    if is_recurrency:
                        # Tạo nội dung cho đặt lịch hàng loạt
                        dates_info = []
                        for event_info in created_events:
                            start_time_display = (event_info['start_utc'] + timedelta(hours=7)).strftime("%H:%M")
                            stop_time_display = (event_info['stop_utc'] + timedelta(hours=7)).strftime("%H:%M")
                            dates_info.append(f"{start_time_display}-{stop_time_display} {event_info['date_str']}")

                        content_summary = f'{user_record.partner_id.display_name} đã tạo lịch PT cho Bạn tại các thời gian: {", ".join(dates_info)}'
                    else:
                        # Nội dung cho đặt lịch đơn như cũ
                        event_info = created_events[0]
                        event = event_info['event']
                        content_summary = f'{user_record.partner_id.display_name} đã tạo lịch PT cho Bạn. ' \
                                          f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}' \
                                          f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}'

                    new_noti = notification_q.create({
                        'partner_id': partner_id,
                        'title': "Lịch tập đã được PT tạo cho Bạn",
                        'content_summary': content_summary,
                        'type': 'training',
                        'calendar_id': created_events[0]['event'].id  # Lấy ID của event đầu tiên
                    })
                    notification_q.send_notification_to_firebase(new_noti)

            if created_events:
                if is_recurrency:
                    return BaseResponse.success(data=[event_info['event'].id for event_info in created_events])
                else:
                    return BaseResponse.success(data=created_events[0]['event'].id)

        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.error(error_code=1, message=str(e))

    # api cập nhật thời gian của lịch có trạng thái nháp
    @http.route('/api/book_pt/updateCalendarTime', type='http', auth='public', methods=['POST'], csrf=False)
    def update_calendar_time(self, **kwargs):
        try:
            data = get_data_and_log_request(request)

            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            calendar_id = data.get('calendar_id')
            start_time = data.get('start')
            stop_time = data.get('stop')

            if not calendar_id or not start_time or not stop_time:
                return BaseResponse.error(error_code=1, message="calendar_id, start, stop là bắt buộc.")

            start_utc = self.convert_timestamp_milliseconds_to_utc(start_time)
            stop_utc = self.convert_timestamp_milliseconds_to_utc(stop_time)
            now_utc = datetime.now(timezone.utc)
            if start_utc <= now_utc:
                return BaseResponse.error(error_code=1, message="Thời gian bắt đầu phải lớn hơn thời gian hiện tại.")
            if stop_utc <= start_utc:
                return BaseResponse.error(error_code=1, message="Thời gian kết thúc phải lớn hơn thời gian bắt đầu.")
            start_utc7 = start_utc + timedelta(hours=7)
            stop_utc7 = stop_utc + timedelta(hours=7)
            if start_utc7.date() != stop_utc7.date():
                return BaseResponse.error(error_code=1, message="Thời gian bắt đầu và kết thúc phải cùng ngày.")

            calendar = request.env['calendar.event'].sudo().browse(int(calendar_id))
            if not calendar.exists():
                return BaseResponse.error(error_code=1, message="Không tìm thấy lịch.")
            if calendar.pt_id.id != user_id:
                return BaseResponse.error(error_code=1, message="Bạn không có quyền chỉnh sửa lịch này.")

            calendar.with_user(user_id).write({
                'start': fields.Datetime.to_string(start_utc),
                'stop': fields.Datetime.to_string(stop_utc)
            })
            return BaseResponse.success(data={'calendar_id': calendar_id})
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.error(error_code=1, message=str(e))

    # API lấy ra lịch tập của pt
    @http.route('/api/book_pt/getCalendarByUser', type='http', auth='public', methods=['POST'], csrf=False)
    def get_calendar_by_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            start_time = data.get('start')
            stop_time = data.get('stop')
            state = data.get('state')
            page = data.get('page', 1)
            size = data.get('size', 10)
            order = data.get('order', 'start ASC')
            if not start_time or not stop_time:
                return BaseResponse.error(error_code=1, message="start, stop require")
            start = self.convert_timestamp_milliseconds_to_utc(start_time)
            stop = self.convert_timestamp_milliseconds_to_utc(stop_time)
            event_r = request.env['calendar.event'].sudo()
            domain = [
                '|', '|',
                '&',
                ('pt_id', '=', int(user_id)),
                ('pt_id_substitute', '=', False),
                ('pt_id_substitute', '=', int(user_id)),
                ('user_id', '=', int(user_id)),
                ('start', '>=', start),
                ('start', '<=', stop),
            ]
            if state:
                state_string = str(state)
                if state_string == 'draft':
                    domain.append(('state', 'in', ('draft', 'await_confirm')))
                else:
                    domain.append(('state', '=', state_string))
            # phân trang
            offset = (page - 1) * size
            total_records = event_r.search_count(domain)
            total_page = (total_records + size - 1) // size

            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }

            # query và lọc response
            event_records = event_r.search(
                domain=domain,
                order=order,
                offset=offset,
                limit=size
            )
            if not event_records:
                return BaseResponse.success(data=[])
            result = []
            for event in event_records:
                tags = ','.join(tag.name for tag in event.categ_ids) if event.categ_ids else None
                partners = [{'id': attendee.partner_id.id, 'name': attendee.display_name,
                             'state': attendee.state, 'attendee_id': attendee.id,
                             'contract_id': attendee.contract_id.id if attendee.contract_id else None,
                             'phone': attendee.partner_id.phone if attendee.partner_id.phone else None,
                             'can_confirm': self.check_can_confirm(attendee_state=attendee.state, event_state=event.state) or
                                            self.check_can_re_confirm(attendee_state=attendee.state,
                                                                     event_state=event.state,
                                                                     user_id=user_id,
                                                                     pt_id=event.pt_id.id,
                                                                     pt_sub_id=event.pt_id_substitute.id if event.pt_id_substitute else None),
                             'can_declined': self.check_can_decline(attendee_state=attendee.state, event_state=event.state)} for attendee in
                            event.attendee_ids] if event.attendee_ids else None
                rs = {
                    "id": event.id,
                    "name": event.name,
                    "type": event.event_type,
                    "state": event.state if event.state != 'await_confirm' else 'draft',
                    "is_past_booking": event.is_past_booking if event.is_past_booking else False,
                    "location": event.location_id.name if event.location_id else None,
                    "start": self._convert_utc_to_timestamp_milliseconds(event.start),
                    "stop": self._convert_utc_to_timestamp_milliseconds(event.stop),
                    "service": event.service_id.name if event.service_id else None,
                    "pt": event.pt_id.partner_id.display_name if event.pt_id else None,
                    "exercise": event.exercise_id.name if event.exercise_id else None,
                    "tag": tags,
                    "note": event.notes if event.notes else None,
                    "session_type": event.session_type.name if event.session_type else None,
                    "partners": partners
                }
                result.append(rs)

            return BaseResponse.success(data=result, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API lấy ra chi tiết lịch tập
    @http.route('/api/book_pt/getCalendarDetailForUser', type='http', auth='public', methods=['POST'], csrf=False)
    def get_calendar_detail(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            if not event_id:
                return BaseResponse.error(error_code=1, message="calendar_id required")
            event = request.env['calendar.event'].sudo().browse(int(event_id))
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
            tags = ','.join(tag.name for tag in event.categ_ids) if event.categ_ids else None
            partners = [{'id': attendee.partner_id.id, 'name': attendee.display_name,
                         'state': attendee.state, 'attendee_id': attendee.id,
                         'is_checked_in': attendee.is_checked_in,
                         'contract_id': attendee.contract_id.id if attendee.contract_id else None,
                         'phone': attendee.partner_id.phone if attendee.partner_id.phone else None,
                         'can_confirm': self.check_can_confirm(attendee_state=attendee.state, event_state=event.state) or
                                        self.check_can_re_confirm(attendee_state=attendee.state,
                                                                     event_state=event.state,
                                                                     user_id=user_id,
                                                                     pt_id=event.pt_id.id,
                                                                     pt_sub_id=event.pt_id_substitute.id if event.pt_id_substitute else None),
                         'can_declined': self.check_can_decline(attendee_state=attendee.state, event_state=event.state)} for attendee in
                        event.attendee_ids] if event.attendee_ids else None
            # kiểm tra có bao nhiêu người tham gia có trạng thái khác hủy và tìm số lượng người có thể thêm mới vào
            accepted_partners = len(event.attendee_ids.filtered(lambda a: a.state != 'declined'))
            limit_participant = event.exercise_id.limit_participants if event.exercise_id else 0
            can_add_partner = 0
            if event.state in ['draft', 'await_confirm']:
                if limit_participant > 0 and accepted_partners < limit_participant:
                    can_add_partner = limit_participant - accepted_partners
            attachments = [{
                'id': image.id,
                'url': f"{base_url}/web/content/{image.id}?access_token={image.generate_access_token()[0]}"
            } for image in event.attachment_ids] if event.attachment_ids else None
            rs = {
                "id": event.id,
                "name": event.name,
                "state": event.state if event.state != 'await_confirm' else 'draft',
                "is_past_booking": event.is_past_booking if event.is_past_booking else False,
                "location": event.location_id.name if event.location_id else None,
                "start": self._convert_utc_to_timestamp_milliseconds(event.start),
                "stop": self._convert_utc_to_timestamp_milliseconds(event.stop),
                "service": event.service_id.name if event.service_id else None,
                "pt": event.pt_id.partner_id.display_name if event.pt_id else None,
                "exercise": event.exercise_id.name if event.exercise_id else None,
                "limit_participant": limit_participant,
                "can_add_partner": can_add_partner,
                "tag": tags,
                "note": event.notes if event.notes else None,
                "session_type": event.session_type.name if event.session_type else None,
                "partners": partners,
                "attachments": attachments
            }
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra calendar đã được đặt của pt
    @http.route('/api/book_pt/getCalendarAvailableForUser', type='http', auth='public', methods=['POST'], csrf=False)
    def get_available_calendar_for_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            start_time = data.get('start')
            stop_time = data.get('stop')
            if not start_time or not stop_time:
                return BaseResponse.error(error_code=1, message="start_time, stop_time required")
            # query builder
            domain = [('event_type', '=', 'pt'),
                      ('state', '!=', 'reject'),
                      '|',
                      '&',
                      ('pt_id', '=', int(user_id)),
                      ('pt_id_substitute', '=', False),
                      ('pt_id_substitute', '=', int(user_id)),
                      ('start', '>=', self.convert_timestamp_milliseconds_to_utc(start_time)),
                      ('start', '<=', self.convert_timestamp_milliseconds_to_utc(stop_time))]
            calendar_records = request.env['calendar.event'].sudo().search_read(
                domain=domain,
                fields=['id', 'start', 'stop'],
                order='start asc'
            )
            for record in calendar_records:
                record['state'] = record['state'] if record['state'] != 'await_confirm' else 'draft'
                record['start'] = self._convert_utc_to_timestamp_milliseconds((record['start']))
                record['stop'] = self._convert_utc_to_timestamp_milliseconds((record['stop']))
            return BaseResponse.success(data=calendar_records)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra danh sách khách hàng hợp lệ để add vào lịch đã được tạo sẵn
    @http.route('/api/book_pt/getListPartnerValidByEvent', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_partner_valid_by_event(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('calendar_id')
            if not event_id:
                return BaseResponse.error(error_code=1, message="calendar_id required")
            event = request.env['calendar.event'].sudo().browse(int(event_id))
            exercise_id = event.exercise_id.id
            limit_participant = event.exercise_id.limit_participants
            attendee_ids = event.mapped('attendee_ids.partner_id.id')
            # nếu lớp 1:1 thì không cho add thêm khách nếu đã có đủ khách có trạng thái là accept, tentative ở trong lịch
            if limit_participant == 1:
                if len(attendee_ids) > 0:
                    if all(att.state in ['tentative', 'accepted'] for att in event.attendee_ids):
                        return BaseResponse.error(error_code=1, message="Lớp đã đủ giới hạn người tham gia")
            location_id = event.location_id.id
            start_utc = event.start
            stop_utc = event.stop
            start_time = (start_utc + timedelta(hours=7)).strftime('%H:%M')
            start_float = float(start_time[:2]) + float(start_time[3:5]) / 60
            stop_time = (stop_utc + timedelta(hours=7)).strftime('%H:%M')
            stop_float = float(stop_time[:2]) + float(stop_time[3:5]) / 60
            service_id = event.service_id.id
            # Lọc xem có show F không
            tags = event.categ_ids.ids
            contracts = None
            show_f = request.env['calendar.event.type'].sudo().search([('name', '=', 'Show F')], limit=1).id
            if tags and show_f in tags:
                domain_member_show_f = [
                    ('service_type', '=', 'member'),
                    # ('state', '=', 'activated'),
                    ('free_session_number', '>', 0),
                    # '|',
                    # ('coach_id', '=', user_id),
                    # ('coach_id', '=', False),
                    # ('date_end', '>=', start_utc),
                    ('checkin_time_from', '<=', start_float),
                    ('checkin_time_to', '>=', stop_float),
                    ('welly_location_many2_many', 'in', location_id),
                ]
                contracts = request.env['welly.contract'].sudo().search(domain_member_show_f)
                if not contracts:
                    domain_pt_show_f = [
                        ('service_type', '=', 'pt'),
                        # ('state', '=', 'activated'),
                        ('free_session_number', '>', 0),
                        ('exercise_form_id', '=', exercise_id),
                        ('welly_service_ids', 'in', service_id),
                        # '|',
                        # ('coach_id', '=', user_id),
                        # ('coach_id', '=', False),
                        # ('date_end', '>=', start_utc),
                        ('checkin_time_from', '<=', start_float),
                        ('checkin_time_to', '>=', stop_float),
                        ('welly_location_many2_many', 'in', location_id),
                    ]
                    contracts_with_coach = request.env['welly.contract'].sudo().search(
                        domain_pt_show_f + [('coach_id', '=', user_id)])
                    if contracts_with_coach:
                        contracts = contracts_with_coach
                    else:
                        contracts = request.env['welly.contract'].sudo().search(domain_pt_show_f)
            else:
                domain_not_show_f = [
                    '|',
                    '&', ('service_type', '=', 'member'),
                    ('free_session_number', '>', 0),
                    ('service_type', '=', 'pt'),
                    # ('state', '=', 'activated'),
                    # '|',
                    # ('available_session_number', '>', 0),
                    # ('free_session_number', '>', 0),
                    '|',
                    '&',
                    ('service_type', '=', 'pt'),
                    ('exercise_form_id', '=', exercise_id),
                    '&',
                    ('service_type', '=', 'member'),
                    (1, '=', 1),
                    ('welly_service_ids', 'in', service_id),
                    # '|',
                    # ('coach_id', '=', user_id),
                    # ('coach_id', '=', False),
                    # ('date_end', '>=', start_utc),
                    ('checkin_time_from', '<=', start_float),
                    ('checkin_time_to', '>=', stop_float),
                    ('welly_location_many2_many', 'in', location_id),
                ]
                contracts = request.env['welly.contract'].sudo().search(domain_not_show_f)
            partner_ids = contracts.mapped('partner_account_ids.id') if contracts else []
            if not partner_ids:
                return BaseResponse.success(data=[])
            # Lọc ra các partner_ids không có trong attendee_ids
            valid_partner_ids = [partner_id for partner_id in partner_ids if partner_id not in attendee_ids]

            partners = request.env['res.partner'].sudo().search([('id', 'in', valid_partner_ids)])
            partner_data = []
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')

            contracts_by_partner = {}
            # tạo dict(từ điển) contracts_by_partner giúp việc tìm ra hợp đồng của partner sẽ nhanh hơn dùng lambda hoặc fillter do ko phải gọi vào ORM lần nữa
            for contract in contracts:
                for partner in contract.partner_account_ids:
                    if partner.id not in contracts_by_partner:
                        contracts_by_partner[partner.id] = []
                    contracts_by_partner[partner.id].append(contract)
            for partner in partners:
                # check xem khách hàng đã có ở trong lịch nào khác chưa
                domain = [
                    ('id', '!=', event.id),
                    ('event_type', '=', 'pt'),
                    ('state', '!=', 'reject'),
                    ('partner_ids', '=', partner.id),
                    '|',
                    '&', ('start', '<', event.stop), ('stop', '>', event.start),
                    '&', ('start', '<', event.stop), ('stop', '>', event.start),
                ]
                overlapping_events = request.env['calendar.event'].sudo().search(domain)

                domain_attendee = [
                    ('partner_id', '=', partner.id),
                    ('event_id', 'in', overlapping_events.ids),
                    '|',
                    '&', ('state', 'in', ['accepted', 'tentative', 'needsAction', 'await_pt_confirm']), (1, '=', 1),
                    '&', ('is_checked_in', '=', True), ('state', '=', 'accepted')
                ]
                attendees_with_accepted_state = request.env['calendar.attendee'].sudo().search(domain_attendee)
                if not attendees_with_accepted_state:
                    # lấy thông tin contract
                    contract_partner = contracts_by_partner.get(partner.id, [])
                    contract_rs = []
                    for contract in contract_partner:
                        contract_date_end = self._convert_utc_to_timestamp_milliseconds(
                            datetime.combine(contract.date_end, datetime.min.time()))
                        can_booking = contract.date_end >= start_utc.date() and contract.state == 'activated' and (
                                    contract.available_session_number > 0 or contract.free_session_number > 0)
                        rs_ct = {
                            "coach_id": contract.coach_id.id or None,
                            "contract_id": contract.id,
                            "contract_name": contract.name,
                            "contract_service_name": contract.sale_order_template_name_print if contract.sale_order_template_name_print else None,
                            "contract_state": contract.state,
                            "contract_date_end": contract_date_end,
                            "contract_available_session_number": contract.available_session_number,
                            "contract_free_session_number": contract.free_session_number,
                            "can_booking": can_booking
                        }
                        contract_rs.append(rs_ct)

                    # Đẩy các hđ của pt lên đầu danh sách
                    contract_rs = sorted(
                        contract_rs,
                        key=lambda c: (c["coach_id"] != user_id)
                    )

                    # lấy thông tin partner
                    avatar = None
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', partner.id),
                         ('res_field', '=', 'image_1920')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                    rs_partner = {
                        "id": partner.id,
                        "name": partner.display_name,
                        "avatar": avatar,
                        "phone": partner.phone if partner.phone else None,
                        "contract": contract_rs
                    }
                    partner_data.append(rs_partner)
            return BaseResponse.success(data=partner_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api thêm khách hàng vào lịch đã tạo sẵn
    @http.route('/api/book_pt/addPartnerToEvent', type='http', auth='public', methods=['POST'], csrf=False)
    def add_partner_to_event(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            calendar_id = data.get('calendar_id')
            partners = data.get('partners')
            if not calendar_id or not partners:
                return BaseResponse.error(error_code=1,
                                          message="calendar_id, partners required")
            user_record = request.env['res.users'].sudo().browse(user_id)
            # check quyền có phải pt hay pt_manager không
            if not user_record.has_group('welly_base.group_pt') and not user_record.has_group('welly_base.group_pt_manager'):
                return BaseResponse.error(error_code=1,
                                          message="Tài khoản không có quyền thao tác lịch PT")
            event = request.env['calendar.event'].sudo().browse(int(calendar_id))

            for partner in partners:
                partner_id = partner.get('id')
                event.update({
                    'partner_ids': [(4, int(partner_id))]
                })
                attendee = request.env['calendar.attendee'].sudo().search([
                    ('partner_id', '=', int(partner_id)),
                    ('event_id', '=', int(event.id)),
                ], order='id ASC', limit=1)
                if attendee:
                    attendee.write({
                        'partner_id': partner_id,
                        'state': 'tentative',
                        'contract_id': partner.get('contract_id'),
                        'note': event.notes,
                    })
                    # tạo thông báo qua firebase cho khách hàng
                    notification_q = request.env['notification.queue'].sudo()
                    new_noti = notification_q.create({
                        'partner_id': partner_id,
                        'title': "Lịch tập đã được PT tạo cho Bạn",
                        'content_summary': f'{user_record.partner_id.display_name} đã tạo lịch PT cho Bạn. '
                                           f'Ca tập {self._convert_date_format(str(event.start + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")} - {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%H:%M")}'
                                           f' {self._convert_date_format(str(event.stop + timedelta(hours=7)), "%Y-%m-%d %H:%M:%S", "%d/%m/%Y")}',
                        'type': 'training',
                        'calendar_id': event.id
                    })
                    notification_q.send_notification_to_firebase(new_noti)
            return BaseResponse.success(data=event.id)
        except Exception as e:
            request.env.cr.rollback()
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra welly.exercise.form
    @http.route('/api/book_pt/getListExercise', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_exercise(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            location_id = data.get('location_id')
            if not location_id:
                return BaseResponse.error(error_code=1, message='location_id required')
            location = request.env['welly.location'].sudo().browse(int(location_id))
            welly_exercise = request.env['welly.exercise.form'].sudo().search_read(
                domain=[('company_id', '=', location.company_id.id)],
                fields=['id', 'name', 'limit_participants']
            )
            return BaseResponse.success(data=welly_exercise)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra calendar.event.class.type
    @http.route('/api/book_pt/getListSessionType', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_session_type(self, **kwargs):
        try:
            calendar_event_class = request.env['calendar.event.class.type'].sudo().search_read(
                fields=['id', 'name']
            )
            return BaseResponse.success(data=calendar_event_class)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/getListServiceType', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_service_type(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            location_id = data.get('location_id')
            if not location_id:
                return BaseResponse.error(error_code=1, message='location_id required')
            location = request.env['welly.location'].sudo().browse(int(location_id))
            welly_service_type = request.env['welly.service.type'].sudo().search_read(
                domain=[('company_id', '=', location.company_id.id)],
                fields=['id', 'name']
            )
            return BaseResponse.success(data=welly_service_type)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    @http.route('/api/book_pt/getListTag', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_tag(self, **kwargs):
        try:
            event_type = request.env['calendar.event.type'].sudo().search_read(
                fields=['id', 'name']
            )
            return BaseResponse.success(data=event_type)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy ra lịch tập của pt
    @http.route('/api/book_pt/getListPartner', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_partner(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            location_id = data.get('location_id')
            exercise_id = data.get('exercise_id')
            service_id = data.get('service_id')
            tag_ids = data.get('tag_ids')
            start = data.get('start')
            stop = data.get('stop')
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            if not location_id or not exercise_id or not service_id or not start or not stop:
                return BaseResponse.error(error_code=1, message='location_id, exercise_id, service_id, start, stop required')
            start_utc = self.convert_timestamp_milliseconds_to_utc(start)
            start_utc7 = start_utc + timedelta(hours=7)
            stop_utc7 = self.convert_timestamp_milliseconds_to_utc(stop) + timedelta(hours=7)
            start_time = start_utc7.strftime('%H:%M')
            start_float = float(start_time[:2]) + float(start_time[3:5]) / 60
            stop_time = stop_utc7.strftime('%H:%M')
            stop_float = float(stop_time[:2]) + float(stop_time[3:5]) / 60
            has_tag_show_f = False
            domain = []
            if tag_ids:
                show_f = request.env['calendar.event.type'].sudo().search([('name', '=', 'Show F')], limit=1).id
                if show_f and show_f == tag_ids:
                    has_tag_show_f = True
            if has_tag_show_f:
                domain_member_show_f = [
                    ('service_type', '=', 'member'),
                    # ('state', '=', 'activated'),
                    ('free_session_number', '>', 0),
                    # '|',
                    # ('coach_id', '=', user_id),
                    # ('coach_id', '=', False),
                    # ('date_end', '>=', start_utc),
                    ('checkin_time_from', '<=', start_float),
                    ('checkin_time_to', '>=', stop_float),
                    ('welly_location_many2_many', 'in', location_id),
                ]
                contracts = request.env['welly.contract'].sudo().search(domain_member_show_f)
                if not contracts:
                    domain_pt_show_f = [
                        ('service_type', '=', 'pt'),
                        # ('state', '=', 'activated'),
                        ('free_session_number', '>', 0),
                        ('exercise_form_id', '=',  exercise_id),
                        ('welly_service_ids', 'in', service_id),
                        # '|',
                        # ('coach_id', '=', user_id),
                        # ('coach_id', '=', False),
                        # ('date_end', '>=', start_utc),
                        ('checkin_time_from', '<=', start_float),
                        ('checkin_time_to', '>=', stop_float),
                        ('welly_location_many2_many', 'in', location_id),
                    ]
                    contracts_with_coach = request.env['welly.contract'].sudo().search(domain_pt_show_f + [('coach_id', '=', user_id)])
                    if contracts_with_coach:
                        contracts = contracts_with_coach
                    else:
                        contracts = request.env['welly.contract'].sudo().search(domain_pt_show_f)
            else:
                domain_not_show_f = [
                    # ('state', '=', 'activated'),
                    '|',
                    '&', ('service_type', '=', 'member'),
                    ('free_session_number', '>', 0),
                    ('service_type', '=', 'pt'),
                    '|',
                    '&',
                    ('service_type', '=', 'pt'),
                    ('exercise_form_id', '=', exercise_id),
                    '&',
                    ('service_type', '=', 'member'),
                    (1, '=', 1),
                    ('welly_service_ids', 'in', service_id),
                    # '|',
                    # ('coach_id', '=', user_id),
                    # ('coach_id', '=', False),
                    # ('date_end', '>=', start_utc),
                    ('checkin_time_from', '<=', start_float),
                    ('checkin_time_to', '>=', stop_float),
                    ('welly_location_many2_many', 'in', location_id),
                ]
                contracts = request.env['welly.contract'].sudo().search(domain_not_show_f)
            partner_ids = contracts.mapped('partner_account_ids.id')
            if not partner_ids:
                return BaseResponse.success(data=[])

            partners = request.env['res.partner'].sudo().search([('id', 'in', partner_ids)])
            partner_data = []
            base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')

            contracts_by_partner = {}
            # tạo dict(từ điển) contracts_by_partner giúp việc tìm ra hợp đồng của partner sẽ nhanh hơn dùng lambda hoặc fillter do ko phải gọi vào ORM lần nữa
            for contract in contracts:
                for partner in contract.partner_account_ids:
                    if partner.id not in contracts_by_partner:
                        contracts_by_partner[partner.id] = []
                    contracts_by_partner[partner.id].append(contract)
            for partner in partners:
                contract_partner = contracts_by_partner.get(partner.id, [])
                contract_rs = []
                for contract in contract_partner:
                    contract_date_end = self._convert_utc_to_timestamp_milliseconds(datetime.combine(contract.date_end, datetime.min.time()))
                    can_booking = contract_date_end >= start and contract.state == 'activated' and (contract.available_session_number > 0 or contract.free_session_number > 0 )
                    rs_ct = {
                        "coach_id": contract.coach_id.id or None,
                        "contract_id": contract.id,
                        "contract_name": contract.name,
                        "contract_service_name": contract.sale_order_template_name_print if contract.sale_order_template_name_print else None,
                        "contract_state": contract.state,
                        "contract_date_end": contract_date_end,
                        "contract_available_session_number": contract.available_session_number,
                        "contract_free_session_number": contract.free_session_number,
                        "can_booking": can_booking
                    }
                    contract_rs.append(rs_ct)

                # Đẩy các hđ của pt lên đầu danh sách
                contract_rs = sorted(
                    contract_rs,
                    key=lambda c: (c["coach_id"] != user_id)
                )

                avatar = None

                image = request.env['ir.attachment'].sudo().search(
                    [('res_model', '=', 'res.partner'), ('res_id', '=', partner.id),
                     ('res_field', '=', 'image_1920')], limit=1)
                if image:
                    access_token = image.generate_access_token()[0]
                    avatar = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                rs_partner = {
                    "id": partner.id,
                    "name": partner.display_name,
                    "avatar": avatar,
                    "phone": partner.phone if partner.phone else None,
                    "contract": contract_rs
                }
                partner_data.append(rs_partner)
            return BaseResponse.success(data=partner_data)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin contract
    @http.route('/api/user/contract/getContractInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_contract_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            partner_id = data.get('partner_id')
            contract_id = data.get('contract_id')
            if not contract_id or not partner_id:
                return BaseResponse.error(error_code=1, message="partner_id, contract_id required")

            contract_records = request.env['welly.contract'].sudo().search_read(
                domain=[('partner_account_ids', 'in', int(partner_id)),
                        ('id', '=', int(contract_id))],
                fields=['id', 'service_type', 'name', 'coach_id', 'welly_location_many2_many', 'state',
                        'partner_name_print',
                        'date_start',
                        'date_end',
                        'registration_form_name_print', 'pay_amount', 'pay_amount_to_text',
                        'sale_order_template_name_print', 'welly_gift_name_print', 'exercise_form_name_print',
                        'available_session_number', 'free_session_number', 'total_free_session_number', 'create_date',
                        'session_number']
            )
            if not contract_records:
                return BaseResponse.error(error_code=1, message="contract_records not exits")
            # lọc response
            for record in contract_records:
                record['date_start'] = str(record['date_start'])
                date_end = record['date_end']
                record['valid_time'] = None
                record['valid_time_type'] = None
                if date_end:
                    record['date_end'] = str(record['date_end'])
                    record['valid_time'] = self.convert_valid_time_type(date_end)
                    record['valid_time_type'] = "day"
                else:
                    record['date_end'] = None
                record['code'] = record['name']
                record.pop('name', None)

                record['coach_name'] = ""
                record['coach_image'] = ""
                if record['coach_id']:
                    record['coach_name'] = record['coach_id'][1]
                    record['coach_id'] = record['coach_id'][0]
                    record['coach_image'] = None
                    user = request.env['res.users'].sudo().browse(int(record['coach_id']))
                    base_url = request.env['ir.config_parameter'].sudo().get_param('web.base.url')
                    image = request.env['ir.attachment'].sudo().search(
                        [('res_model', '=', 'res.partner'), ('res_id', '=', user.partner_id.id),
                         ('res_field', '=', 'image_128')], limit=1)
                    if image:
                        access_token = image.generate_access_token()[0]
                        record['coach_image'] = f"{base_url}/web/image/{image.id}/image_128?access_token={access_token}"
                else:
                    record['coach_id'] = None
                record['location'] = []
                if len(record['welly_location_many2_many']) > 0:
                    welly_location = request.env['welly.location'].sudo()
                    for l in record['welly_location_many2_many']:
                        location = welly_location.search([('id', '=', int(l))])
                        record['location'].append(location.name)

                record.pop('welly_location_many2_many', None)

                record['partner_name'] = record['partner_name_print']
                record.pop('partner_name_print', None)

                record['registration_form_name'] = record['registration_form_name_print']
                record.pop('registration_form_name_print', None)

                record['name'] = record['sale_order_template_name_print']
                record.pop('sale_order_template_name_print', None)

                record['gift_name'] = record['welly_gift_name_print']
                record.pop('welly_gift_name_print', None)

                record['exercise_form_name'] = record['exercise_form_name_print'] if record[
                    'exercise_form_name_print'] else None
                record.pop('exercise_form_name_print', None)
                if record['service_type'] == 'member':
                    record['session_number'] = 0
                    record['available_session_number'] = 0
                total_available_session_number = record['available_session_number'] + record['free_session_number']
                record['total_session_number'] = record['session_number'] + record['total_free_session_number']
                record.pop('session_number', None)
                record['next_session_number'] = record['total_session_number'] - total_available_session_number + 1
                record['available_session_number'] = total_available_session_number
                record['create_date'] = self._convert_utc_to_timestamp_milliseconds((record['create_date']))
            return BaseResponse.success(data=contract_records[0])
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin hội viên và người liên hệ trong hợp đồng
    @http.route('/api/user/contract/getPartnerContractInfo', type='http', auth='public', methods=['POST'], csrf=False)
    def get_partner_contract_info(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            partner_id = data.get('partner_id')
            contract_id = data.get('contract_id')
            if not contract_id:
                return BaseResponse.error(error_code=1, message="partner_id, contract_id required")
            contract_records = request.env['welly.contract'].sudo().search(
                domain=[('partner_account_ids', 'in', int(partner_id)),
                        ('id', '=', int(contract_id))]
            )
            if not contract_records:
                return BaseResponse.error(error_code=1, message="Contract not found")
            # make response
            partner = contract_records.partner_id
            contact = contract_records.other_contact_id
            guardian = contract_records.guardian_id
            rs = {
                "partner": {
                    "partner_id": partner.id,
                    "partner_name": partner.name if partner.name else None,
                    "partner_code": partner.welly_code if partner.welly_code else None,
                    "partner_id_number": partner.partner_id_number if partner.partner_id_number else None,
                    "partner_birthdate": self.convert_date_format(
                        str(partner.birthdate)) if partner.birthdate else None,
                    "partner_gender": partner.gender if partner.gender else None,
                    "partner_email": partner.email if partner.email else None,
                    "partner_nationality": partner.nationality_id.name if partner.nationality_id.name else None,
                    "partner_phone": partner.phone if partner.phone else None,
                    "partner_address": partner.contact_address if partner.contact_address else None,
                },
                "contact": None,
                "guardian": None
            }
            if contact:
                rs['contact'] = {
                    "contact_name": contact.name if contact.name else None,
                    "contact_id_number": contact.partner_id_number if contact.partner_id_number else None,
                    "contact_address": contact.contact_address if contact.contact_address else None,
                    "contact_phone": contact.phone if contact.phone else None,
                    "contact_nationality": contact.nationality_id.name if contact.nationality_id.name else None
                }
            if guardian:
                rs['guardian'] = {
                    "guardian_name": guardian.name if guardian.name else None,
                    "guardian_id_number": guardian.nationality_id.name if guardian.nationality_id.name else None,
                    "guardian_address": guardian.contact_address if guardian.contact_address else None,
                    "guardian_phone": guardian.phone if guardian.phone else None,
                    "guardian_nationality": guardian.nationality_id.name if guardian.nationality_id.name else None
                }
            return BaseResponse.success(data=rs)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api lấy thông tin quyền lợi hội viên theo hđ
    @http.route('/api/user/contract/getServiceBenefit', type='http', auth='public', methods=['POST'], csrf=False)
    def get_contract_service_benefit(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            partner_id = data.get('partner_id')
            contract_id = data.get('contract_id')
            if not contract_id or not partner_id:
                return BaseResponse.error(error_code=1, message="contract_id, partner_id required")
            contract_records = request.env['welly.contract'].sudo().search(
                domain=[('partner_account_ids', 'in', int(partner_id)),
                        ('id', '=', int(contract_id))]
            )
            if not contract_records:
                return BaseResponse.error(error_code=1, message="Contract not found")
            # make response
            product_template = contract_records.sale_order_template_id.sale_order_template_line_ids
            pd = ''
            for line in product_template:
                product_note = line.product_id.welly_service_benefit or ''
                pd += product_note
            soup = BeautifulSoup(pd, 'html.parser')
            # Tìm tất cả các thẻ <li> và trích xuất nội dung của chúng
            result = [li.get_text() for li in soup.find_all('li')]
            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # api tạo action của pt với lịch tập
    @http.route('/api/book_pt/doActionEventForPT', type='http', auth='public', methods=['POST'], csrf=False)
    def do_pt_confirm_event(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            attende_id = data.get('attende_id')
            action = data.get('action')
            if not attende_id or not action:
                return BaseResponse.error(error_code=1, message="attende_id, action required")
            user_record = request.env['res.users'].sudo().browse(user_id)
            # check quyền có phải pt hay pt_manager không
            if not user_record.has_group('welly_base.group_pt') and not user_record.has_group('welly_base.group_pt_manager'):
                return BaseResponse.error(error_code=1,
                                          message="Tài khoản không có quyền thao tác")
            attendee = request.env['calendar.attendee'].sudo().browse(int(attende_id))
            if not attendee:
                return BaseResponse.error(error_code=1, message="Không tìm thấy lịch")
            event = attendee.event_id
            if not event:
                return BaseResponse.error(error_code=1, message="Không tìm thấy lịch")
            user_action = None
            pt = event.pt_id_substitute if event.pt_id_substitute else event.pt_id
            if user_id == pt.id:
                user_action = pt
            if not user_action:
                return BaseResponse.error(error_code=1, message="Không có quyền thao tác")
            if action == 'confirm':
                if self.check_can_confirm(attendee_state=attendee.state, event_state=event.state):
                    attendee.pt_confirm_button(user_action=user_action)
                elif self.check_can_re_confirm(attendee_state=attendee.state,
                                               event_state=event.state,
                                               user_id=user_id,
                                               pt_id=event.pt_id.id,
                                               pt_sub_id=event.pt_id_substitute.id if event.pt_id_substitute else None):
                    attendee.with_user(user_id).confirm_button(user_action=user_action)
            elif action == 'decline':
                attendee.with_user(user_id).pt_decline_button(user_action=user_action)
            elif action == 'delete':
                attendee.with_user(user_id).pt_delete_button(user_action=user_action)
            else:
                return BaseResponse.error(error_code=1, message="Action Không chính xác")
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API xóa lịch
    @http.route('/api/book_pt/deleteEvent', type='http', auth='public', methods=['POST'], csrf=False)
    def do_pt_delete_event(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            event_id = data.get('event_id')
            if not event_id:
                return BaseResponse.error(error_code=1, message="event_id required")
            user_record = request.env['res.users'].sudo().browse(user_id)
            event_record = request.env['calendar.event'].sudo().browse(event_id)
            if not event_record:
                return BaseResponse.error(error_code=1, message="Không tìm thấy lịch")
            event_record.with_user(user_record.id).unlink()
            return BaseResponse.success(data=[])
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API tính toán hiển thị màu chú thích cho màn lịch tập
    @http.route('/api/book_pt/calculateColorCalendarUser', type='http', auth='public', methods=['POST'], csrf=False)
    def calculate_color_booking_for_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            date_from = data.get('date_from')
            date_to = data.get('date_to')
            if not date_from or not date_to:
                return BaseResponse.error(error_code=1, message="date_from, date_to required")
            result = []
            date_format = "%d/%m/%Y"

            # Chuyển định dạng ngày từ input
            start_date_gmt7 = datetime.strptime(date_from, date_format) - timedelta(hours=7)

            # Lấy ra danh sách ngày trong khoảng từ date_from đến date_to
            current_date_gmt = datetime.strptime(date_from, date_format)
            end_date_gmt = datetime.strptime(date_to, date_format)
            while current_date_gmt <= end_date_gmt:
                date_str = current_date_gmt.strftime(date_format)
                domain = [
                    ('event_type', '=', 'pt'),
                    '|',
                    '&',
                    ('pt_id', '=', int(user_id)),
                    ('pt_id_substitute', '=', False),
                    ('pt_id_substitute', '=', int(user_id)),
                    ('start', '>=', start_date_gmt7),
                    ('start', '<=', start_date_gmt7 + timedelta(days=1)),
                ]
                # Tìm kiếm các sự kiện trong ngày
                events = request.env['calendar.event'].sudo().search(
                    domain=domain
                )
                color = 'gray'
                if events:
                    if any(event.state in ['draft', 'accepted'] for event in events):
                        color = 'orange'
                    elif all(event.state in ['during_practice', 'done']
                             for event in events):
                        color = 'green'
                    if all(event.state in ['reject', 'partner_skip_session'] for event in
                           events):
                        color = 'gray'

                result.append({'date': date_str, 'color': color})
                # Di chuyển đến ngày tiếp theo
                current_date_gmt += timedelta(days=1)
                start_date_gmt7 += timedelta(days=1)

            return BaseResponse.success(data=result)
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    # API đánh dấu đã đọc tất cả thông báo của user
    @http.route('/api/user/markAllNotificationsAsRead', type='http', auth='public', methods=['POST'], csrf=False)
    def mark_all_notifications_as_read(self, **kwargs):
        try:
            # Lấy user_id hoặc error từ header request
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            # Từ user_id lấy ra record user, từ record user lấy ra partner_id là id khách hàng
            user_record = request.env['res.users'].sudo().browse(user_id)
            partner_id = user_record.partner_id.id
            if not partner_id:
                return BaseResponse.error(error_code=1, message="Không thấy id khách hàng")
            # Lấy danh sách noti chưa đọc của khách hàng tương ứng trừ type training_telegram
            notifications_sent = request.env['notification.queue'].sudo().search(
                domain=[
                    ('partner_id', '=', int(partner_id)),
                    ('type', 'not in', ['training_telegram']),
                    ('is_read', '=', False)
                ]
            )
            # Đánh dấu các thông báo trong danh sách là đã đọc
            if notifications_sent:
                notifications_sent.write({'is_read': True})
            return BaseResponse.success()
        except Exception as e:
            logging.error(e)
            return BaseResponse.error(error_code=1, message=str(e))

    def _convert_utc_to_timestamp_milliseconds(self, utc_time):
        timestamp_seconds = utc_time.timestamp()
        timestamp_milliseconds = int(timestamp_seconds * 1000)
        return timestamp_milliseconds

    def convert_timestamp_milliseconds_to_utc(self, timestamp_milliseconds):
        timestamp_seconds = timestamp_milliseconds / 1000
        utc_time = datetime.utcfromtimestamp(timestamp_seconds)
        utc_time = utc_time.replace(tzinfo=timezone.utc)
        return utc_time

    # định dạng dd-MM-yyyy
    def convert_date_format(self, input_date):
        # Chuyển đổi định dạng từ chuỗi sang đối tượng datetime
        datetime_object = datetime.strptime(input_date, "%Y-%m-%d")

        # Chuyển đổi đối tượng datetime thành chuỗi mới với định dạng mong muốn
        formatted_date = datetime_object.strftime("%d-%m-%Y")

        return formatted_date

    # ví dụ: input_date: str time; original_format = "%Y-%m-%d"; destination_format= "%d-%m-%Y"
    def _convert_date_format(self, input_date, original_format, destination_format):
        datetime_object = datetime.strptime(input_date, original_format)
        formatted_date = datetime_object.strftime(destination_format)
        return formatted_date

    def convert_valid_time_type(self, date_end):
        current_date = fields.Date.today()
        date_end = fields.Date.from_string(date_end)
        valid_time = (date_end - current_date).days
        return valid_time

    # Check PT có thể confirm lịch của KH: Lịch ở trạng thái draft + KH có trạng thái [await_pt_confirm, needsAction]
    def check_can_confirm(self, attendee_state, event_state):
        return attendee_state in ['needsAction', 'await_pt_confirm'] and event_state == 'draft'

    # Check PT có thể từ chối lịch của KH:
    # Lịch ở trạng thái draft + KH có trạng thái [await_pt_confirm, needsAction, accepted]
    # hoặc Lịch ở trạng thái during_practice + KH có trạng thái [accepted, tentative]
    def check_can_decline(self, attendee_state, event_state):
        return (attendee_state in ['needsAction', 'await_pt_confirm', 'accepted', 'tentative'] and event_state == 'draft') or (attendee_state in ['accepted', 'tentative'] and event_state == 'during_practice')

    # Check PT có xác nhận lại lịch của KH sau khi từ chối:
    # Lịch ở trạng thái Nháp, Khách đã đến + KH có trạng thái Hủy
    def check_can_re_confirm(self, attendee_state, event_state, user_id, pt_id, pt_sub_id=None):
        pt_id_compare = pt_id if not pt_sub_id else pt_sub_id
        return attendee_state == 'declined' and event_state in ['draft', 'accepted'] and user_id == pt_id_compare

    ################## API CRM ######################

    # API lấy danh sách crm.stage (giai đoạn)
    @http.route('/api/crm/getListStage', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_crm_stage(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            stage_records = request.env['crm.stage'].sudo().search_read(
                fields=['id', 'name', 'is_won', 'stage_type']
            )
            for stage in stage_records:
                if not stage.get('stage_type'):
                    stage['stage_type'] = None
            return BaseResponse.success(data=stage_records)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))


    # API lấy danh sách utm.source (nguồn)
    @http.route('/api/crm/getListSource', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_crm_source(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            company_id = data.get('company_id')
            if not company_id:
                return BaseResponse.error(error_code=1, message="company_id required")
            source_records = request.env['utm.source'].sudo().search_read(
                domain=[('company_id', '=', int(company_id)), ('is_deleted', '=', False)],
                fields=['id', 'name']
            )
            return BaseResponse.success(data=source_records)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    #API lấy danh sách res.user có quyền tạo, nvkd, pt hỗ trợ của crm.lead
    @http.route('/api/crm/getListUser', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_crm_user(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)

            name = data.get('name')
            page = int(data.get('page', 1))
            size = int(data.get('size', 10))

            domain = [("active", "=", True)]
            if name:
                domain.append(('name', 'ilike', name))

            # Tính toán offset cho phân trang
            offset = (page - 1) * size

            # Đếm tổng số bản ghi thỏa mãn điều kiện
            total_records = request.env['res.users'].sudo().search_count(domain)
            total_page = (total_records + size - 1) // size

            # Tạo thông tin phân trang
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }
            # Query với offset và limit để phân trang
            user_records = request.env['res.users'].sudo().search_read(
                domain=domain,
                fields=['id', 'name'],
                offset=offset,
                limit=size,
                order='name asc'
            )

            return BaseResponse.success(data=user_records, index=index)

        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))


    # API lấy danh sách crm.lead (cơ hội)
    @http.route('/api/crm/getListCrmLead', type='http', auth='public', methods=['POST'], csrf=False)
    def get_list_crm_lead(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            user_rq = request.env['res.users'].sudo()
            crm_rq = request.env['crm.lead'].sudo()
            domain = [('is_deleted', '=', False)]
            # Giao cho (nvkd)
            nvkd_id = data.get('user_id')
            page = int(data.get('page', 1))
            size = int(data.get('size', 10))
            if nvkd_id:
                domain.append(('user_id', '=', int(nvkd_id)))
            # Tạo bởi
            create_uid = data.get('create_uid')
            if create_uid:
                domain.append(('create_uid', '=', int(create_uid)))
            # Theo dõi
            follow_id = data.get('follower_id')
            if follow_id:
                follow_user = user_rq.browse(int(follow_id))
                domain.append(('message_follower_ids.partner_id', '=', follow_user.partner_id.id))
            # PT hỗ trợ
            pt_id = data.get('user_pt_id')
            if pt_id:
                domain.append(('user_pt_id', '=', int(pt_id)))
            # Giai đoạn
            stage_id = data.get('stage_id')
            if stage_id:
                domain.append(('stage_id', '=', stage_id))

            # Trạng thái
            # Nếu state là won thì crm.lead có stage_id có trường is_won = True
            # Nếu state là lost thì crm.lead có lost_reason_id != False
            # Nếu state là archive thì crm.lead có active = False
            state = data.get('state')
            if state:
                # if state == 'won':
                #     stage = request.env['crm.stage'].sudo().search_read(
                #         domain=[('is_won', '=', True)],
                #         fields=['id'],
                #         limit=1
                #     )
                #     domain.append(('stage_id', '=', stage['id']))
                if state == 'lost':
                    domain.append(('lost_reason_id', '!=', False))
                if state == 'archived':
                    domain.append(('active', '=', False))
            # Nguồn
            source_id = data.get('source_id')
            if source_id:
                domain.append(('source_id', '=', int(source_id)))
            # Ngày tạo
            create_date_from = data.get('create_date_from')
            create_date_to = data.get('create_date_to')
            if create_date_from:
                create_from = self.convert_timestamp_milliseconds_to_utc(create_date_from)
                domain.append(('create_date', '>=', create_from))
            if create_date_to:
                create_to = self.convert_timestamp_milliseconds_to_utc(create_date_to)
                domain.append(('create_date', '<=', create_to))

            # Ngày đóng
            close_date_from = data.get('close_date_from')
            close_date_to = data.get('close_date_to')
            if close_date_from:
                close_from = self.convert_timestamp_milliseconds_to_utc(close_date_from)
                domain.append(('date_closed', '>=', close_from))
            if close_date_to:
                close_to = self.convert_timestamp_milliseconds_to_utc(close_date_to)
                domain.append(('date_closed', '<=', close_to))

            # Mặc định search đang k lấy ra các bản ghi bị lưu trữ, nếu muốn lấy ra hê thì phải thêm domain vào
            if not any(item == ('active', '=', False) for item in domain):
                domain = ['|', ('active', '=', True), ('active', '=', False)] + domain

            # Tính toán offset cho phân trang
            offset = (page - 1) * size

            # Đếm tổng số bản ghi thỏa mãn điều kiện
            total_records = crm_rq.search_count(domain)
            total_page = (total_records + size - 1) // size

            # Tạo thông tin phân trang
            index = {
                "page": page,
                "size": size,
                "total_page": total_page,
                "total_record": total_records
            }

            lead_records = crm_rq.search_read(
                domain=domain,
                fields=['id', 'partner_id', 'phone', 'stage_id', 'service_id'],
                offset=offset,
                limit=size,
                order='id desc'

            )

            if lead_records:
                for record in lead_records:
                    if record['partner_id']:
                        record['partner_name'] = record['partner_id'][1]
                        record['partner_id'] = record['partner_id'][0]
                    if record['service_id']:
                        record['service_name'] = record['service_id'][1]
                        record['service_id'] = record['service_id'][0]
                    if record['stage_id']:
                        record['stage_name'] = record['stage_id'][1]
                        record['stage_id'] = record['stage_id'][0]

            return BaseResponse.success(data=lead_records, index=index)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))

    # API lấy xem chi tiết ticket
    @http.route('/api/crm/getDetailCrmLead', type='http', auth='public', methods=['POST'], csrf=False)
    def get_detail_crm_lead(self, **kwargs):
        try:
            data = get_data_and_log_request(request)
            user_id, error = get_basic_authen_info_from_header()
            if error:
                return BaseResponse.error(error_code=401, message=error)
            
            lead_id = data.get('lead_id')
            if not lead_id:
                return BaseResponse.error(error_code=1, message="lead_id required")

            lead = request.env['crm.lead'].sudo().browse(int(lead_id))
            if not lead:
                return BaseResponse.error(error_code=1, message="Lead not found")

            result = {
                'id': lead.id,
                'name': lead.name,
                'partner_name': lead.partner_id.name if lead.partner_id else None,
                'phone': lead.phone,
                'stage_name': lead.stage_id.name if lead.stage_id else None,
                'can_edit': lead.stage_id.stage_type == 'create' if lead.stage_id else False,
                'source': lead.source_id.name if lead.source_id else None,
                'service_name': lead.service_id.name if lead.service_id else None,
                'priority': lead.priority if lead.priority else None,
                'sale': lead.user_id.name if lead.user_id else None,
                'pt_support': lead.user_pt_id.name if lead.user_pt_id else None,
                'description': lead.description if lead.description else None,
                'calendar_event_count': lead.calendar_event_count,
                'invoice_count': lead.invoice_count,
                'contract_count': lead.contract_count,
                'followers': [{
                    'id': follower.partner_id.id,
                    'name': follower.partner_id.name
                } for follower in lead.message_follower_ids if follower.partner_id],
                'messages': [{
                    'id': message.id,
                    'body': message.body,
                    'author_id': message.author_id.id if message.author_id else None,
                    'author_name': message.author_id.name if message.author_id else None,
                    'date': self._convert_utc_to_timestamp_milliseconds(message.date) if message.date else None,
                    'message_type': message.message_type,
                } for message in lead.message_ids]
            }

            return BaseResponse.success(data=result)
        except Exception as e:
            return BaseResponse.error(error_code=1, message=str(e))
