# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_vat
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-21 21:59+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Mexico) (https://app.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"10XXXXXXXXY or 20XXXXXXXXY or 15XXXXXXXXY or 16XXXXXXXXY or 17XXXXXXXXY"
msgstr "10XXXXXXXXY o 20XXXXXXXXY o 15XXXXXXXXY o 16XXXXXXXXY o 17XXXXXXXXY"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "1792060346001 or 1792060346"
msgstr "1792060346001 o 1792060346"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "310175397400003 [Fifteen digits, first and last digits should be \"3\"]"
msgstr "310175397400003 [Quince dígitos, el primero y el último deben ser \"3\"]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "49-098-576 or 49098576"
msgstr "49-098-576 o 49098576"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "XXXXXXXXX [9 digits] and it should respect the Luhn algorithm checksum"
msgstr ""
"XXXXXXXXX [9 dígitos] que deben cumplir con la suma de verificación del "
"algoritmo de Luhn."

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Los valores establecidos aquí"
" son específicos de la empresa.\" aria-label=\"Los valores establecidos aquí"
" son específicos de la empresa.\" groups=\"base.group_multi_company\" "
"role=\"img\"/>"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "AR200-5536168-2 or 20055361682"
msgstr "AR200-5536168-2 o 20055361682"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CHE-123.456.788 TVA or CHE-123.456.788 MWST or CHE-123.456.788 IVA"
msgstr "CHE-123.456.788 TVA o CHE-123.456.788 MWST o CHE-123.456.788 IVA"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "CO213123432-1 or CO213.123.432-1"
msgstr "CO213123432-1 o CO213.123.432-1"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: base_vat
#: model:ir.model,name:base_vat.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DE********8 or 12/345/67890"
msgstr "DE********8 o 12/345/67890"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "DO1-01-85004-3 or *********"
msgstr "DO1-01-85004-3 o *********"

#. module: base_vat
#: model:ir.model,name:base_vat.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "GB********2 or XI********2"
msgstr "GB********2 o XI********2"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "HU12345676 or ********-1-11 or **********"
msgstr "HU12345676 o ********-1-11 o **********"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid ""
"If this checkbox is ticked, you will not be able to save a contact if its "
"VAT number cannot be verified by the European VIES service."
msgstr ""
"Si esta casilla de verificación se encuentra seleccionada, no podrá guardar "
"un contacto si el NIF no es válido para el servicio Europeo VIES."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "MXGODE561231GR8 or GODE561231GR8"
msgstr "MXGODE561231GR8 o GODE561231GR8"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "TR********90 (VERGINO) or TR17291716060 (TCKIMLIKNO)"
msgstr "TR********90 (VERGINO) o TR17291716060 (TCKIMLIKNO)"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_partner__vies_failed_message
#: model:ir.model.fields,field_description:base_vat.field_res_users__vies_failed_message
msgid "Technical field display a message to the user if the VIES check fails."
msgstr ""
"Campo técnico que muestra un mensaje al usuario si no se cumple con la "
"verificación VIES."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"El número %(vat_label)s [%(wrong_vat)s] no parece ser válido. \n"
"Tome en cuenta que el formato esperado es %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(record_label)s does not seem to be valid. \n"
"Note: the expected format is %(expected_format)s"
msgstr ""
"El número %(vat_label)s [%(wrong_vat)s] para %(record_label)s no parece ser válido.\n"
"Tome en cuenta que el formato esperado es %(expected_format)s"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "The VAT number %s failed the VIES VAT validation check."
msgstr ""
"El número de identificación fiscal %s no pasó la verificación de validación "
"VIES VAT."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country detected for this foreign VAT number does not match any of the "
"countries composing the country group set on this fiscal position."
msgstr ""
"El país detectado para este número de identificación extranjero no coincide "
"con ninguno de los países del grupo de países establecido en esta posición "
"fiscal."

#. module: base_vat
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid ""
"The country of the foreign VAT number could not be detected. Please assign a"
" country to the fiscal position or set a country group"
msgstr ""
"No se pudo detectar el país del número de identificación extranjero. Asigne "
"un país a la posición fiscal o establezca un grupo de países."

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "VAT"
msgstr "RFC"

#. module: base_vat
#: model:ir.model.fields,field_description:base_vat.field_res_company__vat_check_vies
#: model:ir.model.fields,field_description:base_vat.field_res_config_settings__vat_check_vies
msgid "Verify VAT Numbers"
msgstr "Comprobar números de IVA"

#. module: base_vat
#: model_terms:ir.ui.view,arch_db:base_vat.res_config_settings_view_form
msgid "Verify VAT numbers using the European VIES service"
msgstr "Comprobar un número de IVA con el sistema europeo VIES"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "either 11 digits for CPF or 14 digits for CNPJ"
msgstr "11 dígitos para CPF o 14 dígitos para CNPJ"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/account_fiscal_position.py:0
#, python-format
msgid "fiscal position [%s]"
msgstr "posición fiscal [%s]"

#. module: base_vat
#. odoo-python
#: code:addons/base_vat/models/res_partner.py:0
#, python-format
msgid "partner [%s]"
msgstr "partner [%s]"
