<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_salary_component_view_tree" model="ir.ui.view">
        <field name="name">hr_salary_component_view_tree</field>
        <field name="model">hr.salary.component</field>
        <field name="arch" type="xml">
            <tree delete="0">
                <field name="name"/>
                <field name="code"/>
                <field name="is_allowance"/>
                <field name="is_outside_salary"/>
                <field name="calculation_type"/>
            </tree>
        </field>
    </record>

    <record id="hr_salary_component_view_form" model="ir.ui.view">
        <field name="name">hr_salary_component_view_form</field>
        <field name="model">hr.salary.component</field>
        <field name="arch" type="xml">
            <form delete="0">
                <field name="active" invisible="1"/>
                <sheet>
                    <widget name="web_ribbon" title="<PERSON>ưu trữ" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Ví dụ: Trợ cấp điện thoại"/>
                        </h1>
                    </div>
                    <group>
                        <field name="id" invisible="1"/>
                        <field name="code" placeholder="Ví dụ: phone_allowance" attrs="{'readonly': [('id', '!=', False)]}"/>
                    </group>
                    <group>
                        <group>
                            <field name="is_allowance"/>
                            <field name="calculation_type"/>
                            <field name="description"/>
                        </group>
                        <group>
                            <field name="is_outside_salary"/>
                            <field name="apply_for"/>
                            <field name="hr_job_ids"
                                   attrs="{'invisible': [('apply_for', '!=', 'job')],
                                           'required': [('apply_for', '=', 'job')]}"
                                   widget="many2many_tags"
                                   options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="hr_level_ids"
                                   attrs="{'invisible': [('apply_for', '!=', 'level')],
                                           'required': [('apply_for', '=', 'level')]}"
                                   widget="many2many_tags"
                                   options="{'no_create': True, 'no_create_edit':True}"/>
                            <field name="hr_employee_ids"
                                   attrs="{'invisible': [('apply_for', '!=', 'employee')],
                                           'required': [('apply_for', '=', 'employee')]}"
                                   widget="many2many_tags"
                                   options="{'no_create': True, 'no_create_edit':True}"/>
                        </group>
                    </group>

                    <!-- Giá trị áp dụng cho nhân viên -->
                    <group string="Giá trị áp dụng" attrs="{'invisible': [('apply_for', '!=', 'employee')]}">
                        <field name="currency_id" invisible="1"/>
                        <field name="default_value" attrs="{'required': [('apply_for', '=', 'employee')]}"/>
                    </group>

                    <!-- Bảng giá trị áp dụng theo chức vụ -->
                    <group string="Giá trị áp dụng theo chức vụ" attrs="{'invisible': [('apply_for', '!=', 'job')]}">
                        <field name="job_value_ids" nolabel="1">
                            <tree editable="bottom">
                                <field name="job_id" readonly="1"/>
                                <field name="value"/>
                                <field name="currency_id" invisible="1"/>
                            </tree>
                        </field>
                    </group>

                    <!-- Bảng giá trị áp dụng theo cấp bậc -->
                    <group string="Giá trị áp dụng theo cấp bậc" attrs="{'invisible': [('apply_for', '!=', 'level')]}">
                        <field name="level_value_ids" nolabel="1">
                            <tree editable="bottom">
                                <field name="level_id" readonly="1"/>
                                <field name="value"/>
                                <field name="currency_id" invisible="1"/>
                            </tree>
                        </field>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" options="{'open_attachments': True}"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <record id="hr_salary_component_view_search" model="ir.ui.view">
        <field name="name">hr_salary_component_view_search</field>
        <field name="model">hr.salary.component</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="code"/>
                <filter string="Phụ cấp" name="allowances" domain="[('is_allowance', '=', True)]"/>
                <filter string="Ngoài lương" name="outside_salary" domain="[('is_outside_salary', '=', True)]"/>
                <filter string="Lưu trữ" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0">
                    <filter string="Quy cách tính" name="group_by_type" context="{'group_by': 'calculation_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_hr_salary_component" model="ir.actions.act_window">
        <field name="name">Thành phần lương</field>
        <field name="res_model">hr.salary.component</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="hr_salary_component_view_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo mới thành phần lương
            </p>
        </field>
    </record>

    <menuitem
            id="menu_hr_salary_component"
            name="Thành phần lương"
            parent="menu_welly_salary_configuration"
            action="action_hr_salary_component"
            sequence="10"/>

</odoo>
