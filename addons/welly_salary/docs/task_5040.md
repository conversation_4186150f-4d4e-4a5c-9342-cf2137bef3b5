# Task #5040 - <PERSON><PERSON><PERSON> mịn module <PERSON><PERSON>ơng và thưởng

## Description
Thực hiện các cải tiến cho module Lương và thưởng bao gồm:

1. **[Bảng lương\ Kỳ lương] Mặc định áp dụng ngày công chuẩn cho tất cả phòng ban**
   - <PERSON><PERSON>y công chuẩn ca linh hoạt\ Phòng ban áp dụng: với line đầu tiên, mặc định Phòng ban áp dụng = tất cả các phòng ban

2. **[Nhân viên] Bỏ Khối nhân sự**
   - Bỏ danh mục Nhân viên\ Cấu hình\ Khối nhân sự
   - Bỏ trường **Kh<PERSON>i nhân sự** trong model hr.employee

3. **[Nhân viên\ Điều chỉnh lương] Tự động tính **Lương hiệu quả****
   - Tự động tính **Lương hiệu quả** = <PERSON><PERSON><PERSON> lương theo hợp đồng - <PERSON><PERSON><PERSON><PERSON> cơ bản - <PERSON><PERSON> cấp trong lương

## Analysis

### 1. <PERSON><PERSON><PERSON> công chuẩn ca linh hoạt - Mặc định tất cả phòng ban
**Hiện tại**: Khi tạo dòng mới trong `hr.salary.flexible.workday`, người dùng phải chọn phòng ban áp dụng thủ công.
**Yêu cầu**: Dòng đầu tiên tự động có tất cả phòng ban được chọn mặc định.

**Files liên quan**:
- `addons/welly_salary/models/hr_salary_period.py` - Model `HrSalaryFlexibleWorkday`
- `addons/welly_salary/views/hr_salary_period_views.xml` - View form kỳ lương

### 2. Bỏ Khối nhân sự
**Hiện tại**: 
- Model `hr.business.unit` tồn tại trong `addons/welly_salary/models/meta_data.py`
- Trường `hr_business_unit_id` trong model `hr.employee`
- Menu "Khối nhân sự" trong cấu hình HR

**Yêu cầu**: Loại bỏ hoàn toàn tính năng này.

**Files liên quan**:
- `addons/welly_salary/models/meta_data.py` - Model `HrBusinessUnit`
- `addons/welly_salary/models/hr_employee.py` - Trường `hr_business_unit_id`
- `addons/welly_salary/views/meta_data_views.xml` - Views và menu
- `addons/welly_salary/views/hr_employee_views.xml` - Trường trong form nhân viên

### 3. Tự động tính Lương hiệu quả
**Hiện tại**: Lương hiệu quả được tính trong method `_compute_salary_info`
**Yêu cầu**: Cập nhật công thức tính = Mức lương theo hợp đồng - Lương cơ bản - Phụ cấp trong lương

**Files liên quan**:
- `addons/welly_salary/models/hr_employee.py` - Method `_compute_salary_info`
- `addons/welly_salary/wizard/hr_salary_adjust_wizard.py` - Wizard điều chỉnh lương

## Task Breakdown Checklist

### Task 1: Mặc định phòng ban cho ngày công chuẩn ca linh hoạt
- [x] 1.1: Tìm hiểu cách lấy tất cả phòng ban trong hệ thống
- [x] 1.2: Cập nhật model `HrSalaryFlexibleWorkday` để mặc định chọn tất cả phòng ban cho dòng đầu tiên
- [x] 1.3: Kiểm tra và cập nhật logic validation nếu cần
- [ ] 1.4: Test tính năng trên giao diện

### Task 2: Loại bỏ Khối nhân sự
- [x] 2.1: Xóa model `HrBusinessUnit` khỏi `meta_data.py`
- [x] 2.2: Xóa trường `hr_business_unit_id` khỏi model `hr.employee`
- [x] 2.3: Xóa views và menu liên quan đến Khối nhân sự
- [x] 2.4: Xóa trường khỏi form nhân viên
- [x] 2.5: Cập nhật file `__init__.py` nếu cần (không cần thay đổi)
- [x] 2.6: Xóa access rule trong security/ir.model.access.csv

### Task 3: Cập nhật công thức tính Lương hiệu quả
- [x] 3.1: Phân tích method `_compute_salary_info` hiện tại
- [x] 3.2: Cập nhật công thức tính effective_salary
- [x] 3.3: Kiểm tra wizard điều chỉnh lương có cần cập nhật không
- [ ] 3.4: Test tính năng với các trường hợp khác nhau

## Implementation Plan

1. **Bước 1**: Thực hiện Task 2 (Loại bỏ Khối nhân sự) trước vì đây là thay đổi lớn nhất
2. **Bước 2**: Thực hiện Task 3 (Cập nhật công thức lương hiệu quả)
3. **Bước 3**: Thực hiện Task 1 (Mặc định phòng ban) cuối cùng

## Dependencies
- Module `welly_salary`
- Model `hr.employee`, `hr.department`
- Không có dependency với module khác

## Manual Testcase

### Testcase 1: Kiểm tra Khối nhân sự đã bị xóa
- [ ] 1.1: Vào menu Nhân viên > Cấu hình > Kiểm tra không còn menu "Khối nhân sự"
- [ ] 1.2: Vào form nhân viên, kiểm tra không còn trường "Khối nhân sự"
- [ ] 1.3: Kiểm tra không có lỗi khi mở form nhân viên

### Testcase 2: Kiểm tra tính toán Lương hiệu quả tự động
- [ ] 2.1: Vào form nhân viên > Điều chỉnh lương
- [ ] 2.2: Nhập "Mức lương theo hợp đồng" = 10,000,000
- [ ] 2.3: Nhập "Lương cơ bản" = 5,000,000
- [ ] 2.4: Thêm phụ cấp trong lương (ví dụ: 1,000,000)
- [ ] 2.5: Kiểm tra "Lương hiệu quả" tự động = 10,000,000 - 5,000,000 - 1,000,000 = 4,000,000
- [ ] 2.6: Thay đổi các giá trị và kiểm tra lương hiệu quả cập nhật tự động

### Testcase 3: Kiểm tra mặc định phòng ban cho ngày công chuẩn
- [ ] 3.1: Vào menu Bảng lương > Cấu hình > Kỳ lương
- [ ] 3.2: Tạo kỳ lương mới
- [ ] 3.3: Thêm dòng đầu tiên trong "Ngày công chuẩn ca linh hoạt"
- [ ] 3.4: Kiểm tra trường "Phòng ban áp dụng" tự động chọn tất cả phòng ban
- [ ] 3.5: Thêm dòng thứ hai, kiểm tra không tự động chọn phòng ban

## Triển khai hoàn thành

### Các thay đổi đã thực hiện:

**Files đã chỉnh sửa:**
1. `addons/welly_salary/models/hr_employee.py` - Xóa trường hr_business_unit_id
2. `addons/welly_salary/models/meta_data.py` - Xóa model HrBusinessUnit
3. `addons/welly_salary/models/hr_salary_period.py` - Thêm logic mặc định phòng ban
4. `addons/welly_salary/views/hr_employee_views.xml` - Xóa trường khỏi form
5. `addons/welly_salary/views/meta_data_views.xml` - Xóa views và menu
6. `addons/welly_salary/wizard/hr_salary_adjust_wizard.py` - Thêm tính toán lương hiệu quả
7. `addons/welly_salary/wizard/hr_salary_adjust_wizard_views.xml` - Đặt readonly
8. `addons/welly_salary/security/ir.model.access.csv` - Xóa access rule

**Tính năng mới:**
- Tự động tính lương hiệu quả trong wizard điều chỉnh lương
- Tự động chọn tất cả phòng ban cho dòng đầu tiên ngày công chuẩn ca linh hoạt
- Loại bỏ hoàn toàn tính năng Khối nhân sự

**Cần restart Odoo server để áp dụng các thay đổi.**

## Notes
- Cần backup dữ liệu trước khi xóa model `hr.business.unit`
- Kiểm tra xem có module nào khác sử dụng `hr.business.unit` không
- Test kỹ công thức tính lương hiệu quả với nhiều trường hợp
