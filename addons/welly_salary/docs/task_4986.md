# Task #4986 - [<PERSON><PERSON><PERSON> viên\ <PERSON><PERSON><PERSON> hình\<PERSON>ức vụ] C<PERSON>i thiện UX

## Description
Cải thiện UX cho chức năng Nhân viên\ C<PERSON>u hình\ <PERSON><PERSON><PERSON> vụ (Model hr.job) với các yêu cầu:

### Y<PERSON><PERSON> cầu cho các tab cấu hình:
- **<PERSON><PERSON> chế hoa hồng bán** (hr.job.sale.commission)
- **<PERSON><PERSON> chế hoa hồng dạy** (hr.job.pt.commission) 
- **<PERSON><PERSON> số lương K** (hr.job.k.coefficient)

### Y<PERSON><PERSON> cầu cụ thể:
1. **<PERSON><PERSON>c giá trị cấu hình phải liên tục** (không được ngắt quãng)
   - V<PERSON> dụ liên tục: Từ 0-50, Từ 50-100, Từ 100-150, Trên 150
   - <PERSON><PERSON> dụ không liên tục: Từ 0-50, <PERSON><PERSON> 100-150, <PERSON><PERSON><PERSON><PERSON> 150 (thi<PERSON>u 50-100)

2. **<PERSON><PERSON> động sắp xếp theo <PERSON>i<PERSON> trị bắt đầu thật sự chứ không phải là begin_value tăng dần** sau khi lưu
   - Hiện tại: Nhập không đúng thứ tự sẽ báo lỗi trùng nhau
   - Yêu cầu: Tự động sắp xếp nếu không vi phạm điều kiện

## Analysis

### Models liên quan:
1. **hr.job.sale.commission** - Cơ chế hoa hồng bán
   - Trường: begin_value, end_value, begin_op, end_op
   - Constraint hiện tại: `_check_hr_job_sale_commission_line_ids`

2. **hr.job.pt.commission** - Cơ chế hoa hồng dạy  
   - Trường: begin_value, end_value, begin_op, end_op
   - Constraint hiện tại: `_check_hr_job_pt_commission_line_ids`

3. **hr.job.k.coefficient** - Hệ số lương K
   - Trường: begin_value, end_value, begin_op, end_op
   - Constraint hiện tại: Chưa có trong hr.job

### Vấn đề hiện tại:
1. **Logic validation chỉ kiểm tra trùng lặp**, không kiểm tra tính liên tục
2. **Không tự động sắp xếp** theo thứ tự giá trị bắt đầu
3. **UX không tốt** khi nhập không đúng thứ tự

### Files cần chỉnh sửa:
- `addons/welly_payroll/models/hr_job.py` - Constraint cho sale commission và pt commission
- `addons/welly_salary/models/hr_job.py` - Thêm constraint cho k coefficient  
- `addons/welly_payroll/models/hr_job_commission.py` - Logic validation
- `addons/welly_salary/models/hr_job_k_coefficient.py` - Logic validation

## Task Breakdown Checklist

### Task 1: Phân tích và thiết kế logic kiểm tra tính liên tục
- [x] 1.1: Nghiên cứu logic hiện tại của method `check_commission_range`
- [x] 1.2: Thiết kế algorithm kiểm tra tính liên tục của các khoảng giá trị
- [x] 1.3: Thiết kế logic tự động sắp xếp theo begin_value

### Task 2: Cập nhật model hr.job.sale.commission
- [x] 2.1: Cập nhật constraint `_check_hr_job_sale_commission_line_ids`
- [x] 2.2: Thêm method kiểm tra tính liên tục
- [x] 2.3: Thêm method tự động sắp xếp
- [ ] 2.4: Test với các trường hợp khác nhau

### Task 3: Cập nhật model hr.job.pt.commission
- [x] 3.1: Cập nhật constraint `_check_hr_job_pt_commission_line_ids`
- [x] 3.2: Thêm method kiểm tra tính liên tục
- [x] 3.3: Thêm method tự động sắp xếp
- [ ] 3.4: Test với các trường hợp khác nhau

### Task 4: Cập nhật model hr.job.k.coefficient
- [x] 4.1: Thêm constraint trong hr.job cho k_coefficient_line_ids
- [x] 4.2: Thêm method kiểm tra tính liên tục
- [x] 4.3: Thêm method tự động sắp xếp
- [ ] 4.4: Test với các trường hợp khác nhau

### Task 5: Tạo helper method chung
- [x] 5.1: Tạo mixin hoặc helper method cho logic chung
- [x] 5.2: Refactor code để tránh duplicate
- [x] 5.3: Đảm bảo tính nhất quán giữa các model

## Implementation Plan

1. **Bước 1**: Tạo helper method chung cho logic kiểm tra tính liên tục
2. **Bước 2**: Cập nhật từng model theo thứ tự: sale commission → pt commission → k coefficient
3. **Bước 3**: Test tổng thể và tối ưu hóa

## Dependencies
- Module `welly_payroll` (sale commission, pt commission)
- Module `welly_salary` (k coefficient)
- Model `hr.job`

## Manual Testcase

### Testcase 1: Kiểm tra tính liên tục - Hệ số lương K
- [ ] 1.1: Vào Nhân viên > Cấu hình > Chức vụ > Chọn 1 chức vụ
- [ ] 1.2: Vào tab "Hệ số lương K"
- [ ] 1.3: Thêm dòng 1: Từ 0 đến 50, hệ số 1.0
- [ ] 1.4: Thêm dòng 2: Từ 100 đến 150, hệ số 1.5 (bỏ trống 50-100)
- [ ] 1.5: Lưu và kiểm tra hiển thị lỗi "phải liên tục (không được có khoảng trống)"
- [ ] 1.6: Sửa dòng 2 thành: Từ 50 đến 100, lưu thành công

### Testcase 2: Kiểm tra tự động sắp xếp - Cơ chế hoa hồng bán
- [ ] 2.1: Vào tab "Cơ chế hoa hồng bán"
- [ ] 2.2: Thêm dòng 1: >= 100 AND <= 150, commission 15%
- [ ] 2.3: Thêm dòng 2: < 50, commission 5%
- [ ] 2.4: Thêm dòng 3: >= 50 AND < 100, commission 10%
- [ ] 2.5: Lưu và kiểm tra tự động sắp xếp theo giá trị bắt đầu thật sự:
  - Dòng 1: < 50 (bắt đầu từ -∞)
  - Dòng 2: >= 50 AND < 100 (bắt đầu từ 50)
  - Dòng 3: >= 100 AND <= 150 (bắt đầu từ 100)

### Testcase 3: Kiểm tra trùng lặp - Cơ chế hoa hồng dạy
- [ ] 3.1: Vào tab "Cơ chế hoa hồng dạy"
- [ ] 3.2: Thêm dòng 1: Từ 0 đến 60, commission 10%
- [ ] 3.3: Thêm dòng 2: Từ 50 đến 100, commission 15% (trùng 50-60)
- [ ] 3.4: Lưu và kiểm tra hiển thị lỗi "không được trùng lặp"
- [ ] 3.5: Sửa dòng 2 thành: Từ 60 đến 100, lưu thành công

### Testcase 4: Kiểm tra sắp xếp phức tạp - Hệ số lương K
- [ ] 4.1: Vào tab "Hệ số lương K"
- [ ] 4.2: Thêm các dòng không theo thứ tự:
  - Dòng 1: > 200, hệ số 2.0
  - Dòng 2: = 0, hệ số 0.5
  - Dòng 3: > 0 AND <= 100, hệ số 1.0
  - Dòng 4: > 100 AND <= 200, hệ số 1.5
- [ ] 4.3: Lưu và kiểm tra tự động sắp xếp theo giá trị bắt đầu thật sự:
  - Dòng 1: = 0 (bắt đầu từ 0)
  - Dòng 2: > 0 AND <= 100 (bắt đầu từ 0.000001)
  - Dòng 3: > 100 AND <= 200 (bắt đầu từ 100.000001)
  - Dòng 4: > 200 (bắt đầu từ 200.000001)

### Testcase 5: Kiểm tra handle sequence
- [ ] 5.1: Tạo 3 dòng liên tục trong bất kỳ tab nào
- [ ] 5.2: Kiểm tra có icon handle (≡) để kéo thả
- [ ] 5.3: Kéo thả để thay đổi thứ tự
- [ ] 5.4: Lưu và kiểm tra thứ tự được duy trì

## Notes
- Cần đảm bảo backward compatibility với dữ liệu hiện có
- Logic sắp xếp chỉ áp dụng khi không có lỗi validation
- Cần test kỹ với nhiều trường hợp edge case
