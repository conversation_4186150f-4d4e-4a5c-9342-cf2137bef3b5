# Task #5069 - [<PERSON><PERSON><PERSON> lươ<PERSON>\ Thành phần lương] <PERSON><PERSON> sung cấu hình giá trị áp dụng trong thành phần lương

## Description
<PERSON><PERSON> sung cấu hình giá trị áp dụng cho thành phần lương theo từng Cấp bậc/<PERSON><PERSON><PERSON> viên/Ch<PERSON><PERSON> vụ.

### Hiện tại:
- Thành phần lương chỉ có thể cấu hình áp dụng cho nhóm đối tượ<PERSON> (nhân viên, chứ<PERSON> vụ, cấp bậc)
- <PERSON><PERSON><PERSON><PERSON> thể cấu hình giá trị cụ thể cho từng đối tượng

### Mong muốn:
- V<PERSON>i các thành phần lương là <PERSON> cấp, cho phép cấu hình trực tiếp giá trị áp dụng cho các vị trí công việc
- Tự động cập nhật giá trị vào Properties fields của hr.job và hr.level
- Tự động insert thành phần lương vào nhân viên khi áp dụng theo nhân viên

## Analysis

### 1. Trường hợp áp dụng cho Nhân viên (apply_for = 'employee')
**Yêu cầu**:
- Bổ sung trường **Giá trị áp dụng** (Monetary, mặc định = 0)
- Tự động insert thành phần lương vào Nhân viên\ tab Lương
- Khi điều chỉnh lương: giá trị mặc định lấy từ **Giá trị áp dụng**

**Files cần chỉnh sửa**:
- `models/hr_salary_component.py`: Thêm trường `default_value`
- `views/hr_salary_component_views.xml`: Thêm trường vào form
- Logic tự động insert vào nhân viên

### 2. Trường hợp áp dụng cho Chức vụ (apply_for = 'job')
**Yêu cầu**:
- Bổ sung bảng **Giá trị áp dụng** với 2 trường: Chức vụ, Giá trị
- Tự động gen theo danh sách Chức vụ được áp dụng
- Tự động update về hr.job\ tab Phụ cấp trong lương/ngoài lương

**Files cần chỉnh sửa**:
- Tạo model mới: `hr.salary.component.job.value`
- Logic sync với Properties fields của hr.job

### 3. Trường hợp áp dụng cho Cấp bậc (apply_for = 'level')
**Yêu cầu**:
- Bổ sung bảng **Giá trị áp dụng** với 2 trường: Cấp bậc, Giá trị  
- Tự động gen theo danh sách Cấp bậc được áp dụng
- Tự động update về hr.level\ tab Phụ cấp trong lương/ngoài lương

**Files cần chỉnh sửa**:
- Tạo model mới: `hr.salary.component.level.value`
- Logic sync với Properties fields của hr.level

### Models hiện tại liên quan:
1. **hr.salary.component**: Model chính cần mở rộng
2. **hr.job**: Có Properties fields cho phụ cấp trong/ngoài lương
3. **hr.level**: Có Properties fields cho phụ cấp trong/ngoài lương
4. **hr.employee**: Cần logic tự động insert thành phần lương

## Task Breakdown Checklist

### Task 1: Cập nhật model hr.salary.component cho trường hợp nhân viên
- [x] 1.1: Thêm trường `default_value` (Monetary) cho apply_for = 'employee'
- [x] 1.2: Cập nhật view form để hiển thị trường mới
- [ ] 1.3: Thêm logic tự động insert vào nhân viên khi tạo/cập nhật
- [ ] 1.4: Test tính năng với nhân viên

### Task 2: Tạo model cho giá trị áp dụng theo chức vụ
- [x] 2.1: Tạo model `hr.salary.component.job.value`
- [x] 2.2: Thêm One2many field vào hr.salary.component
- [x] 2.3: Tạo view tree/form cho model mới
- [x] 2.4: Logic tự động gen records theo hr_job_ids
- [x] 2.5: Logic sync với Properties fields của hr.job

### Task 3: Tạo model cho giá trị áp dụng theo cấp bậc
- [x] 3.1: Tạo model `hr.salary.component.level.value`
- [x] 3.2: Thêm One2many field vào hr.salary.component
- [x] 3.3: Tạo view tree/form cho model mới
- [x] 3.4: Logic tự động gen records theo hr_level_ids
- [x] 3.5: Logic sync với Properties fields của hr.level

### Task 4: Cập nhật giao diện theo mockup
- [x] 4.1: Cập nhật form view để hiển thị bảng giá trị áp dụng
- [x] 4.2: Ẩn/hiện các trường theo apply_for
- [x] 4.3: Đảm bảo UX tốt và dễ sử dụng

### Task 5: Logic đồng bộ và validation
- [x] 5.1: Đảm bảo sync đúng với Properties fields
- [x] 5.2: Validation dữ liệu hợp lệ
- [ ] 5.3: Test tổng thể các trường hợp

## Implementation Plan

1. **Bước 1**: Cập nhật model hr.salary.component cho trường hợp nhân viên
2. **Bước 2**: Tạo model cho giá trị áp dụng theo chức vụ
3. **Bước 3**: Tạo model cho giá trị áp dụng theo cấp bậc
4. **Bước 4**: Cập nhật giao diện và logic đồng bộ
5. **Bước 5**: Test tổng thể và tối ưu hóa

## Dependencies
- Module `welly_salary`
- Model `hr.salary.component`, `hr.job`, `hr.level`, `hr.employee`
- Properties fields system

## Manual Testcase

### Testcase 1: Kiểm tra giá trị áp dụng cho nhân viên
- [ ] 1.1: Vào Bảng lương > Cấu hình > Thành phần lương
- [ ] 1.2: Tạo thành phần lương mới với "Áp dụng cho" = "Nhân viên"
- [ ] 1.3: Kiểm tra hiển thị trường "Giá trị áp dụng"
- [ ] 1.4: Nhập giá trị 500,000 và chọn một số nhân viên
- [ ] 1.5: Lưu và kiểm tra thành phần lương được tự động thêm vào nhân viên

### Testcase 2: Kiểm tra bảng giá trị áp dụng theo chức vụ
- [ ] 2.1: Tạo thành phần lương mới với "Áp dụng cho" = "Chức vụ"
- [ ] 2.2: Chọn 3 chức vụ khác nhau
- [ ] 2.3: Kiểm tra bảng "Giá trị áp dụng theo chức vụ" tự động hiển thị 3 dòng
- [ ] 2.4: Nhập giá trị khác nhau cho mỗi chức vụ (100k, 200k, 300k)
- [ ] 2.5: Lưu và kiểm tra Properties fields của hr.job được cập nhật

### Testcase 3: Kiểm tra bảng giá trị áp dụng theo cấp bậc
- [ ] 3.1: Tạo thành phần lương mới với "Áp dụng cho" = "Cấp bậc"
- [ ] 3.2: Chọn 3 cấp bậc khác nhau
- [ ] 3.3: Kiểm tra bảng "Giá trị áp dụng theo cấp bậc" tự động hiển thị 3 dòng
- [ ] 3.4: Nhập giá trị khác nhau cho mỗi cấp bậc
- [ ] 3.5: Lưu và kiểm tra Properties fields của hr.level được cập nhật

### Testcase 4: Kiểm tra tự động sync khi thay đổi danh sách áp dụng
- [ ] 4.1: Với thành phần lương áp dụng cho chức vụ đã có 3 dòng giá trị
- [ ] 4.2: Bỏ 1 chức vụ và thêm 1 chức vụ mới
- [ ] 4.3: Lưu và kiểm tra bảng giá trị tự động cập nhật (xóa dòng cũ, thêm dòng mới)
- [ ] 4.4: Kiểm tra Properties fields được sync đúng

### Testcase 5: Kiểm tra phân biệt phụ cấp trong/ngoài lương
- [ ] 5.1: Tạo thành phần lương "Phụ cấp trong lương" (is_outside_salary = False)
- [ ] 5.2: Cấu hình giá trị cho chức vụ và lưu
- [ ] 5.3: Kiểm tra hr.job > tab "Phụ cấp trong lương" được cập nhật
- [ ] 5.4: Tạo thành phần lương "Phụ cấp ngoài lương" (is_outside_salary = True)
- [ ] 5.5: Kiểm tra hr.job > tab "Phụ cấp ngoài lương" được cập nhật

## Notes
- Chỉ áp dụng cho thành phần lương là Phụ cấp (is_allowance = True)
- Cần đảm bảo sync đúng với Properties fields hiện có
- Logic tự động gen records khi thay đổi danh sách áp dụng
