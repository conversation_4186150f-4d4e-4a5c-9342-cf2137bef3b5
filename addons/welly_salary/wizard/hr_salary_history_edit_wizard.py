from odoo import api, fields, models
from odoo.exceptions import UserError


# Timedelta không còn cần thiết

class HrSalaryHistoryEditWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.wizard'
    _description = '<PERSON> chỉnh sửa lịch sử lương'

    salary_history_id = fields.Many2one('hr.salary.history', string='L<PERSON>ch sử lương', required=True)
    employee_id = fields.Many2one(related='salary_history_id.hr_employee_id', string='Nhân viên', readonly=True)
    currency_id = fields.Many2one(
        comodel_name='res.currency',
        string="Tiền tệ",
        required=True,
        default=lambda self: self.env.company.currency_id
    )

    date_from = fields.Date(string='Từ ngày', required=True)
    date_to = fields.Date(string='Đến ngày')

    contract_salary = fields.Monetary(string='<PERSON><PERSON><PERSON> lươ<PERSON> theo hợp đồng', required=True, digits=(16, 0))
    base_salary = fields.Monetary(string='<PERSON><PERSON>ơng cơ bản', required=True, digits=(16, 0))
    effective_salary = fields.Monetary(string='<PERSON><PERSON><PERSON>ng hiệu quả', required=True, digits=(16, 0))
    description = fields.Text(string='Mô tả')

    # Phụ cấp
    all_allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='employee_id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='employee_id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )

    @api.model
    def default_get(self, fields_list):
        res = super().default_get(fields_list)
        if self.env.context.get('default_salary_history_id'):
            salary_history = self.env['hr.salary.history'].browse(
                self.env.context.get('default_salary_history_id')
            )
            res.update({
                'all_allowance_inside': salary_history.all_allowance_inside,
                'all_allowance_outside': salary_history.all_allowance_outside,
            })
        return res

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise UserError('Từ ngày phải nhỏ hơn hoặc bằng Đến ngày')

    def _validate_date_to(self):
        self.ensure_one()

        if self.date_to:
            return
        # LSL hiện tại
        edit_salary_his = self.salary_history_id
        # LSL cuối cùng có date_to null, date_to max, date_from max
        last_salary_his = self.env['hr.salary.history'].search([
            ('hr_employee_id', '=', self.employee_id.id),
        ], order='date_to desc, date_from desc', limit=1)
        if last_salary_his and last_salary_his[0] != edit_salary_his:
            raise UserError('Chỉ được điều chỉnh Đến ngày = Null cho lịch sử lương cuối cùng')

    def _check_date_overlap(self):
        self.ensure_one()
        domain = [
            ('hr_employee_id', '=', self.employee_id.id),
            ('id', '!=', self.salary_history_id.id),
            ('active', '=', True),
        ]
        if self.date_to:
            domain.append(('date_from', '<=', self.date_to))
        domain.extend(['|', ('date_to', '=', False), ('date_to', '>=', self.date_from)])

        if self.env['hr.salary.history'].search_count(domain) > 0:
            raise UserError(
                'Khoảng thời gian áp dụng của lịch sử lương không được trùng với các khoảng thời gian khác đang hoạt động.')

    def _check_non_description_changes(self):
        self.ensure_one()
        history = self.salary_history_id

        # Chuẩn hóa danh sách phụ cấp thành dạng dict {phone_allowance: 1000, transport_allowance: 2000} để so sánh
        def _are_properties_equal(props_edit, props_original):
            dict_edit = {
                p.get('name'): (p.get('value') or 0)
                for p in (props_edit or []) if p.get('name')
            }
            dict_original = {
                p.get('name'): (p.get('value') or 0)
                for p in (props_original or []) if p.get('name')
            }

            return dict_edit == dict_original

        return any([
            self.contract_salary != history.contract_salary,
            self.base_salary != history.base_salary,
            self.effective_salary != history.effective_salary,
            self.date_from != history.date_from,
            self.date_to != history.date_to,
            not _are_properties_equal(self.all_allowance_inside, history.all_allowance_inside),
            not _are_properties_equal(self.all_allowance_outside, history.all_allowance_outside)
        ])

    def action_save(self):
        self.ensure_one()

        self._validate_date_to()
        self._check_date_overlap()

        if self._check_non_description_changes():
            return self._show_confirmation_dialog()
        else:
            return self._save_changes()

    def _show_confirmation_dialog(self):
        view_id = self.env.ref('welly_salary.view_hr_salary_history_edit_confirmation_wizard_form').id
        return {
            'name': 'Xác nhận chỉnh sửa lương',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.edit.confirmation.wizard',
            'view_id': view_id,
            'target': 'new',
            'context': {'default_edit_wizard_id': self.id}
        }

    def _save_changes(self):
        self.ensure_one()
        vals = {
            'contract_salary': self.contract_salary,
            'base_salary': self.base_salary,
            'effective_salary': self.effective_salary,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'description': self.description,
            'all_allowance_inside': self.all_allowance_inside,
            'all_allowance_outside': self.all_allowance_outside,
        }
        self.salary_history_id.write(vals)
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        return {'type': 'ir.actions.act_window_close'}


class HrSalaryHistoryEditConfirmationWizard(models.TransientModel):
    _name = 'hr.salary.history.edit.confirmation.wizard'

    edit_wizard_id = fields.Many2one('hr.salary.history.edit.wizard', string='Edit Wizard', required=True)

    def action_confirm(self):
        """Xác nhận và lưu thay đổi"""
        self.ensure_one()
        self.edit_wizard_id._save_changes()
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        """Hủy thao tác"""
        return {'type': 'ir.actions.act_window_close'}