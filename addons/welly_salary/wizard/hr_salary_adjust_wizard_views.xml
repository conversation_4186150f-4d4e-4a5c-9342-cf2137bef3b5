<odoo>
    <record id="view_hr_salary_adjust_wizard_form" model="ir.ui.view">
        <field name="name">hr.salary.adjust.wizard.form</field>
        <field name="model">hr.salary.adjust.wizard</field>
        <field name="arch" type="xml">
            <form string="Đ<PERSON>ều chỉnh lương">
                <group>
                    <group>
                        <field name="contract_salary"/>
                        <field name="effective_salary" readonly="1"/>
                        <field name="hr_level_id" invisible="1"/>
                        <field name="job_id" invisible="1"/>
                        <field name="employee_id" invisible="1"/>
                        <field name="currency_id" invisible="1"/>
                    </group>
                    <group>
                        <field name="base_salary"/>
                        <label for="date_from" string="Hiệu lực từ"/>
                        <div class="o_row">
                            <field name="date_from" widget="daterange" nolabel="1" class="oe_inline"
                                   options="{'related_end_date': 'date_to'}"/>
                            <i class="fa fa-long-arrow-right mx-2" aria-label="Arrow icon" title="Arrow"/>
                            <field name="date_to" widget="daterange" nolabel="1" class="oe_inline"
                                   options="{'related_start_date': 'date_from'}"/>
                        </div>
                    </group>
                </group>
                <group>
                    <field name="all_allowance_inside" widget="properties_custom" noedit="1" nocreate="1"  columns="2" hideKanbanOption="1"/>
                </group>
                <group>
                    <field name="all_allowance_outside" widget="properties_custom" noedit="1" nocreate="1" colspan="2" hideKanbanOption="1"/>
                </group>
                <separator string="Mô tả"/>
                <field name="description"/>
                <footer>
                    <button name="action_save" string="Lưu" type="object" class="btn-primary"/>
                    <button string="Hủy" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo> 