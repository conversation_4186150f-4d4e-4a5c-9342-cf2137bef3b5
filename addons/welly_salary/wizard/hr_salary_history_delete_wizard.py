from odoo import api, fields, models


class HrSalaryHistoryDeleteWizard(models.TransientModel):
    _name = 'hr.salary.history.delete.wizard'
    _description = '<PERSON> x<PERSON><PERSON> lịch sử lương'

    salary_history_id = fields.Many2one('hr.salary.history', string='<PERSON><PERSON>ch sử lương', required=True)

    def action_confirm_delete(self):
        self.ensure_one()
        self.salary_history_id.soft_delete()
        return {'type': 'ir.actions.act_window_close'}

    def action_cancel(self):
        return {'type': 'ir.actions.act_window_close'}
