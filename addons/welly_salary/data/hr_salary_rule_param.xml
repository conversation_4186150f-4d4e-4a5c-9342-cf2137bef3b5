<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="param_cap_bac" model="hr.salary.rule.param">
            <field name="display_order">1</field>
            <field name="compute_order">1</field>
            <field name="name">Cấ<PERSON> bậc</field>
            <field name="code">cap_bac</field>
            <field name="param_type">auto</field>
            <field name="output_type">char</field>
        </record>

        <record id="param_loai_viec_lam" model="hr.salary.rule.param">
            <field name="display_order">3</field>
            <field name="compute_order">1</field>
            <field name="name">Loại vi<PERSON>c làm</field>
            <field name="code">loai_viec_lam</field>
            <field name="param_type">auto</field>
            <field name="output_type">char</field>
        </record>

        <record id="param_old_salary" model="hr.salary.rule.param">
            <field name="display_order">4</field>
            <field name="compute_order">1</field>
            <field name="name"><PERSON><PERSON><PERSON> l<PERSON>ng theo hợp đồng cũ</field>
            <field name="code">old_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_new_salary" model="hr.salary.rule.param">
            <field name="display_order">5</field>
            <field name="compute_order">1</field>
            <field name="name">Mức lương theo hợp đồng mới</field>
            <field name="code">salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_old_basic_salary" model="hr.salary.rule.param">
            <field name="display_order">6</field>
            <field name="compute_order">1</field>
            <field name="name">Mức lương cơ bản cũ</field>
            <field name="code">old_basic_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_new_basic_salary" model="hr.salary.rule.param">
            <field name="display_order">7</field>
            <field name="compute_order">1</field>
            <field name="name">Mức lương cơ bản mới</field>
            <field name="code">basic_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_old_salary_day" model="hr.salary.rule.param">
            <field name="display_order">8</field>
            <field name="compute_order">1</field>
            <field name="name">Số công mức lương cũ</field>
            <field name="code">old_salary_day</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_new_salary_day" model="hr.salary.rule.param">
            <field name="display_order">9</field>
            <field name="compute_order">1</field>
            <field name="name">Số công mức lương mới</field>
            <field name="code">salary_day</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_coefficient_k" model="hr.salary.rule.param">
            <field name="display_order">10</field>
            <field name="compute_order">1</field>
            <field name="name">Hệ số k</field>
            <field name="code">coefficient_k</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_social_insurance" model="hr.salary.rule.param">
            <field name="display_order">11</field>
            <field name="compute_order">1</field>
            <field name="name">Chính sách BHXH</field>
            <field name="code">is_social_insurance_applied</field>
            <field name="param_type">auto</field>
            <field name="output_type">boolean</field>
        </record>

        <record id="param_tax_policy" model="hr.salary.rule.param">
            <field name="display_order">12</field>
            <field name="compute_order">1</field>
            <field name="name">Chính sách thuế</field>
            <field name="code">tax_policy</field>
            <field name="param_type">auto</field>
            <field name="output_type">selection</field>
        </record>

        <record id="param_dependent_count" model="hr.salary.rule.param">
            <field name="display_order">13</field>
            <field name="compute_order">1</field>
            <field name="name">Số người phụ thuộc</field>
            <field name="code">dependent_count</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_target_reached" model="hr.salary.rule.param">
            <field name="display_order">14</field>
            <field name="compute_order">1</field>
            <field name="name">Tỷ lệ hoàn thành doanh số</field>
            <field name="code">target_reached</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_sale_commission_rate" model="hr.salary.rule.param">
            <field name="display_order">15</field>
            <field name="compute_order">1</field>
            <field name="name">Tỷ lệ com bán</field>
            <field name="code">sale_commission_rate</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_pt_commission_rate" model="hr.salary.rule.param">
            <field name="display_order">16</field>
            <field name="compute_order">1</field>
            <field name="name">Tỷ lệ com dạy</field>
            <field name="code">pt_commission_rate</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_sale_salary" model="hr.salary.rule.param">
            <field name="display_order">17</field>
            <field name="compute_order">1</field>
            <field name="name">Lương kinh doanh</field>
            <field name="code">sale_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_teaching_salary" model="hr.salary.rule.param">
            <field name="display_order">18</field>
            <field name="compute_order">1</field>
            <field name="name">Lương giờ dạy</field>
            <field name="code">teaching_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_standard_work_days" model="hr.salary.rule.param">
            <field name="display_order">19</field>
            <field name="compute_order">1</field>
            <field name="name">Số ngày công chuẩn</field>
            <field name="code">standard_work_days</field>
            <field name="param_type">auto</field>
            <field name="output_type">float</field>
        </record>

        <record id="param_old_effective_salary" model="hr.salary.rule.param">
            <field name="display_order">20</field>
            <field name="compute_order">1</field>
            <field name="name">Lương hiệu quả công việc cũ</field>
            <field name="code">old_effective_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>

        <record id="param_effective_salary" model="hr.salary.rule.param">
            <field name="display_order">21</field>
            <field name="compute_order">1</field>
            <field name="name">Lương Hiệu quả công việc mới</field>
            <field name="code">effective_salary</field>
            <field name="param_type">auto</field>
            <field name="output_type">integer</field>
        </record>
    </data>
</odoo>
