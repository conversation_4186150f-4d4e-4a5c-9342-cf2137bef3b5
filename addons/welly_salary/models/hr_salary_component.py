from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re


class HrSalaryComponent(models.Model):
    _name = 'hr.salary.component'
    _description = 'Thành phần lương'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    _sql_constraints = [
        ('unique_code', 'UNIQUE (code)', 'Mã thành phần lương phải là duy nhất')
    ]

    name = fields.Char(string='Tên', required=True)

    code = fields.Char(string='Mã thành phần', required=True,
                       help='Viết liền không dấu, chỉ cho phép nhập dấu gạch dưới', tracking=True)

    is_allowance = fields.Boolean(string='Phụ cấp', default=True, tracking=True)

    is_outside_salary = fields.Boolean(string='Ngoài lương', default=False, tracking=True)

    calculation_type = fields.Selection(
        selection=[('workday', '<PERSON> ngà<PERSON> công'), ('monthly', 'Tròn tháng')],
        string='Quy cách tính',
        required=True,
        tracking=True
    )

    apply_for = fields.Selection(
        selection=[('employee', 'Nhân viên'), ('job', 'Chức vụ'), ('level', 'Cấp bậc')],
        string='Áp dụng theo',
        required=True,
        tracking=True
    )

    hr_employee_ids = fields.Many2many(
        comodel_name='hr.employee',
        relation='hr_salary_component_hr_employee_rel',
        column1='hr_salary_component_id',
        column2='hr_employee_id',
        string='Nhân viên',
        tracking=True
    )

    hr_job_ids = fields.Many2many(
        comodel_name='hr.job',
        relation='hr_salary_component_hr_job_rel',
        column1='hr_salary_component_id',
        column2='hr_job_id',
        string='Chức vụ',
        tracking=True
    )

    hr_level_ids = fields.Many2many(
        comodel_name='hr.level',
        relation='hr_salary_component_hr_level_rel',
        column1='hr_salary_component_id',
        column2='hr_level_id',
        string='Cấp bậc',
        tracking=True
    )

    description = fields.Text(string='Mô tả', tracking=True)

    active = fields.Boolean(default=True, tracking=True)

    # Trường giá trị áp dụng cho trường hợp nhân viên
    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Công ty',
        required=True,
        default=lambda self: self.env.company
    )

    currency_id = fields.Many2one(
        comodel_name='res.currency',
        string='Tiền tệ',
        related='company_id.currency_id',
        readonly=True
    )

    default_value = fields.Monetary(
        string='Giá trị áp dụng',
        currency_field='currency_id',
        default=0,
        tracking=True,
        help='Giá trị mặc định áp dụng cho nhân viên khi thành phần lương được áp dụng theo nhân viên'
    )

    # One2many fields cho giá trị áp dụng theo chức vụ và cấp bậc
    job_value_ids = fields.One2many(
        comodel_name='hr.salary.component.job.value',
        inverse_name='salary_component_id',
        string='Giá trị áp dụng theo chức vụ'
    )

    level_value_ids = fields.One2many(
        comodel_name='hr.salary.component.level.value',
        inverse_name='salary_component_id',
        string='Giá trị áp dụng theo cấp bậc'
    )

    @api.constrains('code')
    def _check_code(self):
        for record in self:
            if not re.match(pattern=r'^[A-Za-z0-9_]+$', string=record.code):
                raise ValidationError(_('Mã chỉ được viết liền không dấu và có thể chứa dấu gạch dưới'))

    @api.onchange('apply_for')
    def _onchange_apply_for(self):
        if self.apply_for == 'job':
            self.hr_level_ids = [(5, 0, 0)]
            self.hr_employee_ids = [(5, 0, 0)]
        elif self.apply_for == 'level':
            self.hr_job_ids = [(5, 0, 0)]
            self.hr_employee_ids = [(5, 0, 0)]
        elif self.apply_for == 'employee':
            self.hr_job_ids = [(5, 0, 0)]
            self.hr_level_ids = [(5, 0, 0)]

    @api.onchange('hr_job_ids')
    def _onchange_hr_job_ids(self):
        """Tự động tạo/cập nhật records cho job_value_ids khi thay đổi hr_job_ids"""
        if self.apply_for == 'job':
            # Lấy danh sách job hiện tại trong job_value_ids
            existing_job_ids = set(self.job_value_ids.mapped('job_id.id'))
            new_job_ids = set(self.hr_job_ids.ids)

            # Xóa các job không còn trong danh sách
            to_remove = existing_job_ids - new_job_ids
            if to_remove:
                records_to_remove = self.job_value_ids.filtered(lambda r: r.job_id.id in to_remove)
                for record in records_to_remove:
                    self.job_value_ids = [(2, record.id)]

            # Thêm các job mới
            to_add = new_job_ids - existing_job_ids
            for job_id in to_add:
                self.job_value_ids = [(0, 0, {
                    'job_id': job_id,
                    'value': 0,
                })]

    @api.onchange('hr_level_ids')
    def _onchange_hr_level_ids(self):
        """Tự động tạo/cập nhật records cho level_value_ids khi thay đổi hr_level_ids"""
        if self.apply_for == 'level':
            # Lấy danh sách level hiện tại trong level_value_ids
            existing_level_ids = set(self.level_value_ids.mapped('level_id.id'))
            new_level_ids = set(self.hr_level_ids.ids)

            # Xóa các level không còn trong danh sách
            to_remove = existing_level_ids - new_level_ids
            if to_remove:
                records_to_remove = self.level_value_ids.filtered(lambda r: r.level_id.id in to_remove)
                for record in records_to_remove:
                    self.level_value_ids = [(2, record.id)]

            # Thêm các level mới
            to_add = new_level_ids - existing_level_ids
            for level_id in to_add:
                self.level_value_ids = [(0, 0, {
                    'level_id': level_id,
                    'value': 0,
                })]

    def write(self, vals):
        """Override write để tự động tạo/cập nhật job_value_ids và level_value_ids"""
        result = super().write(vals)

        # Nếu thay đổi hr_job_ids và apply_for = 'job'
        if 'hr_job_ids' in vals and self.apply_for == 'job':
            self._sync_job_values()

        # Nếu thay đổi hr_level_ids và apply_for = 'level'
        if 'hr_level_ids' in vals and self.apply_for == 'level':
            self._sync_level_values()

        return result

    def _sync_job_values(self):
        """Đồng bộ job_value_ids với hr_job_ids"""
        existing_job_ids = set(self.job_value_ids.mapped('job_id.id'))
        new_job_ids = set(self.hr_job_ids.ids)

        # Xóa các job không còn trong danh sách
        to_remove = existing_job_ids - new_job_ids
        if to_remove:
            records_to_remove = self.job_value_ids.filtered(lambda r: r.job_id.id in to_remove)
            records_to_remove.unlink()

        # Thêm các job mới
        to_add = new_job_ids - existing_job_ids
        for job_id in to_add:
            self.env['hr.salary.component.job.value'].create({
                'salary_component_id': self.id,
                'job_id': job_id,
                'value': 0,
            })

    def _sync_level_values(self):
        """Đồng bộ level_value_ids với hr_level_ids"""
        existing_level_ids = set(self.level_value_ids.mapped('level_id.id'))
        new_level_ids = set(self.hr_level_ids.ids)

        # Xóa các level không còn trong danh sách
        to_remove = existing_level_ids - new_level_ids
        if to_remove:
            records_to_remove = self.level_value_ids.filtered(lambda r: r.level_id.id in to_remove)
            records_to_remove.unlink()

        # Thêm các level mới
        to_add = new_level_ids - existing_level_ids
        for level_id in to_add:
            self.env['hr.salary.component.level.value'].create({
                'salary_component_id': self.id,
                'level_id': level_id,
                'value': 0,
            })

    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        records.invalidate_cache(['is_allowance', 'is_outside_salary'])
        Param = self.env['hr.salary.rule.param']
        # Lấy display_order lớn nhất hiện có
        max_display = Param.with_context(active_test=False).search([], order='display_order desc', limit=1).display_order or 0
        for idx, rec in enumerate(records):
            # 1. Xử lý tiêm phụ cấp
            if rec.is_allowance:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                target_records = rec[config['field']]
                rec._update_allowance_on_records(target_records, 'add')

            # 2. Xử lý tạo tham số lương (logic gốc)
            if not Param.search([('code', '=', f'old_{rec.code}')], limit=1):
                Param.create({
                    'name': f'{rec.name} cũ',
                    'code': f'old_{rec.code}',
                    'param_type': 'auto',
                    'output_type': 'integer',
                    'display_order': max_display + idx + 1,
                    'compute_order': 1,
                    'active': True,
                })
            if not Param.search([('code', '=', rec.code)], limit=1):
                Param.create({
                    'name': rec.name,
                    'code': rec.code,
                    'param_type': 'auto',
                    'output_type': 'integer',
                    'display_order': max_display + idx + 2,
                    'compute_order': 1,
                    'active': True,
                })
        return records

    def write(self, vals):
        # Lưu lại trạng thái cũ của các record trước khi cập nhật
        # Ví dụ: {'1': {'code': 'phone_allowance', 'is_allowance': True, 'is_outside_salary': False, 'apply_for': 'job', 'related_ids': {1, 2, 3}}}
        old_state_map = {}
        for rec in self:
            was_allowance = rec.is_allowance
            was_apply_for = rec.apply_for
            old_state_map[rec.id] = {
                'code': rec.code,
                'is_allowance': was_allowance,
                'is_outside_salary': rec.is_outside_salary,
                'apply_for': was_apply_for,
                'related_ids': set(rec[self.APPLY_FOR_CONFIG[was_apply_for]['field']].ids) if was_allowance and was_apply_for else set()
            }

        res = super().write(vals)

        # Cập nhật lại danh sách phụ cấp
        for rec in self:
            old_state = old_state_map[rec.id]
            # Lấy trạng thái phụ cấp cũ và mới để so sánh
            old_status = self._get_allowance_status(old_state['is_allowance'], old_state['is_outside_salary'])
            new_status = self._get_allowance_status(rec.is_allowance, rec.is_outside_salary)
            # TRƯỜNG HỢP 1: THAY ĐỔI LOẠI PHỤ CẤP (inside <-> outside <-> none)
            if old_status != new_status:
                # Lấy danh sách record cần xử lý dựa trên trạng thái cũ
                if old_status != 'none' and old_state['apply_for']:
                    old_config = self.APPLY_FOR_CONFIG[old_state['apply_for']]
                    records_to_cleanup = self.env[old_config['model']].browse(list(old_state['related_ids']))
                    # Xóa các phụ cấp liên quan dựa vào trạng thái cũ:
                    # 1. Nếu là phụ cấp trong lương, thì xóa phụ cấp đó khỏi danh sách
                    # 2. Nếu là phụ cấp ngoài lương, thì xóa phụ cấp đó khỏi danh sách
                    if records_to_cleanup:
                        if old_status == 'inside':
                            state_to_remove = {'code': old_state['code'], 'is_outside_salary': False}
                            rec._update_allowance_on_records(records_to_cleanup, 'remove', force_state=state_to_remove)
                        elif old_status == 'outside':
                            state_to_remove = {'code': old_state['code'], 'is_outside_salary': True}
                            rec._update_allowance_on_records(records_to_cleanup, 'remove', force_state=state_to_remove)

                # Tạo phụ cấp mới
                if new_status != 'none' and rec.apply_for:
                    new_config = self.APPLY_FOR_CONFIG[rec.apply_for]
                    new_records = rec[new_config['field']]
                    if new_records:
                        rec._update_allowance_on_records(new_records, 'add')

            # TRƯỜNG HỢP 2: LOẠI PHỤ CẤP KHÔNG ĐỔI, NHƯNG THAY ĐỔI ĐỐI TƯỢNG ÁP DỤNG
            elif old_state['apply_for'] != rec.apply_for:
                if new_status != 'none':
                    # Xóa phụ cấp cho các record cũ
                    if old_state['apply_for']:
                        old_config = self.APPLY_FOR_CONFIG[old_state['apply_for']]
                        old_records = self.env[old_config['model']].browse(list(old_state['related_ids']))
                        if old_records:
                            rec._update_allowance_on_records(old_records, 'remove', force_state=old_state)

                    # Thêm phụ cấp mới cho các record mới
                    if rec.apply_for:
                        new_config = self.APPLY_FOR_CONFIG[rec.apply_for]
                        new_records = rec[new_config['field']]
                        if new_records:
                            rec._update_allowance_on_records(new_records, 'add')

            # TRƯỜNG HỢP 3: KHÔNG THAY ĐỔI LOẠI PHỤ CẤP VÀ ĐỐI TƯỢNG ÁP DỤNG, CHỈ THAY ĐỔI DANH SÁCH
            elif new_status != 'none' and rec.apply_for:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                field_name = config['field']
                if field_name in vals:
                    old_ids = old_state['related_ids']
                    new_ids = set(rec[field_name].ids)

                    added_ids = list(new_ids - old_ids)
                    removed_ids = list(old_ids - new_ids)

                    model = self.env[config['model']]

                    # Chỉ thêm/xóa trên các record có thay đổi -> hiệu quả
                    if added_ids:
                        rec._update_allowance_on_records(model.browse(added_ids), 'add')
                    if removed_ids:
                        rec._update_allowance_on_records(model.browse(removed_ids), 'remove')

            # --- ĐỒNG BỘ VỚI hr.salary.rule.param ---
            param = self.env['hr.salary.rule.param'].search([('code', '=', rec.code)], limit=1)
            if param:
                updates = {}
                if 'name' in vals and param.name != rec.name:
                    updates['name'] = rec.name
                if 'active' in vals and param.active != rec.active:
                    updates['active'] = rec.active
                if updates:
                    param.write(updates)
            param_old = self.env['hr.salary.rule.param'].search([('code', '=', f'old_{rec.code}')], limit=1)
            if param_old:
                updates = {}
                if 'name' in vals and param_old.name != f'{rec.name} cũ':
                    updates['name'] = f'{rec.name} cũ'
                if 'active' in vals and param_old.active != rec.active:
                    updates['active'] = rec.active
                if updates:
                    param_old.write(updates)
        return res

    def unlink(self):
        raise ValidationError(_('Không thể xóa thành phần lương, bạn có thể lưu trữ thông tin.'))

    def action_archive(self):
        res = super().action_archive()
        for rec in self:
            if rec.is_allowance:
                config = self.APPLY_FOR_CONFIG[rec.apply_for]
                records = rec[config['field']]
                rec._update_allowance_on_records(records, 'remove')
        return res

    # Cấu hình ánh xạ giữa giá trị `apply_for` và các thông tin liên quan (model, tên trường)
    APPLY_FOR_CONFIG = {
        'employee': {'model': 'hr.employee', 'field': 'hr_employee_ids'},
        'job': {'model': 'hr.job', 'field': 'hr_job_ids'},
        'level': {'model': 'hr.level', 'field': 'hr_level_ids'},
    }

    def _get_allowance_field_names(self, force_is_outside=None):
        """
        Trả về tên các trường definition và property của phụ cấp.
        Dựa vào cờ `is_outside_salary` để quyết định là phụ cấp trong hay ngoài lương.

        :param force_is_outside: (Boolean) Nếu được truyền, sẽ dùng giá trị này thay vì
                                 giá trị hiện tại của record. Hữu ích khi cần xóa
                                 định nghĩa theo trạng thái cũ.
        :return: (tuple) Ví dụ: ('properties_definition_allowance_inside', 'allowance_inside')
        """
        is_outside = self.is_outside_salary if force_is_outside is None else force_is_outside
        if is_outside:
            return 'properties_definition_allowance_outside', 'allowance_outside'
        else:
            return 'properties_definition_allowance_inside', 'allowance_inside'

    def _update_allowance_on_records(self, records, action, force_state=None):
        """
        Hàm tổng quát để THÊM hoặc XÓA định nghĩa & giá trị phụ cấp
        trên một tập các record (jobs, levels, employees) được truyền vào.

        :param records: (RecordSet) Các record cần cập nhật (vd: self.env['hr.job'].browse(...)).
        :param action: (str) 'add' (thêm) hoặc 'remove' (xóa).
        :param force_state: (dict) Một dict chứa state cũ {'code', 'is_outside_salary'}
                        dùng khi cần xóa định nghĩa theo thông tin của record trước khi thay đổi.
        """
        if not records:
            return

        # Xác định các thông tin cần dùng (từ trạng thái hiện tại hoặc trạng thái cũ được truyền vào)
        state = force_state or self
        code = state.get('code') if isinstance(state, dict) else state.code
        is_outside = state.get('is_outside_salary') if isinstance(state, dict) else state.is_outside_salary
        def_field, prop_field = self._get_allowance_field_names(force_is_outside=is_outside)

        if action == 'add':
            prop_def = {"name": code, "type": "integer", "string": code, "default": 0}
            for record in records:
                current_defs = record[def_field] or []
                # Chỉ thêm nếu định nghĩa chưa tồn tại
                if not any(d.get("name") == code for d in current_defs):
                    record[def_field] = current_defs + [prop_def]

        elif action == 'remove':
            for record in records:
                # Xóa định nghĩa khỏi list
                current_defs = record[def_field] or []
                record[def_field] = [d for d in current_defs if d.get("name") != code]

                # Xóa cả giá trị của phụ cấp đó khỏi list
                current_props = record[prop_field] or []
                record[prop_field] = [p for p in current_props if p.get("name") != code]

    def _get_allowance_status(self, is_allowance, is_outside_salary):
        """
        Chuyển đổi 2 trường boolean thành một trạng thái dạng chuỗi dễ đọc và so sánh.
        :return: (str) 'none' (không phải phụ cấp), 'inside' (phụ cấp trong lương),
                 hoặc 'outside' (phụ cấp ngoài lương).
        """
        if not is_allowance:
            return 'none'
        return 'outside' if is_outside_salary else 'inside'


class HrSalaryComponentJobValue(models.Model):
    """Model lưu giá trị áp dụng của thành phần lương theo chức vụ"""
    _name = 'hr.salary.component.job.value'
    _description = 'Giá trị thành phần lương theo chức vụ'
    _rec_name = 'job_id'

    salary_component_id = fields.Many2one(
        comodel_name='hr.salary.component',
        string='Thành phần lương',
        required=True,
        ondelete='cascade'
    )

    job_id = fields.Many2one(
        comodel_name='hr.job',
        string='Chức vụ',
        required=True
    )

    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Công ty',
        related='salary_component_id.company_id',
        readonly=True
    )

    currency_id = fields.Many2one(
        comodel_name='res.currency',
        string='Tiền tệ',
        related='company_id.currency_id',
        readonly=True
    )

    value = fields.Monetary(
        string='Giá trị',
        currency_field='currency_id',
        default=0,
        required=True
    )

    _sql_constraints = [
        ('unique_job_component', 'UNIQUE (salary_component_id, job_id)',
         'Mỗi chức vụ chỉ có thể có một giá trị cho một thành phần lương')
    ]

    @api.model_create_multi
    def create(self, vals_list):
        """Tự động sync với Properties fields của hr.job khi tạo"""
        records = super().create(vals_list)
        for record in records:
            record._sync_to_job_properties()
        return records

    def write(self, vals):
        """Tự động sync với Properties fields của hr.job khi cập nhật"""
        result = super().write(vals)
        if 'value' in vals:
            for record in self:
                record._sync_to_job_properties()
        return result

    def _sync_to_job_properties(self):
        """Đồng bộ giá trị với Properties fields của hr.job"""
        self.ensure_one()
        if not self.job_id or not self.salary_component_id:
            return

        component = self.salary_component_id
        job = self.job_id

        # Xác định trường properties cần cập nhật
        if component.is_outside_salary:
            prop_field = 'allowance_outside'
        else:
            prop_field = 'allowance_inside'

        # Cập nhật giá trị trong properties
        current_props = getattr(job, prop_field) or []

        # Tìm và cập nhật hoặc thêm mới
        updated = False
        for prop in current_props:
            if prop.get('name') == component.code:
                prop['value'] = self.value
                updated = True
                break

        if not updated:
            current_props.append({
                'name': component.code,
                'value': self.value
            })

        setattr(job, prop_field, current_props)


class HrSalaryComponentLevelValue(models.Model):
    """Model lưu giá trị áp dụng của thành phần lương theo cấp bậc"""
    _name = 'hr.salary.component.level.value'
    _description = 'Giá trị thành phần lương theo cấp bậc'
    _rec_name = 'level_id'

    salary_component_id = fields.Many2one(
        comodel_name='hr.salary.component',
        string='Thành phần lương',
        required=True,
        ondelete='cascade'
    )

    level_id = fields.Many2one(
        comodel_name='hr.level',
        string='Cấp bậc',
        required=True
    )

    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Công ty',
        related='salary_component_id.company_id',
        readonly=True
    )

    currency_id = fields.Many2one(
        comodel_name='res.currency',
        string='Tiền tệ',
        related='company_id.currency_id',
        readonly=True
    )

    value = fields.Monetary(
        string='Giá trị',
        currency_field='currency_id',
        default=0,
        required=True
    )

    _sql_constraints = [
        ('unique_level_component', 'UNIQUE (salary_component_id, level_id)',
         'Mỗi cấp bậc chỉ có thể có một giá trị cho một thành phần lương')
    ]

    @api.model_create_multi
    def create(self, vals_list):
        """Tự động sync với Properties fields của hr.level khi tạo"""
        records = super().create(vals_list)
        for record in records:
            record._sync_to_level_properties()
        return records

    def write(self, vals):
        """Tự động sync với Properties fields của hr.level khi cập nhật"""
        result = super().write(vals)
        if 'value' in vals:
            for record in self:
                record._sync_to_level_properties()
        return result

    def _sync_to_level_properties(self):
        """Đồng bộ giá trị với Properties fields của hr.level"""
        self.ensure_one()
        if not self.level_id or not self.salary_component_id:
            return

        component = self.salary_component_id
        level = self.level_id

        # Xác định trường properties cần cập nhật
        if component.is_outside_salary:
            prop_field = 'allowance_outside'
        else:
            prop_field = 'allowance_inside'

        # Cập nhật giá trị trong properties
        current_props = getattr(level, prop_field) or []

        # Tìm và cập nhật hoặc thêm mới
        updated = False
        for prop in current_props:
            if prop.get('name') == component.code:
                prop['value'] = self.value
                updated = True
                break

        if not updated:
            current_props.append({
                'name': component.code,
                'value': self.value
            })

        setattr(level, prop_field, current_props)