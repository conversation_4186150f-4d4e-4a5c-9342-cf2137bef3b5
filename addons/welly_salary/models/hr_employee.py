from datetime import datetime

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    birthday = fields.Date('Ng<PERSON>y sinh', groups="hr.group_hr_user", tracking=True)
    start_working_date = fields.Date(string='Ngày nhận việc', tracking=True)

    @api.constrains('start_working_date', 'birthday')
    def _check_start_working_date(self):
        for record in self:
            if record.start_working_date and record.birthday and record.start_working_date <= record.birthday:
                raise ValidationError('Ng<PERSON>y nhận việc không được nhỏ hơn ngày sinh')

    hr_level_id = fields.Many2one(
        comodel_name='hr.level',
        string='Cấp bậc',
        tracking=True
    )

    hr_contract_type_id = fields.Many2one(
        comodel_name='hr.contract.type',
        string='<PERSON>ại việ<PERSON> làm',
        tracking=True
    )

    hr_business_unit_id = fields.Many2one(
        comodel_name='hr.business.unit',
        string='<PERSON>h<PERSON><PERSON> nhân sự',
        tracking=True
    )

    address = fields.Char(string='Địa chỉ')

    dependent_count = fields.Integer(string='Số người phụ thuộc')

    # Thêm trường ngày cấp chứng minh nhân dân
    identification_date = fields.Date(string='Ngày cấp CMND')

    # Thêm trường nơi cấp chứng minh nhân dân
    identification_place = fields.Text(string='Nơi cấp CMND')

    @api.constrains('identification_date', 'birthday')
    def _check_identification_date(self):
        today = fields.Date.today()
        for record in self:
            if record.identification_date and record.birthday and record.identification_date < record.birthday:
                raise ValidationError('Ngày cấp CMND không được nhỏ hơn ngày sinh')
            if record.identification_date and record.identification_date > today:
                raise ValidationError('Ngày cấp CMND không được lớn hơn ngày hiện tại')

    @api.constrains('birthday')
    def _check_birthday(self):
        for record in self:
            if record.birthday and record.certificate_line_ids:
                for cert_line in record.certificate_line_ids:
                    if cert_line.issue_date and cert_line.issue_date < record.birthday:
                        raise ValidationError(f'Ngày sinh không được lớn hơn ngày cấp chứng chỉ')
                    if cert_line.expiration_date and cert_line.expiration_date < record.birthday:
                        raise ValidationError(f'Ngày sinh không được lớn hơn ngày hiệu lực chứng chỉ')
            if record.birthday and record.dependent_line_ids:
                for dep_line in record.dependent_line_ids:
                    if dep_line.start_dependent_date and dep_line.start_dependent_date < record.birthday:
                        raise ValidationError(f'Ngày sinh nhân viên không được lớn hơn ngày bắt đầu phụ thuộc')
                    if dep_line.end_dependent_date and dep_line.end_dependent_date < record.birthday:
                        raise ValidationError(f'Ngày sinh nhân viên không được lớn hơn ngày kết thúc phụ thuộc')

    certificate_line_ids = fields.One2many(
        comodel_name='hr.certificate.line',
        inverse_name='hr_employee_id',
        string='Danh sách bằng cấp, chứng chỉ'
    )

    # Danh sách người phụ thuộc
    dependent_line_ids = fields.One2many(
        comodel_name='hr.dependent.line',
        inverse_name='hr_employee_id',
        string='Danh sách người phụ thuộc'
    )

    salary_component_ids = fields.Many2many(
        comodel_name='hr.salary.component',
        relation='hr_salary_component_hr_employee_rel',
        column1='hr_employee_id',
        column2='hr_salary_component_id',
        string='Thành phần lương',
    )

    # Tự tham chiếu đến chính model hiện tại, id
    self_container_id = fields.Many2one(
        'hr.employee',
        string='Self Container',
        compute='_compute_self_container',
        store=True,
        precompute=True
    )

    @api.depends('name')
    def _compute_self_container(self):
        for rec in self:
            rec.self_container_id = rec

    ##### THÀNH PHẦN LƯƠNG #############################################
    ### Phụ cấp trong lương ###
    properties_definition_allowance_inside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp trong lương dành cho NV',
    )

    allowance_inside = fields.Properties(
        string='Phụ cấp trong lương dành cho NV',
        definition_record='self_container_id',
        definition_record_field='properties_definition_allowance_inside',
        store=True
    )

    ### Phụ cấp ngoài lương ###
    properties_definition_allowance_outside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp ngoài lương NV',
    )

    allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương dành cho NV',
        definition_record='self_container_id',
        definition_record_field='properties_definition_allowance_outside',
        store=True
    )

    #### TỔNG HỢP CỦA CHỨC VỤ, CẤP BẬC, NHÂN VIÊN #####################
    all_properties_definition_allowance_inside = fields.PropertiesDefinition(
        string='Định nghĩa tổng hợp phụ cấp trong lương',
        compute='_compute_all_properties_definition_allowance',
        store=True,
        copy=True
    )
    all_properties_definition_allowance_outside = fields.PropertiesDefinition(
        string='Định nghĩa tổng hợp phụ cấp ngoài lương',
        compute='_compute_all_properties_definition_allowance',
        store=True,
        copy=True
    )

    @api.depends('hr_level_id', 'job_id', 'salary_component_ids',
                 'properties_definition_allowance_inside', 'properties_definition_allowance_outside',
                 'hr_level_id.properties_definition_allowance_inside', 'hr_level_id.properties_definition_allowance_outside',
                 'job_id.properties_definition_allowance_inside', 'job_id.properties_definition_allowance_outside')
    def _compute_all_properties_definition_allowance(self):
        for record in self:
            # Tổng hợp định nghĩa phụ cấp trong lương
            defs_inside = (record.properties_definition_allowance_inside or []) \
                + (record.hr_level_id.properties_definition_allowance_inside or []) \
                + (record.job_id.properties_definition_allowance_inside or [])
            record.all_properties_definition_allowance_inside = defs_inside or []
            # Tổng hợp định nghĩa phụ cấp ngoài lương
            defs_outside = (record.properties_definition_allowance_outside or []) \
                + (record.hr_level_id.properties_definition_allowance_outside or []) \
                + (record.job_id.properties_definition_allowance_outside or [])
            record.all_properties_definition_allowance_outside = defs_outside or []

    all_allowance_inside = fields.Properties(
        string='Tổng hợp phụ cấp trong lương',
        definition_record='self_container_id',
        definition_record_field='all_properties_definition_allowance_inside',
        compute='_compute_salary_info',
        store=True
    )

    all_allowance_outside = fields.Properties(
        string='Tổng hợp phụ cấp ngoài lương',
        definition_record='self_container_id',
        definition_record_field='all_properties_definition_allowance_outside',
        compute='_compute_salary_info',
        store=True
    )

    @api.depends(
        'salary_history_ids', 'hr_level_id', 'job_id', 'salary_component_ids',
        'properties_definition_allowance_inside', 'properties_definition_allowance_outside',
        'hr_level_id.properties_definition_allowance_inside', 'hr_level_id.properties_definition_allowance_outside',
        'job_id.properties_definition_allowance_inside', 'job_id.properties_definition_allowance_outside',
        'salary_history_ids.date_to', 'salary_history_ids.date_from',
        'salary_history_ids.all_allowance_inside', 'salary_history_ids.all_allowance_outside',
        'salary_history_ids.contract_salary', 'salary_history_ids.base_salary', 'salary_history_ids.effective_salary',
        'salary_history_ids.active',
    )
    def _compute_salary_info(self):
        """Tính toán tất cả các thông tin lương và phụ cấp"""
        for record in self:
            # Lọc các lịch sử lương đang hoạt động
            active_histories = record.salary_history_ids.filtered(lambda h: h.active)
            # TH đã có lịch sử lương
            if active_histories:
                latest_record = max(
                    active_histories,
                    key=lambda h: (h.date_to is False, h.date_to or datetime.min, h.date_from, h.id)
                )

                # --- Gán giá trị lương ---
                record.base_salary = latest_record.base_salary
                record.contract_salary = latest_record.contract_salary
                record.effective_salary = latest_record.effective_salary

                # --- Gán giá trị phụ cấp ---
                record.all_allowance_inside = latest_record.all_allowance_inside or {}
                record.all_allowance_outside = latest_record.all_allowance_outside or {}
            # TH không có lịch sử lương
            else:
                # Đặt lương về 0
                record.base_salary = 0
                record.contract_salary = 0
                record.effective_salary = 0

                # Xây dựng phụ cấp mặc định từ các cấu hình
                # Phụ cấp trong lương
                defs_inside = (record.properties_definition_allowance_inside or []) \
                              + (record.hr_level_id.properties_definition_allowance_inside or []) \
                              + (record.job_id.properties_definition_allowance_inside or [])
                result_inside = {field_def.get('name'): 0 for field_def in defs_inside if field_def.get('name')}
                record.all_allowance_inside = result_inside

                # Phụ cấp ngoài lương
                defs_outside = (record.properties_definition_allowance_outside or []) \
                               + (record.hr_level_id.properties_definition_allowance_outside or []) \
                               + (record.job_id.properties_definition_allowance_outside or [])
                result_outside = {field_def.get('name'): 0 for field_def in defs_outside if field_def.get('name')}
                record.all_allowance_outside = result_outside

    salary_history_ids = fields.One2many(
        comodel_name='hr.salary.history',
        inverse_name='hr_employee_id',
        string='Lịch sử lương'
    )

    sales_target_ids = fields.One2many('hr.employee.sales.target', 'employee_id', string='Mục tiêu doanh số')

    login = fields.Char(related='user_id.login', string='Tài khoản đăng nhập')

    # Action mở formview
    def action_open_user_details(self):
        # Mở view form của user từ view base.view_users_form
        view_id = self.env.ref('base.view_users_form').id
        return {
            'name': 'User Details',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'res.users',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'current',
            'res_id': self.user_id.id,
        }

    # Action server Điều chỉnh lương
    @api.model
    def action_salary_adjustment(self):
        view_id = self.env.ref('welly_salary.view_hr_salary_adjust_wizard_form').id
        return {
            'name': 'Điều chỉnh lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.adjust.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'size': 'large',
            'context': {
                'default_employee_id': self.id,
                'default_contract_salary': self.contract_salary,
                'default_base_salary': self.hr_level_id.base_salary if self.hr_level_id else 0,
                'default_effective_salary': self.effective_salary,
                'default_currency_id': self.company_id.currency_id.id,
                'default_hr_level_id': self.hr_level_id.id if self.hr_level_id else False,
                'default_job_id': self.job_id.id if self.job_id else False,
            }
        }

    base_salary = fields.Monetary(string='Lương cơ bản', currency_field='currency_id', compute='_compute_salary_info',
                                  store=True)

    contract_salary = fields.Monetary(string='Mức lương theo hợp đồng', currency_field='currency_id',
                                      compute='_compute_salary_info', store=True)

    effective_salary = fields.Monetary(string='Lương hiệu quả', currency_field='currency_id',
                                       compute='_compute_salary_info', store=True)

    is_social_insurance_applied = fields.Boolean(string='Chính sách BHXH', required=True, default=False)

    tax_policy = fields.Selection(
        string='Chính sách thuế',
        selection=[('no', 'No'), ('vn-progressive', 'vn-progressive'), ('vn-fixed-10', 'vn-fixed-10')],
        required=True,
        default='no'
    )

    hr_payslip_structure_id = fields.Many2one(
        comodel_name='hr.payslip.structure',
        string='Cấu trúc phiếu lương',
        tracking=True
    )


class HrDependentLine(models.Model):
    _name = 'hr.dependent.line'
    _description = 'Danh sách người phụ thuộc'
    _sql_constraints = [
        ('unique_name', 'unique (name)', 'Tên người phụ thuộc phải là duy nhất')
    ]

    hr_relationship_id = fields.Many2one(
        comodel_name='hr.relationship',
        string='Mối quan hệ',
    )

    name = fields.Char(string='Họ và tên')

    phone = fields.Char(string='Số điện thoại', length=12)

    # Check phone không quá 12 ký tự
    @api.constrains('phone')
    def _check_phone(self):
        for record in self:
            if record.phone and len(record.phone) > 12:
                raise ValidationError('Số điện thoại không được quá 12 ký tự')

    @api.constrains('name', )
    def _check_require(self):
        for record in self:
            if not record.name:
                raise ValidationError('Họ và tên không được để trống')
            if not record.hr_relationship_id:
                raise ValidationError('Mối quan hệ không được để trống')

    birth = fields.Date(string='Ngày sinh')

    @api.constrains('birth')
    def _check_birth(self):
        today = fields.Date.today()
        for record in self:
            if not record.birth:
                raise ValidationError('Ngày sinh không được để trống')
            if record.birth > today:
                raise ValidationError('Ngày sinh không được lớn hơn ngày hiện tại')

    is_dependent = fields.Boolean(string='Là người phụ thuộc')

    start_dependent_date = fields.Date(string='Ngày bắt đầu phụ thuộc')

    end_dependent_date = fields.Date(string='Ngày kết thúc phụ thuộc')

    @api.constrains('is_dependent', 'start_dependent_date', 'end_dependent_date', 'birth')
    def _check_dependent_date(self):
        for record in self:
            if record.is_dependent and not record.start_dependent_date:
                raise ValidationError('Ngày bắt đầu phụ thuộc không được để trống')
            if record.is_dependent and record.hr_employee_id.birthday and record.start_dependent_date < record.hr_employee_id.birthday:
                raise ValidationError('Ngày bắt đầu phụ thuộc không được nhỏ hơn ngày sinh của nhân viên')
            if record.is_dependent and record.start_dependent_date < record.birth:
                raise ValidationError('Ngày bắt đầu phụ thuộc không được nhỏ hơn ngày sinh người phụ thuộc')
            if record.is_dependent and record.end_dependent_date and record.start_dependent_date > record.end_dependent_date:
                raise ValidationError('Ngày bắt đầu phụ thuộc không được lớn hơn ngày kết thúc phụ thuộc')

    job = fields.Char(string='Nghề nghiệp')

    # Mã số thuế
    tax_code = fields.Char(string='Mã số thuế')

    hr_employee_id = fields.Many2one(
        comodel_name='hr.employee',
        string='Nhân viên',
        required=True
    )

    # Ghi đè create để log_activities khi tạo mới các trường
    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if res:
            for record in res:
                message = ''
                message += f"<br>- Mối quan hệ: {record.hr_relationship_id.name}"
                message += f"<br>- Họ và tên: {record.name}"
                message += f"<br>- Ngày bắt đầu phụ thuộc: {record.birth.strftime('%d/%m/%Y') if record.birth else ''}"
                message += f"<br>- Số điện thoại: {record.phone}"
                message += f"<br>- Nghề nghiệp: {record.job}"
                message += f"<br>- Mã số thuế: {record.tax_code}"
                message += f"<br>- Là người phụ thuộc: {record.is_dependent}"
                message += f"<br>- Ngày bắt đầu phụ thuộc: {record.start_dependent_date.strftime('%d/%m/%Y') if record.start_dependent_date else ''}"
                message += f"<br>- Ngày kết thúc phụ thuộc: {record.end_dependent_date.strftime('%d/%m/%Y') if record.end_dependent_date else ''}"
                record._log_activities(record.hr_employee_id.id,
                                       f"Tạo mới người phụ thuộc: <strong>{record.name}</strong>{message}")

        return res

    # Ghi đè write để log_activities khi sửa các trường
    def write(self, vals):
        res = super().write(vals)
        for record in self:
            message = ''
            if 'name' in vals:
                message += f"<br>- Họ và tên: {record.name}"
            if 'hr_relationship_id' in vals:
                message += f"<br>- Mối quan hệ: {record.hr_relationship_id.name}"
            if 'birth' in vals:
                message += f"<br>- Ngày sinh: {record.birth.strftime('%d/%m/%Y') if record.birth else ''}"
            if 'phone' in vals:
                message += f"<br>- Số điện thoại: {record.phone}"
            if 'job' in vals:
                message += f"<br>- Nghề nghiệp: {record.job}"
            if 'tax_code' in vals:
                message += f"<br>- Mã số thuế: {record.tax_code}"
            if 'is_dependent' in vals:
                message += f"<br>- Là người phụ thuộc: {record.is_dependent}"
            if 'start_dependent_date' in vals:
                message += f"<br>- Ngày bắt đầu phụ thuộc: {record.start_dependent_date.strftime('%d/%m/%Y') if record.start_dependent_date else ''}"
            if 'end_dependent_date' in vals:
                message += f"<br>- Ngày kết thúc phụ thuộc: {record.end_dependent_date.strftime('%d/%m/%Y') if record.end_dependent_date else ''}"
            if message:
                record._log_activities(record.hr_employee_id.id,
                                       f"Chỉnh sửa người phụ thuộc: <strong>{record.name}</strong>{message}")
        return res

    # Ghi đè unlink để log_activities khi xóa các trường
    def unlink(self):
        for record in self:
            record._log_activities(record.hr_employee_id.id, f"Xóa người phụ thuộc: <strong>{record.name}</strong>")
        return super().unlink()

    def _log_activities(self, res_id, message):
        self.env['mail.message'].create({
            'model': 'hr.employee',
            'res_id': res_id,
            'message_type': 'comment',
            'body': message,
        })


selection = [
    ('very_bad', 'Rất kém'),
    ('bad', 'Kém'),
    ('average', 'Trung bình'),
    ('good', 'Khá'),
    ('excellent', 'Giỏi')]


class HrCertificateLine(models.Model):
    _name = 'hr.certificate.line'
    _description = 'Danh sách bằng cấp, chứng chỉ'
    _inherit = ['mail.thread']

    _sql_constraints = [
        ('unique_name', 'unique (name)', 'Tên chứng chỉ phải là duy nhất')
    ]

    name = fields.Char(string='Tên chứng chỉ')

    hr_certificate_id = fields.Many2one(
        comodel_name='hr.certificate',
        string='Chứng chỉ',
        tracking=True
    )

    hr_employee_id = fields.Many2one(
        comodel_name='hr.employee',
        string='Nhân viên',
    )

    issuing_unit = fields.Char(string='Đơn vị cấp')

    issue_date = fields.Date(string='Ngày cấp')

    expiration_date = fields.Date(string='Hiệu lực tới', )

    level = fields.Selection(
        string='Cấp độ',
        selection=selection
    )

    @api.constrains('name', 'hr_certificate_id', 'level', 'issuing_unit', 'issue_date', 'expiration_date',
                    'hr_employee_id')
    def _check_dates(self):
        for record in self:
            if record.issue_date and record.hr_employee_id.birthday and record.issue_date < record.hr_employee_id.birthday:
                raise ValidationError('Ngày cấp không được nhỏ hơn ngày sinh của nhân viên')

            if record.expiration_date and record.hr_employee_id.birthday and record.expiration_date < record.hr_employee_id.birthday:
                raise ValidationError('Ngày hiệu lực không được nhỏ hơn ngày sinh của nhân viên')

            if record.issue_date and record.expiration_date and record.issue_date > record.expiration_date:
                raise ValidationError('Ngày cấp không được lớn hơn ngày hiệu lực')

            if not record.name:
                raise ValidationError('Tên chứng chỉ không được để trống')
            if not record.issuing_unit:
                raise ValidationError('Đơn vị cấp không được để trống')
            if not record.level:
                raise ValidationError('Cấp độ không được để trống')
            if not record.issue_date:
                raise ValidationError('Ngày cấp không được để trống')
            if not record.expiration_date:
                raise ValidationError('Ngày hiệu lực không được để trống')
            if not record.hr_certificate_id:
                raise ValidationError('Chứng chỉ không được để trống')

    # Ghi đè create để log_activities khi tạo mới các trường
    @api.model_create_multi
    def create(self, vals_list):
        res = super().create(vals_list)
        if res:
            for record in res:
                message = ''
                message += f"<br>- Chứng chỉ: {record.hr_certificate_id.name}"
                message += f"<br>- Cấp độ: {dict(selection).get(record.level)}"
                message += f"<br>- Đơn vị cấp: {record.issuing_unit}"
                message += f"<br>- Ngày cấp: {record.issue_date.strftime('%d/%m/%Y') if record.issue_date else ''}"
                message += f"<br>- Ngày hiệu lực: {record.expiration_date.strftime('%d/%m/%Y') if record.expiration_date else ''}"
                record._log_activities(record.hr_employee_id.id,
                                       f"Tạo mới chứng chỉ: <strong>{record.name}</strong>{message}")

        return res

    # Ghi đè write để log_activities khi sửa các trường
    def write(self, vals):
        res = super().write(vals)
        for record in self:
            message = ''
            if 'name' in vals:
                message += f"<br>- Tên chứng chỉ: {record.name}"
            if 'hr_certificate_id' in vals:
                message += f"<br>- Chứng chỉ: {record.hr_certificate_id.name}"
            if 'level' in vals:
                message += f"<br>- Cấp độ: {dict(selection).get(record.level)}"
            if 'issuing_unit' in vals:
                message += f"<br>- Đơn vị cấp: {record.issuing_unit}"
            if 'issue_date' in vals:
                message += f"<br>- Ngày cấp: {record.issue_date.strftime('%d/%m/%Y') if record.issue_date else ''}"
            if 'expiration_date' in vals:
                message += f"<br>- Ngày hiệu lực: {record.expiration_date.strftime('%d/%m/%Y') if record.expiration_date else ''}"
            if message:
                record._log_activities(record.hr_employee_id.id,
                                       f"Chỉnh sửa chứng chỉ: <strong>{record.name}</strong>{message}")
        return res

    # Ghi đè unlink để log_activities khi xóa các trường
    def unlink(self):
        for record in self:
            record._log_activities(record.hr_employee_id.id, f"Xóa chứng chỉ: <strong>{record.name}</strong>")
        return super().unlink()

    def _log_activities(self, res_id, message):
        self.env['mail.message'].create({
            'model': 'hr.employee',
            'res_id': res_id,
            'message_type': 'comment',
            'body': message,
        })
