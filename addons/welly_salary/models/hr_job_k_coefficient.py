from odoo import api, models, fields
from odoo.exceptions import ValidationError
from .commission_range_helper import CommissionRangeMixin

COMMISSION_OPERATOR = [('>', '>'), ('>=', '>='), ('<', '<'), ('<=', '<='), ('=', '=')]


class HrJobKCoefficient(models.Model):
    _name = 'hr.job.k.coefficient'
    _description = 'Cấu hình hệ số lương K'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'commission.range.mixin']
    _order = 'sequence, begin_value'

    hr_job_id = fields.Many2one('hr.job')

    # Tên
    name = fields.Char("Chỉ tiêu", size=150, required=True)

    # Constrains check trùng tên trong hr_job_id
    @api.constrains('name', 'hr_job_id')
    def check_unique_name(self):
        for rec in self:
            domain = [
                ('hr_job_id', '=', rec.hr_job_id.id),
                ('name', '=', rec.name),
                ('id', '!=', rec.id)
            ]
            if self.env['hr.job.k.coefficient'].search_count(domain) > 0:
                raise ValidationError("Tên chỉ tiêu hệ số lương không được trùng nhau.")

    # Toán tử
    begin_op = fields.Selection(string='Toán Tử', selection=COMMISSION_OPERATOR, default=">=", required=True)
    begin_value = fields.Integer('Giá trị bắt đầu', required=True)
    end_op = fields.Selection(string='Toán Tử', selection=COMMISSION_OPERATOR, default="<=", required=True)
    end_value = fields.Integer('Giá trị kết thúc', required=True)

    salary_coefficient = fields.Float(string="Hệ số lương")

    @api.constrains('begin_op', 'begin_value', 'end_op', 'end_value')
    def check_valid(self):
        def get_interval(op, val):
            if op == '>':
                return val, float('inf')
            elif op == '>=':
                return val, float('inf')
            elif op == '<':
                return float('-inf'), val
            elif op == '<=':
                return float('-inf'), val
            elif op == '=':
                return val, val

        for rec in self:
            begin_op = rec.begin_op
            begin_value = rec.begin_value
            end_op = rec.end_op
            end_value = rec.end_value
            salary_coefficient = rec.salary_coefficient

            # Validate giá trị phải lớn hơn bằng 0
            if begin_value < 0 or end_value < 0 or salary_coefficient < 0:
                raise ValidationError("Hệ số lương K: Giá trị bắt đầu, kết thúc, hệ số lương phải lớn hơn hoặc bằng 0")

            # Validate tính hợp lệ của biểu thức
            a1, b1 = get_interval(begin_op, begin_value)
            a2, b2 = get_interval(end_op, end_value)

            if begin_op == '>':
                a1 += 1e-9
            if end_op == '>':
                a2 += 1e-9
            if begin_op == '<':
                b1 -= 1e-9
            if end_op == '<':
                b2 -= 1e-9

            left = max(a1, a2)
            right = min(b1, b2)

            if left > right:
                raise ValidationError("Hệ số lương K: Chỉ tiêu có điều kiện không hợp lệ")

    # Kiểm tra giá trị nằm trong khoảng hoa hồng
    def check_commission_range(self, input_value):
        self.ensure_one()
        # Tạo ánh xạ các toán tử với các hàm so sánh tương ứng của chúng
        operators = {
            '>': lambda x, y: x > y,
            '>=': lambda x, y: x >= y,
            '<': lambda x, y: x < y,
            '<=': lambda x, y: x <= y,
            '=': lambda x, y: x == y,
        }

        # Lấy hàm so sánh tương ứng với toán tử
        begin_op_func = operators.get(self.begin_op)
        end_op_func = operators.get(self.end_op)

        # Execute hàm so sánh
        begin_check = begin_op_func(input_value, self.begin_value)
        end_check = end_op_func(input_value, self.end_value)

        # Trả về kết quả cuối cùng
        return begin_check and end_check