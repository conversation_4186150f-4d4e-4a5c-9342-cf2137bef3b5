from odoo import api, fields, models
from odoo.exceptions import ValidationError
from odoo.tools import format_date


class HrSalaryHistory(models.Model):
    _name = 'hr.salary.history'
    _description = '<PERSON><PERSON><PERSON> sử lươ<PERSON>'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = "date_to desc"

    currency_id = fields.Many2one(comodel_name='res.currency', string="Tiền tệ", required=True,
                                  default=lambda self: self.env.company.currency_id)

    date_from = fields.Date(string='Từ ngày', required=True, tracking=True)
    date_to = fields.Date(string='Đến ngày', tracking=True)

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise ValidationError('Từ ngày phải nhỏ hơn hoặc bằng Đến ngày')

    contract_salary = fields.Monetary(string='<PERSON><PERSON><PERSON> lương theo hợp đồng', required=True, digits=(16, 0), tracking=True)

    base_salary = fields.Monetary(string='<PERSON><PERSON><PERSON><PERSON> cơ bản', required=True, digits=(16, 0), tracking=True)

    effective_salary = fields.Monetary(string='Lương hiệu quả', required=True, digits=(16, 0), tracking=True)

    hr_employee_id = fields.Many2one(comodel_name='hr.employee', string='Nhân viên', required=True)

    description = fields.Text(string='Mô tả', tracking=True)

    active = fields.Boolean(string='Active', default=True, tracking=True)

    hr_level_id = fields.Many2one(related='hr_employee_id.hr_level_id', string='Cấp bậc')
    job_id = fields.Many2one(related='hr_employee_id.job_id', string='Vị trí công việc')

    all_allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='hr_employee_id',
        definition_record_field='all_properties_definition_allowance_inside',
        copy=True
    )
    all_allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='hr_employee_id',
        definition_record_field='all_properties_definition_allowance_outside',
        copy=True
    )

    def action_edit_salary_history(self):
        """Mở wizard để chỉnh sửa lịch sử lương"""
        self.ensure_one()
        view_id = self.env.ref('welly_salary.view_hr_salary_history_edit_wizard_form').id
        return {
            'name': 'Chỉnh sửa lịch sử lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.edit.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_salary_history_id': self.id,
                'default_contract_salary': self.contract_salary,
                'default_base_salary': self.base_salary,
                'default_effective_salary': self.effective_salary,
                'default_date_from': self.date_from,
                'default_date_to': self.date_to,
                'default_description': self.description,
                'default_currency_id': self.currency_id.id,
            }
        }

    def action_delete_salary_history(self):
        """Mở wizard xác nhận xóa lịch sử lương"""
        self.ensure_one()
        view_id = self.env.ref('welly_salary.view_hr_salary_history_delete_wizard_form').id
        return {
            'name': 'Xóa lịch sử lương',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'hr.salary.history.delete.wizard',
            'view_id': view_id,
            'type': 'ir.actions.act_window',
            'target': 'new',
            'context': {
                'default_salary_history_id': self.id,
            }
        }

    def soft_delete(self):
        for record in self:
            record.write({'active': False})

    def _log_salary_history_activity(self, message):
        self.env['mail.message'].create({
            'model': 'hr.employee',
            'res_id': self.hr_employee_id.id,
            'message_type': 'comment',
            'body': message,
        })

    def write(self, vals):
        # Định nghĩa các trường cần theo dõi và tiêu đề tương ứng của chúng
        tracked_fields = {
            'contract_salary': 'Lương hợp đồng',
            'base_salary': 'Lương cơ bản',
            'effective_salary': 'Lương hiệu quả',
            'date_from': 'Từ ngày',
            'date_to': 'Đến ngày',
            'all_allowance_inside': 'Phụ cấp trong lương',
            'all_allowance_outside': 'Phụ cấp ngoài lương',
            'description': 'Mô tả',
            'active': 'Trạng thái',
        }

        # 1. Lấy giá trị cũ
        old_values_map = {}
        fields_to_check = [f for f in tracked_fields if f in vals]
        if fields_to_check:
            for field_name in fields_to_check:
                old_values_map[field_name] = {
                    record.id: record[field_name] for record in self
                }
            old_values_map['currency_id'] = {record.id: record.currency_id for record in self}

        # 2. Gọi hàm `write` của lớp cha
        res = super().write(vals)

        # 3. So sánh giá trị mới và cũ để log
        for record in self:
            change_parts = []

            # Xử lý trường hợp đặc biệt: Xóa (Lưu trữ)
            if 'active' in vals and not vals['active']:
                message = "<strong>Bản ghi Lịch sử lương đã được xóa (lưu trữ).</strong>"
                record._log_salary_history_activity(message)
                continue

            # Duyệt qua các trường đã thay đổi để tạo log
            for field_name in fields_to_check:
                if field_name == 'active': continue

                field_title = tracked_fields[field_name]
                old_value = old_values_map.get(field_name, {}).get(record.id)
                new_value = record[field_name]

                if old_value == new_value:
                    continue

                message_content = ""
                # Xử lý cho các trường tiền tệ và ngày tháng
                if field_name in ['contract_salary', 'base_salary', 'effective_salary']:
                    currency = old_values_map['currency_id'].get(record.id)
                    old_str = f"{old_value:,.0f} {currency.symbol}"
                    new_str = f"{new_value:,.0f} {currency.symbol}"
                    message_content = f"{field_title}: {old_str} &rarr; {new_str}"

                elif field_name in ['date_from', 'date_to']:
                    old_str = format_date(self.env, old_value) if old_value else '<em>Không có</em>'
                    new_str = format_date(self.env, new_value) if new_value else '<em>Không có</em>'
                    message_content = f"{field_title}: {old_str} &rarr; {new_str}"

                elif field_name == 'description':
                    old_str = old_value or '<em>Không có</em>'
                    new_str = new_value or '<em>Không có</em>'
                    message_content = f"{field_title}: {old_str} &rarr; {new_str}"

                # Xử lý cho các trường phụ cấp
                elif field_name in ['all_allowance_inside', 'all_allowance_outside']:
                    old_props_dict = {prop.get('name'): prop.get('value', 0) for prop in (old_value or [])}
                    new_props_dict = {prop.get('name'): prop.get('value', 0) for prop in (new_value or [])}
                    all_prop_keys = sorted(list(set(old_props_dict.keys()) | set(new_props_dict.keys())))

                    currency = old_values_map['currency_id'].get(record.id)
                    inner_changes = []
                    for prop_name in all_prop_keys:
                        old_prop_val = old_props_dict.get(prop_name)
                        new_prop_val = new_props_dict.get(prop_name)

                        if old_prop_val == new_prop_val: continue

                        if new_prop_val is not None and old_prop_val is None:
                            inner_changes.append(
                                f"<li>Thêm mới {prop_name}: {new_prop_val:,.0f} {currency.symbol}</li>")
                        elif old_prop_val is not None and new_prop_val is None:
                            inner_changes.append(
                                f"<li>Xóa bỏ {prop_name} (giá trị cũ: {old_prop_val:,.0f} {currency.symbol})</li>")
                        elif old_prop_val != new_prop_val:
                            inner_changes.append(
                                f"<li>{prop_name}: {old_prop_val:,.0f} &rarr; {new_prop_val:,.0f} {currency.symbol}</li>")

                    if inner_changes:
                        message_content = f"{field_title}:<ul>{''.join(inner_changes)}</ul>"

                if message_content:
                    change_parts.append(message_content)

            if change_parts:
                list_items = "".join([f"<li>{part}</li>" for part in change_parts])
                full_message = f"<strong>Cập nhật lịch sử lương:</strong><ul>{list_items}</ul>"
                record._log_salary_history_activity(full_message)

        return res