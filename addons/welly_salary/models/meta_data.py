import re

from odoo import models, fields, _, api
from odoo.exceptions import ValidationError


class HrRelationship(models.Model):
    _name = 'hr.relationship'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = '<PERSON>uan hệ gia đình'

    name = fields.Char(string='Tên', required=True, tracking=True)

    description = fields.Text(string='Mô tả', tracking=True)

    @api.constrains('name')
    def _check_unique_name(self):
        for rec in self:
            if rec.name:
                existing_record = self.search([
                    ('name', '=ilike', rec.name),
                    ('id', '!=', rec.id)
                ])
                if existing_record:
                    raise ValidationError(_('Tên mối quan hệ đã tồn tại.'))


class HrCertificate(models.Model):
    _name = 'hr.certificate'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Bằng cấp, chứng chỉ'

    name = fields.Char(string='Tên', required=True, tracking=True)

    description = fields.Text(string='<PERSON>ô tả', tracking=True)

    @api.constrains('name')
    def _check_unique_name(self):
        for rec in self:
            if rec.name:
                existing_record = self.search([
                    ('name', '=ilike', rec.name),
                    ('id', '!=', rec.id)
                ])
                if existing_record:
                    raise ValidationError(_('Tên bằng cấp, chứng chỉ đã tồn tại.'))





class HrSalaryGeneralConfig(models.Model):
    _name = 'hr.salary.general.config'
    _description = 'Cấu hình chung dùng cho bảng lương'
    _rec_name = 'company_id'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _sql_constraints = [
        ('unique_company', 'unique(company_id)', 'Mỗi công ty chỉ có một cấu hình!'),
    ]

    company_id = fields.Many2one(
        'res.company',
        string='Công ty',
        required=True,
        default=lambda self: self.env.company
    )

    auto_approve_duration = fields.Integer(
        string='Thời gian yêu cầu xác nhận phiếu lương',
        required=True,
        tracking=True
    )

    duration_type = fields.Selection([
        ('days', 'Ngày'),
        ('hours', 'Giờ'),
        ('minutes', 'Phút')
    ], string='Loại thời gian', default='days', required=True, tracking=True)

    email_send_payslip = fields.Char(
        string='Email gửi phiếu lương',
        store=True,
        required=True,
        tracking=True
    )

    @api.constrains('auto_approve_duration')
    def _check_positive_duration(self):
        for rec in self:
            if rec.auto_approve_duration <= 0:
                raise ValidationError(_('Thời gian xác nhận phải lớn hơn 0.'))

    @api.constrains('email_send_payslip')
    def _check_valid_email(self):
        email_regex = r'^[^@]+@[^@]+\.[^@]+$'
        for rec in self:
            if rec.email_send_payslip and not re.match(email_regex, rec.email_send_payslip):
                raise ValidationError(_('Email gửi phiếu lương không hợp lệ, nhập sai định dạng. Ví dụ: <EMAIL>'))

    @api.model
    def create(self, vals):
        config = super().create(vals)
        if 'email_send_payslip' in vals and 'company_id' in vals:
            config.company_id.email_send_payslip = vals['email_send_payslip']
        return config

    def write(self, vals):
        res = super().write(vals)
        for rec in self:
            if 'email_send_payslip' in vals:
                rec.company_id.email_send_payslip = vals['email_send_payslip']
        return res

class ResCompany(models.Model):
    _inherit = 'res.company'

    email_send_payslip = fields.Char(
        string='Email gửi phiếu lương',
        store=True,
        tracking=True
    )
