from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrSalaryPeriod(models.Model):
    _name = 'hr.salary.period'
    _description = '<PERSON><PERSON> lương'
    _order = 'date_from desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Tên kỳ lương', required=True, tracking=True)

    month = fields.Selection(
        selection=[
            ('01', 'Tháng 01'), ('02', 'Tháng 02'), ('03', 'Tháng 03'), ('04', 'Tháng 04'),
            ('05', 'Tháng 05'), ('06', 'Tháng 06'), ('07', 'Tháng 07'), ('08', 'Tháng 08'),
            ('09', 'Tháng 09'), ('10', 'Tháng 10'), ('11', 'Tháng 11'), ('12', 'Tháng 12'),
        ],
        string='Tháng',
        required=True,
        tracking=True
    )
    year = fields.Selection(
        selection=lambda self: self._get_year_selection(),
        string='Năm',
        required=True,
        default=lambda self: str(fields.Date.today().year),
        tracking=True
    )

    @api.model
    def _get_year_selection(self):
        """Tính Selection cho Năm động theo năm hiện tại, nếu giá trị nhỏ hoặc lớn quá có thể thay + - giá trị khác"""
        current_year = fields.Date.today().year
        return [(str(y), str(y)) for y in range(current_year - 3, current_year + 7)]

    date_from = fields.Date(string='Từ ngày', required=True, tracking=True)
    date_to = fields.Date(string='Đến ngày', required=True, tracking=True)

    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Công ty',
        default=lambda self: self.env.company,
        readonly=True
    )

    # Liên kết với nhiều dòng cấu hình công chuẩn ca linh hoạt
    flexible_workday_ids = fields.One2many(
        'hr.salary.flexible.workday',
        'salary_period_id',
        string='Ngày công chuẩn ca linh hoạt',
        tracking=True
    )

    payroll_ids = fields.One2many('hr.payroll', 'salary_period_id', string='Bảng lương')
    has_payroll = fields.Boolean(compute='_compute_has_payroll', string='Đã có bảng lương', store=True)

    @api.depends('payroll_ids')
    def _compute_has_payroll(self):
        for rec in self:
            rec.has_payroll = bool(rec.payroll_ids)

    @api.onchange('month', 'year')
    def _onchange_month(self):
        """Tự động cập nhật date_from và date_to khi chọn tháng và năm."""
        if self.month and self.year:
            import datetime
            year = int(self.year)
            month = int(self.month)
            first_day = datetime.date(year, month, 1)
            if month == 12:
                last_day = datetime.date(year, 12, 31)
            else:
                last_day = datetime.date(year, month + 1, 1) - datetime.timedelta(days=1)
            self.date_from = first_day
            self.date_to = last_day

    @api.constrains('date_from', 'date_to')
    def _check_date_range(self):
        for rec in self:
            if rec.date_from and rec.date_to and rec.date_from > rec.date_to:
                raise ValidationError(_('Từ ngày không được lớn hơn Đến ngày.'))

    @api.constrains('year', 'date_from', 'date_to')
    def _check_year_value_validity(self):
        current_year = fields.Date.today().year
        for rec in self:
            year_int = int(rec.year)
            valid_years = {current_year}

            if rec.date_to and rec.date_to.year > current_year:
                valid_years.add(current_year + 1)
            if rec.date_from and rec.date_from.year < current_year:
                valid_years.add(current_year - 1)

            if year_int not in valid_years:
                raise ValidationError(_(
                    'Giá trị năm không hợp lệ. Vui lòng chọn 1 trong các năm hợp lệ: %s.'
                ) % ', '.join(str(y) for y in sorted(valid_years)))


    @api.model_create_multi
    def create(self, vals_list):
        records = super().create(vals_list)
        for record in records:
            record._update_employee_work_days()
        return records

    def write(self, vals):
        result = super().write(vals)
        self._update_employee_work_days()
        return result

    def _update_employee_work_days(self):
        """Cập nhật work_days của nhân viên dựa theo kỳ lương và ca linh hoạt"""
        Employee = self.env['hr.employee']

        for line in self.flexible_workday_ids:
            # Tìm các nhân viên thuộc phòng ban và có ca làm việc linh hoạt
            employees = Employee.search([
                ('department_id', 'in', line.department_ids.ids),
                ('work_shift_type', '=', 'flexible'),
            ])
            for emp in employees:
                emp.work_days = line.standard_work_days

    def action_generate_payroll(self):
        """Button tạo mới bảng lương theo kỳ lương"""
        self.ensure_one()
        Payroll = self.env['hr.payroll']
        existing = Payroll.search([('salary_period_id', '=', self.id)], limit=1)
        if existing:
            raise ValidationError(_('Bảng lương đã tồn tại cho kỳ này.'))
        payroll = Payroll.create({
            'salary_period_id': self.id,
        })
        self.env['mail.message'].create({
            'model': 'hr.salary.period',
            'res_id': self.id,
            'message_type': 'comment',
            'body': f"Tạo mới Bảng Lương: <a href='/web#id={payroll.id}&view_type=form&model=hr.payroll'>{payroll.name}</a>",
        })
        return {
            'type': 'ir.actions.act_window',
            'name': 'Bảng lương',
            'res_model': 'hr.payroll',
            'res_id': payroll.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def unlink(self):
        for rec in self:
            if rec.payroll_ids:
                raise ValidationError(_("Kỳ lương đã phát sinh Bảng lương không thể xóa."))
        return super().unlink()

class HrSalaryFlexibleWorkday(models.Model):
    _name = 'hr.salary.flexible.workday'
    _description = 'Ngày công chuẩn ca linh hoạt'
    _order = 'id'

    salary_period_id = fields.Many2one(
        'hr.salary.period',
        string='Kỳ lương',
        required=True,
        ondelete='cascade'
    )

    standard_work_days = fields.Float(
        string='Số ngày công chuẩn',
        digits=(16, 2),
        required=True,
        default=0.0
    )

    department_ids = fields.Many2many(
        'hr.department',
        string='Phòng ban áp dụng',
        default=lambda self: self.env['hr.department'].search([('company_id', '=', self.env.company.id)]),
        required=True
    )

    @api.onchange('standard_work_days')
    def _onchange_standard_work_days(self):
        if self.standard_work_days is False:
            self.standard_work_days = 0.0

    @api.constrains('standard_work_days')
    def _check_positive_days(self):
        for rec in self:
            if rec.standard_work_days < 0:
                raise ValidationError(_("Số ngày công chuẩn phải >= 0."))

    @api.constrains('department_ids', 'salary_period_id')
    def _check_unique_department_in_period(self):
        for rec in self:
            if not rec.salary_period_id or not rec.department_ids:
                continue

            # Tìm các dòng khác trong cùng kỳ lương
            other_lines = self.env['hr.salary.flexible.workday'].search([
                ('salary_period_id', '=', rec.salary_period_id.id),
                ('id', '!=', rec.id),
            ])

            # Lấy tất cả phòng ban đã tồn tại ở các dòng khác
            existing_department_ids = set()
            for line in other_lines:
                existing_department_ids.update(line.department_ids.ids)

            # Kiểm tra giao nhau
            conflicted_departments = rec.department_ids.filtered(lambda d: d.id in existing_department_ids)
            if conflicted_departments:
                dept_names = ', '.join(conflicted_departments.mapped('name'))
                raise ValidationError(
                    _('Phòng ban "%s" đã được cấu hình trong kỳ lương này. Mỗi phòng ban chỉ được khai báo một lần.')
                    % dept_names
                )
