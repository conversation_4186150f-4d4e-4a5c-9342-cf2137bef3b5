from odoo import api,models,fields, _
from odoo.exceptions import ValidationError


class HrJob(models.Model):
    _inherit = "hr.job"

    # Cấu hình lương
    salary_config_id = fields.Many2one(comodel_name='hr.salary.config', string='<PERSON>ấu hình lương', compute='_compute_salary_config_id', store=True)

    @api.depends('company_id', 'company_id.hr_salary_config_ids')
    def _compute_salary_config_id(self):
        for record in self:
            config = self.env['hr.salary.config'].search([('company_id', '=', record.company_id.id)], limit=1)
            record.salary_config_id = config if config else False

    # C<PERSON>u hình hệ số lương K
    k_coefficient_type = fields.Selection(
        string="Loại chỉ tiêu hệ số lương",
        selection=[("kpi", "<PERSON>anh số"), ("booking", "Số giờ dạy"), ("constant", "Hằng số")],
        default="kpi",
        required=True,
        tracking=True,
        copy=True
    )

    k_coefficient_target = fields.Integer(string="Chỉ tiêu (Số ca/tháng)", tracking=True, copy=True)

    k_coefficient_constant = fields.Integer(string='Hằng số hệ số lương (K)', tracking=True, copy=True)

    k_coefficient_line_ids = fields.One2many(
        string='Hệ số lương (K)',
        comodel_name='hr.job.k.coefficient',
        inverse_name='hr_job_id'
    )

    # Check các dòng hệ số lương K phải liên tục và không trùng lặp
    @api.constrains('k_coefficient_line_ids')
    def _check_k_coefficient_line_ids(self):
        for job in self:
            if job.k_coefficient_line_ids:
                # Sử dụng mixin để validate
                mixin = self.env['commission.range.mixin']
                mixin.validate_commission_ranges(
                    job.k_coefficient_line_ids,
                    "Hệ số lương K: "
                )

                # Tự động sắp xếp theo begin_value
                mixin.auto_sort_by_begin_value(job.k_coefficient_line_ids)

    # thành phần lương là động được lấy từ model hr.salary.component.
    # -> sử dụng fields.Properties để định nghĩa các trường động
    hr_salary_component_ids = fields.Many2many(
        comodel_name='hr.salary.component',
        relation='hr_salary_component_hr_job_rel',
        column1='hr_job_id',
        column2='hr_salary_component_id',
        string='Thành phần lương',
    )
    # tự tham chiếu đến chính model hiện tại, id
    self_container_id = fields.Many2one(
        'hr.job',
        string='Self Container',
        compute='_compute_self_container',
        store=True,
        precompute=True
    )

    @api.depends('name')
    def _compute_self_container(self):
        for rec in self:
            rec.self_container_id = rec

    ### Phụ cấp trong lương ###
    properties_definition_allowance_inside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp trong lương',
    )

    allowance_inside = fields.Properties(
        string='Phụ cấp trong lương',
        definition_record='self_container_id',
        definition_record_field='properties_definition_allowance_inside',
        copy=True
    )

    ### Phụ cấp ngoài lương ###
    properties_definition_allowance_outside = fields.PropertiesDefinition(
        string='Định nghĩa phụ cấp ngoài lương',
    )

    allowance_outside = fields.Properties(
        string='Phụ cấp ngoài lương',
        definition_record='self_container_id',
        definition_record_field='properties_definition_allowance_outside',
        copy=True
    )

    @api.model_create_multi
    def create(self, vals_list):
        records = super(HrJob, self).create(vals_list)
        for record in records:
            if record:
                record._compute_self_container()
        return records

    # Ghi đè write để log_activities khi sửa các trường phụ cấp
    def write(self, vals):
        # Định nghĩa các trường cần theo dõi và tiêu đề tương ứng của chúng trong log
        tracked_fields = {
            'allowance_inside': 'Phụ cấp trong lương',
            'allowance_outside': 'Phụ cấp ngoài lương',
        }

        # 1. Lấy giá trị cũ
        old_values_map = {}
        for field_name in tracked_fields:
            old_values_map[field_name] = {record.id: list(record[field_name]) for record in self}

        # 2. Gọi hàm `write` của lớp cha để thực hiện việc cập nhật
        res = super().write(vals)

        # 3. So sánh giá trị mới và cũ để log
        for record in self:
            change_messages = []

            for field_name, field_title in tracked_fields.items():
                old_props_list = old_values_map[field_name].get(record.id) or []
                new_props_list = record[field_name] or []

                if old_props_list != new_props_list:
                    old_props_dict = {prop.get('name'): prop.get('value') for prop in old_props_list}
                    new_props_dict = {prop.get('name'): prop.get('value') for prop in new_props_list}

                    all_prop_keys = sorted(list(set(old_props_dict.keys()) | set(new_props_dict.keys())))

                    field_specific_changes = []
                    for prop_name in all_prop_keys:
                        is_in_old = prop_name in old_props_dict
                        is_in_new = prop_name in new_props_dict

                        old_value = old_props_dict.get(prop_name, 0)
                        new_value = new_props_dict.get(prop_name, 0)
                        # Thêm mới một phụ cấp
                        if is_in_new and not is_in_old:
                            field_specific_changes.append(f"<li>Thêm mới: <b>{prop_name}</b></li>")
                        # Xóa bỏ một phụ cấp
                        elif is_in_old and not is_in_new:
                            field_specific_changes.append(f"<li>Xóa bỏ: <b>{prop_name}</b></li>")
                        # Sửa đổi giá trị của phụ cấp đã có
                        elif old_value != new_value:
                            field_specific_changes.append(f"<li>Sửa đổi <b>{prop_name}</b>: {old_value} &rarr; {new_value}</li>")

                    if field_specific_changes:
                        changes_html = "".join(field_specific_changes)
                        message = f"<b>{field_title}:</b><ul>{changes_html}</ul>"
                        change_messages.append(message)

            if change_messages:
                full_message = "<br/>".join(change_messages)
                record._log_activities(record.id, full_message)

        return res

    def _log_activities(self, res_id, message):
        self.env['mail.message'].create({
            'model': 'hr.job',
            'res_id': res_id,
            'message_type': 'comment',
            'body': message,
        })

    # Ghi đè copy gốc để nhân bản hr_job
    @api.returns('self', lambda value: value.id)
    def copy(self, default=None):
        # Đảm bảo chỉ thực hiện trên một bản ghi duy nhất, theo chuẩn của Odoo
        self.ensure_one()

        default = dict(default or {})

        if 'name' not in default:
            default['name'] = _("%s (Bản sao)", self.name)
        new_job = super(HrJob, self).copy(default)

        # Sao chép các dòng hệ số lương K
        for line in self.k_coefficient_line_ids:
            line.copy({'hr_job_id': new_job.id})

        # Sao chép các dòng hoa hồng dạy
        for line in self.hr_job_pt_commission_line_ids:
            line.copy({'hr_job_id': new_job.id})

        # Sao chép các dòng hoa hồng bán
        for line in self.hr_job_sale_commission_line_ids:
            line.copy({'hr_job_id': new_job.id})

        return new_job