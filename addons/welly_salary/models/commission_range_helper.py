# -*- coding: utf-8 -*-

from odoo import api, models, fields, _
from odoo.exceptions import ValidationError


class CommissionRangeHelper:
    """Helper class cho logic kiểm tra tính liên tục và sắp xếp các khoảng giá trị commission"""
    
    @staticmethod
    def get_interval_bounds(op, val):
        """Chuyển đổi toán tử và giá trị thành khoảng số thực"""
        if op == '>':
            return val + 1e-9, float('inf')
        elif op == '>=':
            return val, float('inf')
        elif op == '<':
            return float('-inf'), val - 1e-9
        elif op == '<=':
            return float('-inf'), val
        elif op == '=':
            return val, val
        else:
            raise ValidationError(f"Toán tử không hợp lệ: {op}")
    
    @staticmethod
    def get_effective_range(begin_op, begin_value, end_op, end_value):
        """<PERSON><PERSON><PERSON> kho<PERSON>ng giá trị hiệu quả từ điều kiện begin và end"""
        begin_left, begin_right = CommissionRangeHelper.get_interval_bounds(begin_op, begin_value)
        end_left, end_right = CommissionRangeHelper.get_interval_bounds(end_op, end_value)
        
        # Lấy giao của hai khoảng
        left = max(begin_left, end_left)
        right = min(begin_right, end_right)
        
        if left > right:
            raise ValidationError("Điều kiện không hợp lệ: không có giá trị nào thỏa mãn cả hai điều kiện")
        
        return left, right
    
    @staticmethod
    def check_ranges_continuous(records):
        """
        Kiểm tra tính liên tục của các khoảng giá trị
        
        Args:
            records: Danh sách các record có begin_op, begin_value, end_op, end_value
            
        Returns:
            bool: True nếu liên tục, False nếu không
            
        Raises:
            ValidationError: Nếu có lỗi trong cấu hình
        """
        if not records:
            return True
        
        # Lấy các khoảng hiệu quả và sắp xếp
        ranges = []
        for record in records:
            try:
                left, right = CommissionRangeHelper.get_effective_range(
                    record.begin_op, record.begin_value,
                    record.end_op, record.end_value
                )
                ranges.append((left, right, record))
            except ValidationError:
                # Nếu có record không hợp lệ, bỏ qua để constraint khác xử lý
                continue
        
        if not ranges:
            return True
        
        # Sắp xếp theo điểm bắt đầu
        ranges.sort(key=lambda x: x[0])
        
        # Kiểm tra tính liên tục
        for i in range(len(ranges) - 1):
            current_right = ranges[i][1]
            next_left = ranges[i + 1][0]
            
            # Cho phép sai số nhỏ do floating point
            if abs(current_right - next_left) > 1e-6 and current_right < next_left:
                return False
        
        return True
    
    @staticmethod
    def check_ranges_overlap(records):
        """
        Kiểm tra xem có khoảng nào trùng lặp không
        
        Args:
            records: Danh sách các record có begin_op, begin_value, end_op, end_value
            
        Returns:
            bool: True nếu có trùng lặp, False nếu không
        """
        if len(records) <= 1:
            return False
        
        ranges = []
        for record in records:
            try:
                left, right = CommissionRangeHelper.get_effective_range(
                    record.begin_op, record.begin_value,
                    record.end_op, record.end_value
                )
                ranges.append((left, right, record))
            except ValidationError:
                continue
        
        # Sắp xếp theo điểm bắt đầu
        ranges.sort(key=lambda x: x[0])
        
        # Kiểm tra overlap
        for i in range(len(ranges) - 1):
            current_right = ranges[i][1]
            next_left = ranges[i + 1][0]
            
            # Có overlap nếu current_right > next_left (với tolerance)
            if current_right > next_left + 1e-6:
                return True
        
        return False
    
    @staticmethod
    def get_effective_start_value(begin_op, begin_value):
        """
        Lấy giá trị bắt đầu thật sự của khoảng (để sắp xếp)

        Args:
            begin_op: Toán tử bắt đầu ('>', '>=', '<', '<=', '=')
            begin_value: Giá trị bắt đầu

        Returns:
            float: Giá trị bắt đầu thật sự
        """
        if begin_op == '>':
            return begin_value + 1e-9  # Lớn hơn begin_value một chút
        elif begin_op == '>=':
            return begin_value
        elif begin_op == '<':
            return float('-inf')  # Bắt đầu từ âm vô cực
        elif begin_op == '<=':
            return float('-inf')  # Bắt đầu từ âm vô cực
        elif begin_op == '=':
            return begin_value
        else:
            return begin_value

    @staticmethod
    def auto_sort_records(records):
        """
        Tự động sắp xếp các record theo giá trị bắt đầu thật sự tăng dần

        Args:
            records: Recordset cần sắp xếp

        Returns:
            list: Danh sách các giá trị sequence mới
        """
        if not records:
            return []

        # Tạo danh sách (effective_start_value, begin_value, record_id) và sắp xếp
        sorted_items = []
        for record in records:
            effective_start = CommissionRangeHelper.get_effective_start_value(
                record.begin_op, record.begin_value
            )
            sorted_items.append((effective_start, record.begin_value, record.id))

        # Sắp xếp theo effective_start_value, nếu bằng nhau thì theo begin_value
        sorted_items.sort(key=lambda x: (x[0], x[1]))

        # Tạo sequence mới
        sequence_values = []
        for i, (_, _, record_id) in enumerate(sorted_items):
            sequence_values.append({
                'id': record_id,
                'sequence': (i + 1) * 10  # Sequence cách nhau 10 để dễ chèn sau này
            })

        return sequence_values


class CommissionRangeMixin(models.AbstractModel):
    """Mixin cung cấp các method chung cho validation commission range"""
    _name = 'commission.range.mixin'
    _description = 'Commission Range Validation Mixin'
    
    sequence = fields.Integer(string='Thứ tự', default=10)
    
    def validate_commission_ranges(self, records, error_prefix=""):
        """
        Validate tính hợp lệ của các khoảng commission
        
        Args:
            records: Recordset cần validate
            error_prefix: Prefix cho thông báo lỗi
        """
        if not records:
            return
        
        # Kiểm tra overlap
        if CommissionRangeHelper.check_ranges_overlap(records):
            raise ValidationError(f"{error_prefix}Các khoảng giá trị không được trùng lặp")
        
        # Kiểm tra tính liên tục
        if not CommissionRangeHelper.check_ranges_continuous(records):
            raise ValidationError(
                f"{error_prefix}Các khoảng giá trị phải liên tục (không được có khoảng trống). "
                "Ví dụ đúng: 0-50, 50-100, 100-150. "
                "Ví dụ sai: 0-50, 100-150 (thiếu 50-100)"
            )
    
    def auto_sort_by_begin_value(self, records):
        """Tự động sắp xếp records theo begin_value"""
        if not records:
            return
        
        sequence_values = CommissionRangeHelper.auto_sort_records(records)
        
        # Cập nhật sequence
        for item in sequence_values:
            record = records.browse(item['id'])
            record.sequence = item['sequence']
