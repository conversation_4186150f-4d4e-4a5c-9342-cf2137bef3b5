/** @odoo-module **/

import { _lt } from "@web/core/l10n/translation";
import { registry } from "@web/core/registry";
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { loadJS } from "@web/core/assets";
import { Component, onWillStart, useRef, onMounted } from "@odoo/owl";


export class TimepickerField extends Component {
    setup() {
        this.timepickerRef = useRef("timepicker");
        onWillStart(async () => {
            return loadJS("/welly_widget/static/libs/flatpickr/flatpickr.min.js");
        });
        onMounted(
            () => {
                if (this.timepickerRef.el) {
                    this.timepickerRef.el.flatpickr({
                        enableTime: true,
                        noCalendar: true,
                        dateFormat: "H:i", // Định dạng 24 giờ
                        time_24hr: true
                    });
                }
            }
        );
    }

    get formattedValue() {
        return this.props.value;
    }

    onChangeValue(e) {
        this.props.update(e.target.value);
    }

    onClearValue() {
        this.props.update("");
    }
}

TimepickerField.template = "web.TimepickerField";
TimepickerField.props = {
    ...standardFieldProps,
    inputType: { type: String, optional: true },
    placeholder: { type: String, optional: true },
};
TimepickerField.defaultProps = {
    inputType: "text",
};

TimepickerField.displayName = _lt("Time");
TimepickerField.supportedTypes = ["char"];

TimepickerField.isEmpty = () => false;
TimepickerField.extractProps = ({ attrs }) => {
    return {
        inputType: attrs.options.type,
        placeholder: attrs.placeholder,
    };
};

registry.category("fields").add("timepicker", TimepickerField);
