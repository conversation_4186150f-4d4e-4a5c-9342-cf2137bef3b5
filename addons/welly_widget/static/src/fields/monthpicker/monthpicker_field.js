/** @odoo-module **/

import { _lt } from "@web/core/l10n/translation";
import { registry } from "@web/core/registry";
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { loadJS, loadCSS } from "@web/core/assets";
import { Component, onWillStart, useRef, onMounted } from "@odoo/owl";

export class MonthPickerField extends Component {
    setup() {
        this.monthPickerRef = useRef("monthpicker");
        onWillStart(async () => {
            await loadCSS("/welly_widget/static/libs/flatpickr/month_picker.min.css");
            await loadJS("/welly_widget/static/libs/flatpickr/flatpickr.min.js");
            await loadJS("/welly_widget/static/libs/flatpickr/month_picker.min.js");
            await loadJS("/welly_widget/static/libs/flatpickr/vi.min.js");
        });
        onMounted(
            () => {
                if (this.monthPickerRef.el) {
                    this.monthPickerRef.el.flatpickr({
                        locale: "vn",
                        plugins: [new monthSelectPlugin({
                            shorthand: true, // Hiển thị tháng rút gọn
                            dateFormat: "m/Y", // Định dạng lưu trữ (01/2025)
                            altFormat: "F Y"   // Hiển thị (Tháng Một 2025)
                        })]
                    });
                }
            }
        );
    }

    get formattedValue() {
        return this.props.value;
    }

    onChangeValue(e) {
        this.props.update(e.target.value);
    }

    onClearValue() {
        this.props.update("");
    }
}

MonthPickerField.template = "web.MonthPickerField";
MonthPickerField.props = {
    ...standardFieldProps,
    inputType: { type: String, optional: true },
    placeholder: { type: String, optional: true },
};
MonthPickerField.defaultProps = {
    inputType: "text",
};

MonthPickerField.displayName = _lt("Month");
MonthPickerField.supportedTypes = ["char"];

MonthPickerField.isEmpty = () => false;
MonthPickerField.extractProps = ({ attrs }) => {
    return {
        inputType: attrs.options.type,
        placeholder: attrs.placeholder,
    };
};

registry.category("fields").add("monthpicker", MonthPickerField);
