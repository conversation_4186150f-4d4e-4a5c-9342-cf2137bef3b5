# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_account
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-10-10 11:34+0000\n"
"PO-Revision-Date: 2017-10-10 11:34+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> Jovev <<EMAIL>>, 2017\n"
"Language-Team: Serbian (Latin) (https://www.transifex.com/odoo/teams/41243/sr%40latin/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr@latin\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
msgid "Account Entry"
msgstr "Računovodstveni unos"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move_account_move_ids
msgid "Account Move"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "Stavke knjiženja"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings_module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on receipt operations and split them among products to "
"update their cost price."
msgstr ""

#. module: stock_account
#: selection:product.category,property_valuation:0
msgid "Automated"
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Average Cost (AVCO)"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Cancel"
msgstr "Odustani"

#. module: stock_account
#: code:addons/stock_account/models/stock.py:409
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:411
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Price"
msgstr ""

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_view_change_standard_price
#: model:ir.model,name:stock_account.model_stock_change_standard_price
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Change Standard Price"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_inventory_accounting_date
msgid ""
"Choose the accounting date at which you want to value the stock moves "
"created by the inventory instead of the default one (the inventory end date)"
msgstr ""

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_move_valuation_action
msgid "Click to create a stock movement."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:480
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "Cost"
msgstr "Cijena koštanja"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_cost_method
msgid "Cost Method"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Costing"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_cost_method
msgid "Costing Method"
msgstr "Metod obračuna troškova"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_counterpart_account_id
msgid "Counter-Part Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_counterpart_account_id_required
msgid "Counter-Part Account Required"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_create_date
msgid "Created on"
msgstr "Datum kreiranja"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "First In First Out (FIFO)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_inventory_accounting_date
msgid "Force Accounting Date"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_change_standard_price_new_price
msgid ""
"If cost price is increased, stock variation account will be debited and stock output account will be credited with the value = (difference of amount * quantity available).\n"
"If cost price is decreased, stock variation account will be creadited and stock input account will be debited."
msgstr ""

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.product_valuation_action
msgid "If there are products, you will see its name and valuation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Include landed costs in product cost"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_inventory
msgid "Inventory"
msgstr "Skladište"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_valuation
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.product_template_valuation_form_view
#: model_terms:ir.ui.view,arch_db:stock_account.product_valuation_form_view
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Inventory Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice
msgid "Invoice"
msgstr "Faktura"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_invoice_line
msgid "Invoice Line"
msgstr "Stavka računa"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings_module_stock_landed_costs
msgid "Landed Costs"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price___last_update
msgid "Last Modified on"
msgstr "Zadnja promena"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_uid
msgid "Last Updated by"
msgstr "Promenio"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_write_date
msgid "Last Updated on"
msgstr "Vreme promene"

#. module: stock_account
#: selection:product.category,property_valuation:0
msgid "Manual"
msgstr "Ručno"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_valuation
#: model:ir.model.fields,help:stock_account.field_product_template_property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation
msgid "Moves"
msgstr "Premeštanja"

#. module: stock_account
#: code:addons/stock_account/models/product.py:128
#, python-format
msgid "No difference between standard price and new price!"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Packing Operation"
msgstr ""

#. module: stock_account
#: selection:product.template,property_valuation:0
msgid "Periodic (manual)"
msgstr ""

#. module: stock_account
#: selection:product.template,property_valuation:0
msgid "Perpetual (automated)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_change_standard_price_new_price
msgid "Price"
msgstr "Cijena"

#. module: stock_account
#: model:ir.model,name:stock_account.model_procurement_group
msgid "Procurement Requisition"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
#: model_terms:ir.ui.view,arch_db:stock_account.product_valuation_tree
msgid "Product"
msgstr "Proizvod"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
msgid "Product Category"
msgstr "Grupa proizvoda"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
msgid "Product Template"
msgstr "Predložak proizvoda"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.product_valuation_action
msgid "Product Valuation"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation
msgid "Qty"
msgstr "Kol."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_valuation_tree
msgid "Quantity on Hand"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation
msgid "Reference"
msgstr "Šifra"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move_remaining_qty
msgid "Remaining Qty"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move_remaining_value
msgid "Remaining Value"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_variant_easy_edit_view_inherit
msgid "Set standard cost"
msgstr ""

#. module: stock_account
#: selection:product.category,property_cost_method:0
#: selection:product.template,property_cost_method:0
msgid "Standard Price"
msgstr "Standardna cena"

#. module: stock_account
#: code:addons/stock_account/models/product.py:145
#: code:addons/stock_account/models/product.py:150
#, python-format
msgid "Standard Price changed  - %s"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_template_property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_input_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_input
msgid "Stock Input Account"
msgstr "Nalog ulaznih zaliha"

#. module: stock_account
#: code:addons/stock_account/models/account_chart_template.py:15
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_journal
#, python-format
msgid "Stock Journal"
msgstr "Dnevnik zaliha"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_move_stock_move_id
msgid "Stock Move"
msgstr "Skladišni prenosi"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.stock_move_valuation_action
msgid "Stock Moves"
msgstr "Prenosnice"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_account_output_categ_id
#: model:ir.model.fields,field_description:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,field_description:stock_account.field_product_template_property_stock_account_output
msgid "Stock Output Account"
msgstr "Izlazni nalog zaliha"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_valuation_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Stock Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category_property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location_valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr ""

#. module: stock_account
#: model:ir.actions.server,name:stock_account.action_stock_account_valuation_report
msgid "Stock Valuation Report"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Templates for Account Chart"
msgstr "Sabloni kontnog plana"

#. module: stock_account
#: code:addons/stock_account/models/stock.py:435
#, python-format
msgid ""
"The cost of %s is currently equal to 0. Change the cost or the configuration"
" of your product to avoid an incorrect valuation."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move_to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line_to_refund
msgid "To Refund (update SO/PO)"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_valuation_tree
msgid "Total Value"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move_to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line_to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_tree_valuation
msgid "Unit of Measure"
msgstr "Jedinica mere"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_template_property_form
msgid "Update Cost"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location_valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template_valuation
msgid "Valuation"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product_stock_value
#: model:ir.model.fields,field_description:stock_account.field_stock_move_value
msgid "Value"
msgstr "Vrijednost"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_input_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. This is the default "
"value for all products in this category. It can also directly be set on each"
" product"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_input
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_input
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"incoming stock moves will be posted in this account, unless there is a "
"specific valuation account set on the source location. When not set on the "
"product, the one from the product category is used."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_account_output_categ_id
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. This is the "
"default value for all products in this category. It can also directly be set"
" on each product"
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product_property_stock_account_output
#: model:ir.model.fields,help:stock_account.field_product_template_property_stock_account_output
msgid ""
"When doing real-time inventory valuation, counterpart journal items for all "
"outgoing stock moves will be posted in this account, unless there is a "
"specific valuation account set on the destination location. When not set on "
"the product, the one from the product category is used."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_journal
msgid ""
"When doing real-time inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category_property_stock_valuation_account_id
msgid ""
"When real-time inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/stock.py:407
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts"
msgstr ""

#. module: stock_account
#: code:addons/stock_account/models/product.py:130
#: code:addons/stock_account/models/stock.py:413
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_change_standard_price
msgid "_Apply"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "res.config.settings"
msgstr ""

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "stock.return.picking.line"
msgstr ""
