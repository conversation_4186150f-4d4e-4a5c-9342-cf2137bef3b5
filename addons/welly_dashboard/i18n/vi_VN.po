# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* izi_dashboard
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-04 10:18+0000\n"
"PO-Revision-Date: 2024-12-04 10:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_analysis.py:0
#, python-format
msgid ""
"\n"
"                Your analysis looks fine!\n"
"                Sample Data:\n"
"                %s\n"
"            "
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "AI"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "AI Action"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__ai_analysis_text
msgid "AI Analysis Text"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__ai_explore_analysis_ids
msgid "AI Explore Analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__ai_language
msgid "AI Language"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "AI Settings"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "AND"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Action"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Activate Other Tables"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__active
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__is_active
msgid "Active"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Add Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Add Dimensions"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Add Filter"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Add Filters"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Add Metrics"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Add Sort"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#, python-format
msgid "Add to Dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "All (No Filters)"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__allowed_analysis_ids
msgid "Allowed Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard_block.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard_block.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard_block.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_visual.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_visual.js:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#: model:ir.actions.act_window,name:izi_dashboard.izi_analysis_action
#: model:ir.model,name:izi_dashboard.model_izi_analysis
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__analysis_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__analysis_ids
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__analysis_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__analysis_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__analysis_id
#: model:ir.ui.menu,name:izi_dashboard.izi_analysis_menu
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis
#, python-format
msgid "Analysis"
msgstr "Analysis"

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Analysis Blocks"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__analysis_data
msgid "Analysis Data"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__analysis_domain
msgid "Analysis Domain"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__allowed_field_ids
msgid "Analysis Fields"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__analysis_visual_config_ids
msgid "Analysis Visual Config"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Applied To"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_sort_item.xml:0
#, python-format
msgid "Asc"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Ask AI Consultant"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#, python-format
msgid "Ask AI to Explore and Generate Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Auto Arrange"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__auto_slide
msgid "Auto Slide (Seconds)"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__automatic_font_color
msgid "Automatic Font Color"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__automatic_font_size
msgid "Automatic Font Size"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,help:izi_dashboard.field_izi_dashboard_slide__automatic_font_color
msgid "Automatically follow selected themes"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_metric_item.xml:0
#, python-format
msgid "Avg"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Back"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__general_bg_file
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__bg_file
msgid "Background"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__general_bg_filename
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__bg_filename
msgid "Background Name"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__base_url
msgid "Base URL"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__beige
msgid "Beige"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,help:izi_dashboard.field_izi_dashboard_slide__automatic_font_size
msgid "Best for short texts"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__black
msgid "Black"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__blood
msgid "Blood"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config_value__value_type__boolean
msgid "Boolean"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__new_block_position__bottom
msgid "Bottom"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_config_wizard
#, python-format
msgid "Cancel"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_dashboard.py:0
#, python-format
msgid "Cannot have multiple slides using the same analysis!"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "Capture Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Capture Dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Capturing.."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Category"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__text_align__center
msgid "Center"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout_order__chart_text
msgid "Chart -> Text"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout__chart
msgid "Chart Only"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__chart_size
msgid "Chart Size (%)"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "Choose operator"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Choose the fields to aggregate the data"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Choose the quantitative fields to measure"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Choose the type of the charts"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Click to select existing dashboard or create a new one"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Click to select or create a dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Click to select or create analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__code
msgid "Code"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__code_file
msgid "Code File"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__concave
msgid "Concave"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__config_type
msgid "Config Type"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_analysis_item.xml:0
#, python-format
msgid "Configuration"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__convex
msgid "Convex"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_metric_item.xml:0
#, python-format
msgid "Count"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__create_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__create_uid
msgid "Created by"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__create_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__create_date
msgid "Created on"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_metric_item.xml:0
#, python-format
msgid "Cumulative Sum"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__custom
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Custom Range"
msgstr "Tùy chỉnh khoảng thời gian"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_select_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_select_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: model:ir.actions.act_window,name:izi_dashboard.izi_dashboard_action
#: model:ir.model.fields,field_description:izi_dashboard.field_ir_ui_menu__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__dashboard_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__dashboard_id
#: model:ir.ui.menu,name:izi_dashboard.izi_dashboard_menu
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard
#, python-format
msgid "Dashboard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__block_ids
msgid "Dashboard Blocks"
msgstr ""

#. module: izi_dashboard
#: model:ir.actions.act_window,name:izi_dashboard.action_izi_dashboard_config_wizard
msgid "Dashboard Config Wizard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Dashboard Name"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__theme_id
msgid "Dashboard Theme"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.constraint,message:izi_dashboard.constraint_izi_dashboard_theme_name_unique
msgid "Dashboard Theme Name Already Exist."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Data"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Data Script Editor"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_analysis_item.xml:0
#, python-format
msgid "Data Source / Table"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dimension_item.xml:0
#, python-format
msgid "Date"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__date_format
msgid "Date Filter"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Date From"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Date To"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__default_config_value
msgid "Default Config Value"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__default_gs_h
msgid "Default Gridstack H"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__default_gs_w
msgid "Default Gridstack W"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Delete"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Delete Dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_sort_item.xml:0
#, python-format
msgid "Desc"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__dimension_field_ids
msgid "Dimension Fields"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__display_name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__display_name
msgid "Display Name"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__dracula
msgid "Dracula"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#, python-format
msgid "Drag & Drop Files Here"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "Duplicate Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dashboard_item.xml:0
#, python-format
msgid "Edit Dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Edit Layout"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Edit Table"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Embed Dashboard"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Enable"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__animation
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__animation
msgid "Enable Animation"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__end_date
msgid "End Date"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "English"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Execute Query and Build Schema"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__expired_date
msgid "Expired Date"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Explain With AI"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid "Explore Analysis Variations With AI"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Explore Variations"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "Export Configuration"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "Export Excel"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__fade
msgid "Fade"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__table_field_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__field_id
msgid "Field"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Field %s Not Found"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__model_field_id
msgid "Field Model"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__model_field_values
msgid "Field Values"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Fields"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__code_filename
msgid "Filename"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__filter_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__filter_id
msgid "Filter"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__filter_analysis_ids
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__filter_analysis_ids
msgid "Filter Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Filter By Category"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Filter By Visual Type"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Filter Date Format"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Filter Date Range"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "Filter Field"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "Filter Limit"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__filter_ids
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Filters"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__font_color
msgid "Font Color"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__font_size
msgid "Font Size"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "From"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid "Generate Code"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_slide_form
#, python-format
msgid "Generate With AI"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Get Insights"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__gs_h
msgid "Gridstack H"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__gs_w
msgid "Gridstack W"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__gs_x
msgid "Gridstack X"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__gs_y
msgid "Gridstack Y"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__group_ids
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Groups"
msgstr ""

#. module: izi_dashboard
#: model:ir.module.category,description:izi_dashboard.module_category_izi_dashboard
msgid "Helps you handle your dashboard."
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__id
msgid "ID"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__ir_actions_act_window_view__view_mode__izianalysis
#: model:ir.model.fields.selection,name:izi_dashboard.selection__ir_ui_view__type__izianalysis
msgid "IZI Analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_analysis_drilldown_dimension
msgid "IZI Analysis Drilldown Demension"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_analysis_visual_config
msgid "IZI Analysis Visual Config"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__ir_actions_act_window_view__view_mode__izidashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__ir_ui_view__type__izidashboard
#: model:ir.module.category,name:izi_dashboard.module_category_izi_dashboard
msgid "IZI Dashboard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__izi_dashboard_access_token
msgid "IZI Dashboard Access Token"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_block
msgid "IZI Dashboard Block"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_config_wizard
msgid "IZI Dashboard Config Wizard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_filter
msgid "IZI Dashboard Filter"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_filter_analysis
msgid "IZI Dashboard Filter Analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_filter_value
msgid "IZI Dashboard Filter Value"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_slide
msgid "IZI Dashboard Slide"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_theme
msgid "IZI Dashboard Theme"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_dashboard_token
msgid "IZI Dashboard Token"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.view_res_company_form
msgid "IZI Lab"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__izi_lab_api_key
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__izi_lab_api_key
#: model:ir.model.fields,field_description:izi_dashboard.field_res_company__izi_lab_api_key
msgid "IZI Lab API Key"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_lab_api_key_wizard
msgid "IZI Lab API Key Wizard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__izi_lab_url
msgid "IZI Lab URL"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_table
msgid "IZI Table"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_visual_config
msgid "IZI Visual Config"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_izi_visual_config_value
msgid "IZI Visual Config Value"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/js/izi_analysis_view.js:0
#: code:addons/izi_dashboard/static/src/js/izi_analysis_view_legacy.js:0
#, python-format
msgid "IZIAnalysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/js/izi_dashboard_view.js:0
#: code:addons/izi_dashboard/static/src/js/izi_dashboard_view_legacy.js:0
#, python-format
msgid "IZIDashboard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__icon
msgid "Icon"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Import Configuration"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Import Data"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config__config_type__input_number
msgid "Input Number"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config__config_type__input_string
msgid "Input String"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Insights"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__text_align__justify
msgid "Justify"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__lang_id
msgid "Language"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_10
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last 10 Days"
msgstr "10 Ngày gần nhất"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_two_months
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last 2 Months"
msgstr "2 Tháng gần nhất"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_three_months
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last 3 Months"
msgstr "3 Tháng gần nhất"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_30
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last 30 Days"
msgstr "30 Ngày gần nhất"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_60
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last 60 Days"
msgstr "60 Ngày gần nhất"

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value____last_update
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type____last_update
msgid "Last Modified on"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_month
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last Month"
msgstr "Tháng trước"

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__write_uid
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_config_wizard__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_lab_api_key_wizard__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__write_date
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_week
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last Week"
msgstr "Tuần trước"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__last_year
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Last Year"
msgstr "Năm trước"

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__layout
msgid "Layout"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__layout_order
msgid "Layout Order"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__league
msgid "League"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__text_align__left
msgid "Left"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "Limit"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "Limit Analysis"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_lab_api_key_wizard
msgid "Login to Lab"
msgstr ""

#. module: izi_dashboard
#: model:res.groups,name:izi_dashboard.group_manager_dashboard
msgid "Manager: All Dashboard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__max_gs_h
msgid "Maximum Gridstack H"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__max_gs_w
msgid "Maximum Gridstack W"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_dashboard_lab.py:0
#, python-format
msgid "Maximum Retry Count."
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_ir_ui_menu
msgid "Menu"
msgstr "Trình đơn"

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__menu_ids
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Menus"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__metric_field_ids
msgid "Metric Fields"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__min_gs_h
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__min_gs_h
msgid "Minimum Gridstack H"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__min_gs_w
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__min_gs_w
msgid "Minimum Gridstack W"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__model_id
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__source_type__model
msgid "Model"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dimension_item.xml:0
#, python-format
msgid "Month"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__mtd
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Month to Date"
msgstr "Từ đầu tháng đến nay"

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__moon
msgid "Moon"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__selection_type__multiple
msgid "Multiple"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_theme__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__name
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__name
msgid "Name"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_dashboard.py:0
#: code:addons/izi_dashboard/models/common/izi_dashboard.py:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_config_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_dashboard.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_view_visual.js:0
#, python-format
msgid "Need API Access"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__new_block_position
msgid "New Chart Position"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "New Table"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__night
msgid "Night"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__none
msgid "None"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config_value__value_type__number
msgid "Number"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "OR"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "Open Analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__operator
msgid "Operator"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__parent_analysis_id
msgid "Parent Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_dashboard_lab.py:0
#, python-format
msgid "Please Set IZI Lab URL in System Parameters."
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_dashboard_lab.py:0
#, python-format
msgid "Please Set The Table First For AI Assistance."
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_analysis_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_dashboard_lab.py:0
#: code:addons/izi_dashboard/models/common/izi_dashboard_lab.py:0
#, python-format
msgid "Please set IZI Lab URL in System Parameters."
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__source_type__predefined
msgid "Predefined"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Present Slides"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Present With AI"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_slide
msgid "Presentation"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_config_wizard
msgid "Process"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dimension_item.xml:0
#, python-format
msgid "Quarter"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__query_special_variable
msgid "Query Special Variable"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__rtl
msgid "RTL"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__rtl
msgid "RTL (Right to Left)"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__refresh_interval
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__refresh_interval
msgid "Refresh Interval in Seconds"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "Remove Analysis"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__render_visual_script
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Render Visual Script"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__is_repeat
msgid "Repeat Slide"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__text_align__right
msgid "Right"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "SQL Query"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_slide_form
#, python-format
msgid "Save"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Save & Close"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid "Save & Run"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid "Save Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "Save Filter"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Save Layout"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Save Table"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid ""
"Script to render visual in JS format.<br/>\n"
"                        You can use these variables: visual.title, visual.idElm, visual.data, visual.dimesion and visual.metric."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Search Analysis Name.."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_dashboard.xml:0
#, python-format
msgid "Search Dashboard Name ..."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_analysis_item.xml:0
#, python-format
msgid "Select Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dashboard_item.xml:0
#, python-format
msgid "Select Dashboard"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Select Date"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Select Themes"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config__config_type__selection_number
msgid "Selection Number"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config__config_type__selection_string
msgid "Selection String"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__selection_type
msgid "Selection Type"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__sequence
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__sequence
msgid "Sequence"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__serif
msgid "Serif"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#, python-format
msgid "Share Dashboard"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__show_logo
msgid "Show Logo"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__simple
msgid "Simple"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__selection_type__single
msgid "Single"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__sky
msgid "Sky"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__slide_ids
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__slide
msgid "Slide"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__slide_name
msgid "Slide Name"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__slide_title
msgid "Slide Title"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_form
msgid "Slides"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__solarized
msgid "Solarized"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Source %s Not Found"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__source_type
msgid "Source Type"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__start_date
msgid "Start Date"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config_value__value_type__string
msgid "String"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__string_value
msgid "String Value"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_lab_api_key_wizard
msgid "Submit API Key"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/common/izi_analysis.py:0
#, python-format
msgid "Successfully Get Data Analysis"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_metric_item.xml:0
#, python-format
msgid "Sum"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/js/component/main/izi_add_analysis.js:0
#: code:addons/izi_dashboard/static/src/js/component/main/izi_select_analysis.js:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__table_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__table_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_analysis__table_id
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__source_type__table
#, python-format
msgid "Table"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Table %s Has No Model"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Table %s Not Found"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__table_field_names
msgid "Table Field Names"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__table_name
msgid "Table Name"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_analysis_form_without_footer
msgid "Test Amchart"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "Test Query"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout_order__text_chart
msgid "Text -> Chart"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__text_align
msgid "Text Align"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__text_content
msgid "Text Content"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout__text
msgid "Text Only"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_slide__text_size
msgid "Text Size (%)"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__theme
msgid "Theme"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__theme_name
msgid "Theme Name"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid ""
"These are some generated analysis from AI. Select one or more generated "
"analysis below and then click the button to save it!"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__this_month
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "This Month"
msgstr "Tháng này"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__this_week
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "This Week"
msgstr "Tuần này"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__this_year
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "This Year"
msgstr "Năm nay"

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__title
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__title
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__title
msgid "Title"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout__title
msgid "Title Slide"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_filter_temp.xml:0
#, python-format
msgid "To"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__today
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Today"
msgstr "Hôm nay"

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_visual_config__config_type__toggle
msgid "Toggle"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__token
msgid "Token"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__new_block_position__top
msgid "Top"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard__transition
msgid "Transition"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout__column
msgid "Two Columns"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_slide__layout__row
msgid "Two Rows"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "Update"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__model_field_values__field
msgid "Use Field Values"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter__model_field_values__id
msgid "Use ID"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__use_render_visual_script
msgid "Use Render Visual Script"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_token__user_id
msgid "User"
msgstr ""

#. module: izi_dashboard
#: model:res.groups,name:izi_dashboard.group_user_dashboard
msgid "User: Own Dashboard Only"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter_value__value
#, python-format
msgid "Value"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__value_type
msgid "Value Type"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_filter__value_ids
msgid "Values"
msgstr ""

#. module: izi_dashboard
#: model:ir.model,name:izi_dashboard.model_ir_ui_view
msgid "View"
msgstr "Xem"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard_block.xml:0
#, python-format
msgid "View List"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:izi_dashboard.field_ir_ui_view__type
msgid "View Type"
msgstr "Dạng hiển thị"

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Visual"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__visual_config_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config_value__visual_config_ids
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_type__visual_config_ids
msgid "Visual Config"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.constraint,message:izi_dashboard.constraint_izi_visual_config_name_unique
msgid "Visual Config Name Already Exist."
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_visual_config__visual_config_value_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__visual_config_value_ids
msgid "Visual Config Value"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "Visual Script Editor"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#: model:ir.model,name:izi_dashboard.model_izi_visual_type
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis__visual_type_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_analysis_drilldown_dimension__visual_type_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__visual_type_id
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_visual_config__visual_type_ids
#, python-format
msgid "Visual Type"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Visual Type %s Must Have At Least One Dimension"
msgstr ""

#. module: izi_dashboard
#. odoo-python
#: code:addons/izi_dashboard/models/wizard/izi_dashboard_config_wizard.py:0
#, python-format
msgid "Visual Type %s Not Found"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields,field_description:izi_dashboard.field_izi_dashboard_block__visual_type_name
msgid "Visual Type Name"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.constraint,message:izi_dashboard.constraint_izi_visual_type_name_unique
msgid "Visual Type Name Already Exist."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dimension_item.xml:0
#, python-format
msgid "Week"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__theme__white
msgid "White"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_dimension_item.xml:0
#, python-format
msgid "Year"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_dashboard.xml:0
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__date_format__ytd
#: model_terms:ir.ui.view,arch_db:izi_dashboard.dashboard_page
#, python-format
msgid "Year to Date"
msgstr "Từ đầu năm đến nay"

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_lab_api_key_wizard
msgid ""
"You are accessing the <b>Premium Features of IZI Analytic Dashboard.</b> \n"
"                        You need to input a valid API key to continue your access. \n"
"                        Please get your API key from your <b>IZI Lab Account</b> and input the API key here or in the company settings."
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard__transition__zoom
msgid "Zoom"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#, python-format
msgid "and"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_analysis.xml:0
#, python-format
msgid "close"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter_analysis__operator__ilike
msgid "ilike"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter_analysis__operator__in
msgid "in"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter_analysis__operator__like
msgid "like"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_view_dashboard.xml:0
#, python-format
msgid "magic_button"
msgstr ""

#. module: izi_dashboard
#: model:ir.model.fields.selection,name:izi_dashboard.selection__izi_dashboard_filter_analysis__operator__not_in
msgid "not in"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: code:addons/izi_dashboard/static/src/xml/component/qweb/izi_select_filter_item.xml:0
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_config_wizard
#, python-format
msgid "or"
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_select_analysis.xml:0
#, python-format
msgid "search"
msgstr ""

#. module: izi_dashboard
#: model:res.groups,comment:izi_dashboard.group_manager_dashboard
msgid ""
"the user will have access to all dashboard of everyone in the IZI Dashboard "
"application."
msgstr ""

#. module: izi_dashboard
#: model:res.groups,comment:izi_dashboard.group_user_dashboard
msgid ""
"the user will have access to his own dashboard in the IZI Dashboard "
"application."
msgstr ""

#. module: izi_dashboard
#. odoo-javascript
#: code:addons/izi_dashboard/static/src/xml/component/izi_config_analysis.xml:0
#, python-format
msgid "translate"
msgstr ""

#. module: izi_dashboard
#: model_terms:ir.ui.view,arch_db:izi_dashboard.izi_dashboard_slide_form
msgid "white, black, or #f4f4f4"
msgstr ""

#. module: izi_data
#: model:ir.ui.menu,name:izi_data.izi_dashboard_root
msgid "Analytics"
msgstr "Báo Cáo Phân Tích"
