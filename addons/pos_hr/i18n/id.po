# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_hr
# 
# Translators:
# <PERSON>, 2022
# <PERSON>to The <<EMAIL>>, 2022
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:32+0000\n"
"PO-Revision-Date: 2022-09-22 05:54+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_hr
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_config_form_view_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.res_config_settings_view_form
msgid "Allowed Employees"
msgstr "Karyawan yang Diizinkan"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__cashier
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_form_inherit
#: model_terms:ir.ui.view,arch_db:pos_hr.pos_order_list_select_inherit
msgid "Cashier"
msgstr "Kasir"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/js/SelectCashierMixin.js:0
#, python-format
msgid "Change Cashier"
msgstr "Ganti Kasir"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_hr_employee
#: model:ir.model.fields,field_description:pos_hr.field_pos_order__employee_id
#: model:ir.model.fields,field_description:pos_hr.field_report_pos_order__employee_id
#: model_terms:ir.ui.view,arch_db:pos_hr.view_report_pos_order_search_inherit
msgid "Employee"
msgstr "Karyawan"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid "Employee: %s - PoS Config(s): %s \n"
msgstr "Karyawan: %s - Konfigurasi POS: %s \n"

#. module: pos_hr
#: model:ir.model.fields,field_description:pos_hr.field_pos_config__employee_ids
#: model:ir.model.fields,field_description:pos_hr.field_res_config_settings__pos_employee_ids
msgid "Employees with access"
msgstr "Karyawan dengan akses"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_config__employee_ids
#: model:ir.model.fields,help:pos_hr.field_res_config_settings__pos_employee_ids
msgid "If left empty, all employees can log in to the PoS session"
msgstr "Jika dibiarkan kosong, semua karyawan dapat log in ke sesi POS"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/js/SelectCashierMixin.js:0
#, python-format
msgid "Incorrect Password"
msgstr "Password Salah"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Log in to"
msgstr "Log in ke"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/js/SelectCashierMixin.js:0
#, python-format
msgid "Password ?"
msgstr "Password ?"

#. module: pos_hr
#: model:ir.model.fields,help:pos_hr.field_pos_order__employee_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr ""
"Orang yang menggunakan mesin kasir. Bisa merupakan bantuan, siswa atau "
"karyawan sementara."

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Konfigurasi Point of Sale"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_order
msgid "Point of Sale Orders"
msgstr "Order Point of Sale"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "Laporan Order POS"

#. module: pos_hr
#: model:ir.model,name:pos_hr.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesi Point of Sale"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Pindai lencana Anda"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "Select Cashier"
msgstr "Pilih Kasir"

#. module: pos_hr
#. odoo-python
#: code:addons/pos_hr/models/hr_employee.py:0
#, python-format
msgid ""
"You cannot delete an employee that may be used in an active PoS session, "
"close the session(s) first: \n"
msgstr ""
"Anda tidak dapat menghapus karyawan yang mungkin dapat digunakan di sesi POS"
" yang aktif, harap tutup sesi tersebut terlebih dahulu:\n"

#. module: pos_hr
#. odoo-javascript
#: code:addons/pos_hr/static/src/xml/LoginScreen.xml:0
#, python-format
msgid "or"
msgstr "atau"
