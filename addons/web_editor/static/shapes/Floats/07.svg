<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 675 643">
    <style>
		@keyframes move_1_90deg {
			0%     {transform: scale(1) rotate(0deg);}
			1.25%  {transform: scale(.5) rotate(90deg);}
			2.91%  {transform: scale(.5) rotate(90deg);}
			4.16%  {transform: scale(1) rotate(90deg);}
			50%    {transform: scale(1) rotate(90deg);}
			51.25% {transform: scale(.5) rotate(90deg);}
			52.91% {transform: scale(.5) rotate(90deg);}
			54.16% {transform: scale(1) rotate(0deg);}
			100%   {transform: scale(1) rotate(0deg);}
		}
        @keyframes move_2_90deg {
			0%     {transform: scale(1) rotate(0deg);}
			25%     {transform: scale(1) rotate(0deg);}
			26.25%  {transform: scale(.5) rotate(90deg);}
			27.91%  {transform: scale(.5) rotate(90deg);}
			29.16%  {transform: scale(1) rotate(90deg);}
			75%    {transform: scale(1) rotate(90deg);}
			76.25% {transform: scale(.5) rotate(90deg);}
			77.91% {transform: scale(.5) rotate(90deg);}
			79.16% {transform: scale(1) rotate(0deg);}
			100%   {transform: scale(1) rotate(0deg);}
		}
        @keyframes move_1_min_90deg {
			0%     {transform: scale(1) rotate(0deg);}
			1.25%  {transform: scale(.5) rotate(90deg);}
			2.91%  {transform: scale(.5) rotate(90deg);}
			4.16%  {transform: scale(1) rotate(90deg);}
			50%    {transform: scale(1) rotate(90deg);}
			51.25% {transform: scale(.5) rotate(90deg);}
			52.91% {transform: scale(.5) rotate(90deg);}
			54.16% {transform: scale(1) rotate(0deg);}
			100%   {transform: scale(1) rotate(0deg);}
		}
        @keyframes move_2_min_90deg {
			0%     {transform: scale(1) rotate(0deg);}
			25%     {transform: scale(1) rotate(0deg);}
			26.25%  {transform: scale(.5) rotate(90deg);}
			27.91%  {transform: scale(.5) rotate(90deg);}
			29.16%  {transform: scale(1) rotate(90deg);}
			75%    {transform: scale(1) rotate(90deg);}
			76.25% {transform: scale(.5) rotate(90deg);}
			77.91% {transform: scale(.5) rotate(90deg);}
			79.16% {transform: scale(1) rotate(0deg);}
			100%   {transform: scale(1) rotate(0deg);}
		}
		polygon {
			transform-box: fill-box;
			transform-origin: center;
		}
		#poly_1 {animation: move_1_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_2 {animation: move_2_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_3 {animation: move_1_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_4 {animation: move_2_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_5 {animation: move_1_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_6 {animation: move_2_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_7 {animation: move_1_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_8 {animation: move_2_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_9 {animation: move_1_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_10 {animation: move_2_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_11 {animation: move_1_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
		#poly_12 {animation: move_2_min_90deg 24s cubic-bezier(.445, .05, .55, .95) infinite;}
    </style>
    <g transform="translate(0 193)">
        <polygon id="poly_1" points="675 0 562.5 112.5 675 112.5 675 0" fill="#383E45"/>
        <polygon id="poly_2" points="562.5 112.5 450 225 562.5 225 562.5 112.5" fill="#3AADAA"/>
        <polygon id="poly_3" points="562.5 337.5 675 225 562.5 225 562.5 337.5" fill="#3AADAA"/>
        <polygon id="poly_4" points="562.5 337.5 450 225 450 337.5 562.5 337.5" fill="#7C6576"/>
        <polygon id="poly_5" points="337.5 337.5 450 450 450 337.5 337.5 337.5" fill="#7C6576"/>
        <polygon id="poly_6" points="337.5 225 450 337.5 450 225 337.5 225" fill="#F6F6F6"/>
        <polygon id="poly_7" points="225 337.5 337.5 225 225 225 225 337.5" fill="#F6F6F6"/>
        <polygon id="poly_8" points="337.5 337.5 225 450 337.5 450 337.5 337.5" fill="#383E45"/>
        <polygon id="poly_9" points="112.5 337.5 225 450 225 337.5 112.5 337.5" fill="#3AADAA"/>
        <polygon id="poly_10" points="0 337.5 112.5 450 112.5 337.5 0 337.5" fill="#7C6576"/>
        <polygon id="poly_11" points="675 337.5 562.5 450 675 450 675 337.5" fill="#383E45"/>
        <polygon id="poly_12" points="675 225 562.5 112.5 675 112.5 675 225" fill="#7C6576"/>
    </g>
</svg>
