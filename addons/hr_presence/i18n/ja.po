# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_presence
# 
# Translators:
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-23 08:23+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON><PERSON>, 2023\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_presence
#: model:mail.template,body_html:hr_presence.mail_template_presence
msgid ""
"<div>\n"
"                    Dear <t t-out=\"object.name or ''\"><PERSON></t>,<br><br>\n"
"                    Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.<br>\n"
"                    Please, take appropriate measures in order to carry out this work absence.<br>\n"
"                    Do not hesitate to contact your manager or the human resource department.\n"
"                    <br>Best Regards,<br><br>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                     <t t-out=\"object.name or ''\">Abigail Peterson</t>様、<br><br>\n"
"                    こちらの間違いである場合を除き、あなたは出勤しておらず、休暇申請もされていません。<br>\n"
"                    この欠勤に対して、必要な処理をして下さい。<br>\n"
"                    必要な場合は、マネージャーまたは人事部にお問合せ下さい。\n"
"                    <br>宜しくお願い致します。<br><br>\n"
"                </div>\n"
"            "

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Absence/Presence"
msgstr "不在 / 出社"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__absent
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__absent
msgid "Absent"
msgstr "不在"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_hr_employee_base
msgid "Basic Employee"
msgstr "基本社員"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_company
msgid "Companies"
msgstr "会社"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Compose Email"
msgstr "Eメール作成"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_actions_server_action_open_presence_view
msgid "Compute presence and open presence view"
msgstr "出勤を計算し出勤ビューを開く"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__create_uid
msgid "Create Uid"
msgstr "Uidを作成する"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__email_sent
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__email_sent
msgid "Email Sent"
msgstr "Eメール送信済"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "Employee's Presence to Define"
msgstr "未定義の従業員出勤"

#. module: hr_presence
#: model:sms.template,name:hr_presence.sms_template_data_hr_presence
msgid "Employee: Presence Reminder"
msgstr "従業員:出勤リマインダ"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
msgid "Employees"
msgstr "従業員"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: model:sms.template,body:hr_presence.sms_template_data_hr_presence
#, python-format
msgid ""
"Exception made if there was a mistake of ours, it seems that you are not at your office and there is not request of time off from you.\n"
"Please, take appropriate measures in order to carry out this work absence.\n"
"Do not hesitate to contact your manager or the human resource department."
msgstr ""
" こちらの間違いである場合を除き、あなたは出勤しておらず、休暇申請もされていません。\n"
"この欠勤に対して、必要な処理をして下さい。\n"
"必要な場合は、マネージャーまたは人事部にご連絡下さい。"

#. module: hr_presence
#: model:ir.actions.server,name:hr_presence.ir_cron_presence_control_ir_actions_server
#: model:ir.cron,cron_name:hr_presence.ir_cron_presence_control
msgid "HR Presence: cron"
msgstr "HR 出勤: cron"

#. module: hr_presence
#: model:mail.template,name:hr_presence.mail_template_presence
msgid "HR: Employee Absence email"
msgstr "HR: 従業員欠勤Eメール"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_company__hr_presence_last_compute_date
msgid "Hr Presence Last Compute Date"
msgstr "HR出勤最終計算日付"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__hr_presence_state_display
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__hr_presence_state_display
msgid "Hr Presence State Display"
msgstr "HR出勤ステータス表示"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_res_users_log__ip
msgid "IP Address"
msgstr "IPアドレス"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__ip_connected
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__ip_connected
msgid "Ip Connected"
msgstr "IP接続済"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Log"
msgstr "ログ"

#. module: hr_presence
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_base__manually_set_present
#: model:ir.model.fields,field_description:hr_presence.field_hr_employee_public__manually_set_present
msgid "Manually Set Present"
msgstr "手動設定出勤"

#. module: hr_presence
#: model:ir.ui.menu,name:hr_presence.menu_hr_presence_view
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_presence_search
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_search
msgid "Presence"
msgstr "在席"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__present
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__present
msgid "Present"
msgstr "在席"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "SMS"
msgstr "SMS"

#. module: hr_presence
#: model:mail.template,description:hr_presence.mail_template_presence
msgid ""
"Sent manually in presence module when an employee wasn't working despite not"
" being off"
msgstr "従業員が休暇でないにもかかわらず勤務していない場合、出勤モジュールに手動で送信されます。"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as absent"
msgstr "欠勤として設定"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Set as present"
msgstr "出勤として設定"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional email address for this employee."
msgstr "この従業員には仕事用のEメールアドレスがありません。"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "There is no professional mobile for this employee."
msgstr "この従業員には仕事用の携帯電話がありません。"

#. module: hr_presence
#: model_terms:ir.ui.view,arch_db:hr_presence.hr_employee_view_kanban
msgid "Time Off"
msgstr "休暇"

#. module: hr_presence
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_base__hr_presence_state_display__to_define
#: model:ir.model.fields.selection,name:hr_presence.selection__hr_employee_public__hr_presence_state_display__to_define
msgid "To Define"
msgstr "定義する"

#. module: hr_presence
#: model:mail.template,subject:hr_presence.mail_template_presence
msgid "Unexpected Absence"
msgstr "予想外の欠勤"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_res_users_log
msgid "Users Log"
msgstr "ユーザログ"

#. module: hr_presence
#. odoo-python
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#: code:addons/hr_presence/models/hr_employee.py:0
#, python-format
msgid "You don't have the right to do this. Please contact an Administrator."
msgstr "これを行う権限がありません。管理者に連絡して下さい。"

#. module: hr_presence
#: model:ir.model,name:hr_presence.model_ir_websocket
msgid "websocket message handling"
msgstr "WebSocketメッセージ処理"
