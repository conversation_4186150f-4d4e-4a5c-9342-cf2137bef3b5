# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_din5008_repair
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-13 21:51+0000\n"
"PO-Revision-Date: 2024-02-14 08:48+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.2\n"

#. module: l10n_din5008_repair
#: model:ir.model.fields,field_description:l10n_din5008_repair.field_repair_order__l10n_din5008_document_title
msgid "L10N Din5008 Document Title"
msgstr "L10N Din5008 Dokumenttitel"

#. module: l10n_din5008_repair
#: model:ir.model.fields,field_description:l10n_din5008_repair.field_repair_order__l10n_din5008_template_data
msgid "L10N Din5008 Template Data"
msgstr "L10N Din5008 Vorlagendaten"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#, python-format
msgid "Lot/Serial Number"
msgstr "Los-/Seriennummer"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#, python-format
msgid "Printing Date"
msgstr "Druckdatum"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#, python-format
msgid "Product to Repair"
msgstr "Zu reparierendes Produkt"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#: model:ir.model,name:l10n_din5008_repair.model_repair_order
#, python-format
msgid "Repair Order"
msgstr "Reparaturauftrag"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#, python-format
msgid "Repair Quotation"
msgstr "Kostenvoranschlag"

#. module: l10n_din5008_repair
#. odoo-python
#: code:addons/l10n_din5008_repair/models/repair.py:0
#, python-format
msgid "Warranty"
msgstr "Garantie"
