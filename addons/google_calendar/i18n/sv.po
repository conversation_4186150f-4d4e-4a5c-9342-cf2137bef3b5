# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* google_calendar
# 
# Translators:
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# 3eec91a23d05c632ffac786ac42b81b8_b6fff7b <8985b7bc57db860af29969457dbb51b3_1018915>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-06 13:31+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "%(id)s and %(length)s following"
msgstr "%(id)s och %(length)s enligt följande"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s Dagar"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s Timmar"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s Minuter"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "(No title)"
msgstr "(Ingen titel)"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__active
msgid "Active"
msgstr "Aktiv"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"An administrator needs to configure Google Synchronization before you can "
"use it!"
msgstr ""
"En administratör behöver konfigurera Google Synkronisering innan du kan "
"använda det!"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Google APIs plateform or try to stop and restart your calendar"
" synchronisation."
msgstr ""
"Ett fel uppstod när polletten genererades. Din auktoriseringskod kan vara "
"ogiltig eller har redan gått ut [%s]. Du bör kontrollera ditt klient-ID och "
"hemlighet på Google APIs plattform eller försöka stoppa och starta om din "
"kalendersynkronisering."

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "Information till kalenderdeltagare"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "Kalenderhändelse"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_cal_id
msgid "Calendar ID"
msgstr "Kalender-ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Cancel"
msgstr "Avbryt"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "Klient-ID"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "Klienthemlighet"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_id
msgid "Client_id"
msgstr "Client_id"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_config_settings__cal_client_secret
msgid "Client_key"
msgstr "Client_key"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "Inställningar"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Configuration"
msgstr "Konfiguration"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Confirm"
msgstr "Bekräfta"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__create_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__create_date
msgid "Created on"
msgstr "Skapad"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Ta bort från Odoo"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "Ta bort från båda"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__delete_google
msgid "Delete from the current Google Calendar account"
msgstr "Ta bort från det aktuella Google Kalender-kontot"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__display_name
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Email"
msgstr "E-post"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "Regel för återkommande händelse"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.xml:0
#, python-format
msgid "Google"
msgstr "Google"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Google Calendar"
msgstr "Google Kalender"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_account_id
msgid "Google Calendar Account"
msgstr "Google Kalender-konto"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_credentials
msgid "Google Calendar Account Data"
msgstr "Kontodata för Google Kalender"

#. module: google_calendar
#: model:ir.actions.act_window,name:google_calendar.google_calendar_reset_account_action
#: model:ir.model,name:google_calendar.model_google_calendar_account_reset
msgid "Google Calendar Account Reset"
msgstr "Återställning av Google Kalender-konto"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__google_id
msgid "Google Calendar Event Id"
msgstr "Händelse-ID för Google Kalender"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__google_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__google_id
msgid "Google Calendar Id"
msgstr "Google Kalender Id"

#. module: google_calendar
#: model:ir.actions.server,name:google_calendar.ir_cron_sync_all_cals_ir_actions_server
#: model:ir.cron,cron_name:google_calendar.ir_cron_sync_all_cals
msgid "Google Calendar: synchronization"
msgstr "Google Kalender: synkronisering"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__synchronization_stopped
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_synchronization_stopped
msgid "Google Synchronization stopped"
msgstr "Google Synkronisering stoppad"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "Google gave the following explanation: %s"
msgstr "Google gav följande förklaring: %s"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__id
msgid "ID"
msgstr "ID"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr ""
"Om det aktiva fältet är falskt, tillåter det dig att gömma händelselarm utan"
" att radera det."

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_credentials__calendar_cal_id
#: model:ir.model.fields,help:google_calendar.field_res_users__google_calendar_cal_id
msgid ""
"Last Calendar ID who has been synchronized. If it is changed, we remove all "
"links between GoogleID and Odoo Google Internal ID"
msgstr ""
"Senaste kalender-ID som har synkroniserats. Om det ändras tar vi bort alla "
"länkar mellan GoogleID och Odoo Google Internal ID"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset____last_update
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials____last_update
msgid "Last Modified on"
msgstr "Senast redigerad den"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_uid
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__write_date
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad på"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "Lämna dem orörda"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_calendar_event__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_calendar_recurrence__need_sync
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_sync__need_sync
msgid "Need Sync"
msgstr "Behöver synkroniseras"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_sync_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_sync_token
msgid "Next Sync Token"
msgstr "Nästa synkpollett"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "Nästa synkronisering"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/calendar.py:0
#, python-format
msgid "Notification"
msgstr "Avisering"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_rtoken
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_rtoken
msgid "Refresh Token"
msgstr "Uppdatera token"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.view_users_form
msgid "Reset Account"
msgstr "Återställ konto"

#. module: google_calendar
#: model_terms:ir.ui.view,arch_db:google_calendar.google_calendar_reset_account_view_form
msgid "Reset Google Calendar Account"
msgstr "Återställ Google Kalender-konto"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "Success"
msgstr "Framgång"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_google_calendar_sync
msgid "Synchronize a record with Google Calendar"
msgstr "Synkronisera en post med Google Kalender"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "Synkronisera alla befintliga evenemang"

#. module: google_calendar
#: model:ir.model.fields.selection,name:google_calendar.selection__google_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "Synkronisera endast nya händelser"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"The Google Synchronization needs to be configured before you can use it, do "
"you want to do it now?"
msgstr ""
"Google Synchronization måste konfigureras innan du kan använda det, vill du "
"göra det nu?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_credentials.py:0
#, python-format
msgid "The account for the Google Calendar service is not configured."
msgstr "Kontot för Google Kalender-tjänsten är inte konfigurerat."

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"The following event could not be synced with Google Calendar. </br>It will "
"not be synced as long at it is not updated.</br>%(reason)s"
msgstr ""
"Följande händelse kunde inte synkroniseras med Google Kalender. </br>  Det "
"kommer inte att synkroniseras så länge det inte är "
"uppdaterat.</br>%(reason)s"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid "The synchronization with Google calendar was successfully stopped."
msgstr "Synkroniseringen med Google Kalender har stoppats framgångsrikt."

#. module: google_calendar
#: model:ir.model.constraint,message:google_calendar.constraint_res_users_google_token_uniq
msgid "The user has already a google account"
msgstr "Användaren har redan ett Google-konto"

#. module: google_calendar
#: model:ir.model.fields,help:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "Detta påverkar endast händelser som användaren är ägare till"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token_validity
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token_validity
msgid "Token Validity"
msgstr "Pollettens giltighet"

#. module: google_calendar
#: model:ir.model,name:google_calendar.model_res_users
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__user_id
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__user_ids
msgid "User"
msgstr "Användare"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_credentials__calendar_token
#: model:ir.model.fields,field_description:google_calendar.field_res_users__google_calendar_token
msgid "User token"
msgstr "Användarpollett"

#. module: google_calendar
#: model:ir.model.fields,field_description:google_calendar.field_google_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "Användarens befintliga händelser"

#. module: google_calendar
#. odoo-javascript
#: code:addons/google_calendar/static/src/views/google_calendar/google_calendar_controller.js:0
#, python-format
msgid ""
"You are about to stop the synchronization of your calendar with Google. Are "
"you sure you want to continue?"
msgstr ""
"Du är på väg att stoppa synkroniseringen av din kalender med Google. Är du "
"säker på att du vill fortsätta?"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid "undefined time"
msgstr "obestämd tid"

#. module: google_calendar
#. odoo-python
#: code:addons/google_calendar/models/google_sync.py:0
#, python-format
msgid ""
"you don't seem to have permission to modify this event on Google Calendar"
msgstr ""
"du verkar inte ha behörighet att ändra den här händelsen i Google Kalender"
