# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
from odoo import fields, models, api


class AccountMoveReject(models.TransientModel):
    _inherit = 'account.move.reject'

    is_account_move_extend = fields.Boolean(default=False)

    @api.onchange('reject_reason')
    def _compute_is_account_move_extend(self):
        move = self.env['account.move'].browse(self.env.context.get('active_ids'))
        model_data = self.env['ir.model.data'].sudo().search([
            ('name', '=', 'welly_registration_form_extend'),
            ('model', '=', 'welly.registration.form'),
        ]).mapped('res_id')
        registration_form_id_extend = model_data[0] if model_data else False
        if move and move.is_clone and move.registration_form_id.id == registration_form_id_extend:
            self.is_account_move_extend = True
