# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * website_sale_stock
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-18 14:08+0000\n"
"PO-Revision-Date: 2016-04-21 09:34+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/odoo/"
"odoo-9/language/en_GB/)\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.orders_followup_shipping
msgid ""
"<span class=\"label label-danger label-text-align\"><i class=\"fa fa-fw fa-"
"times\"/> Cancelled</span>"
msgstr ""
"<span class=\"label label-danger label-text-align\"><i class=\"fa fa-fw fa-"
"times\"/> Cancelled</span>"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.orders_followup_shipping
msgid ""
"<span class=\"label label-info label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Preparation</span>"
msgstr ""
"<span class=\"label label-info label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Preparation</span>"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.orders_followup_shipping
msgid ""
"<span class=\"label label-success label-text-align\"><i class=\"fa fa-fw fa-"
"truck\"/> Shipped</span>"
msgstr ""
"<span class=\"label label-success label-text-align\"><i class=\"fa fa-fw fa-"
"truck\"/> Shipped</span>"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.orders_followup_shipping
msgid ""
"<span class=\"label label-warning label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Partially Available</span>"
msgstr ""
"<span class=\"label label-warning label-text-align\"><i class=\"fa fa-fw fa-"
"clock-o\"/> Partially Available</span>"

#. module: website_sale_stock
#: model_terms:ir.ui.view,arch_db:website_sale_stock.orders_followup_shipping
msgid "<strong>Delivery Orders</strong>"
msgstr "<strong>Delivery Orders</strong>"
