from odoo import Command, models, fields, api
import regex
from odoo.exceptions import ValidationError

class HrDepartment(models.Model):
    _inherit = "hr.department"
    
    @api.constrains('name')
    def _check_name(self):
        for record in self:
            if not regex.match(r"^(?!\s*$)(?!\s*\d)[\p{L}\p{N}\s]+$", record.name):
                raise ValidationError('Tên phòng ban phải là chữ cái, số và dấu cách, và bắt đầu bằng chữ cái')

    attendance_manager_ids = fields.Many2many(
        "res.users",
        relation="hr_department_attendance_manager_rel",
        column1="department_id",
        column2="user_id",
        string="Người được phép tạo ca làm việc",
        tracking=True
    )
    
    # Kiểm tra sự thay đổi của người được phép tạo ca làm việc và cập nhật lại người dùng trong nhóm hr_attendance.hr_attendance_user
    @api.constrains('attendance_manager_ids')
    def _check_attendance_manager_ids(self):
        for record in self:
            for user in record.attendance_manager_ids:
                # Thêm quyền truy cập vào nhóm hr_attendance.group_hr_attendance_user
                if not user.has_group('hr_attendance.group_hr_attendance_user'):
                    self.env.ref('hr_attendance.group_hr_attendance_user').users = [Command.link(user.id)]

    def write(self, vals):
        if 'attendance_manager_ids' in vals:
            old_attendance_manager_ids = self.attendance_manager_ids.ids
            res = super(HrDepartment, self).write(vals)
            new_attendance_manager_ids = self.attendance_manager_ids.ids
            
            # Lấy các phần tử khác nhau giữa hai mảng (phần tử có trong mảng này nhưng không có trong mảng kia)
            different_user_ids = set(new_attendance_manager_ids) ^ set(old_attendance_manager_ids)
            self.env['res.users'].sudo().browse(list(different_user_ids)).clear_caches()
            return res
            
        return super(HrDepartment, self).write(vals)

    shift_configuration_ids = fields.Many2many('hr.work.shift.configuration', 'hr_work_shift_configuration_department_rel', 'department_id', 'shift_configuration_id', string='Cấu Hình Ca Làm Việc')
    
    def action_view_work_shift_calendar(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Lịch Làm Việc',
            'view_mode': 'gantt,tree',
            'res_model': 'hr.work.shift',
            'domain': [('department_id', '=', self.id)],
            'context': {'search_default_group_by_employee_id': 1}
        }
