<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="mrp_production_subcontracting_form_view" model="ir.ui.view">
        <field name="name">mrp.production.subcontracting.form.view</field>
        <field name="model">mrp.production</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view" />
        <field name="arch" type="xml">
            <xpath expr="//header" position="replace">
                <field name="state" invisible="1"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="replace"/>
            <xpath expr="//div[hasclass('oe_title')]" position="replace"/>
            <xpath expr="//group[@name='group_extra_info']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='operations']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='lot_producing_id']" position="attributes">
                <attribute name="attrs">{'invisible': [('product_tracking', 'in', ('none', False))], 'required': [('product_tracking', 'not in', ('none', False))]}</attribute>
            </xpath>
            <xpath expr="//page[@name='miscellaneous']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@name='bom_label']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//div[@name='bom_div']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='move_byproduct_ids']" position="replace"/>
            <xpath expr="//field[@name='workorder_ids']" position="replace"/>
            <xpath expr="//button[@name='action_generate_serial']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath> 
            <xpath expr="//field[@name='move_finished_ids']" position="replace"/>
            <xpath expr="//field[@name='move_raw_ids']" position="replace">
                <field name="bom_product_ids" invisible="1"/>
                <field name="move_line_raw_ids" force_save="1"
                    context="{'tree_view_ref': 'mrp_subcontracting.mrp_subcontracting_stock_move_line_tree_view', 'default_company_id': company_id, 'default_location_id': location_src_id, 'default_location_dest_id': production_location_id, 'bom_product_ids': bom_product_ids}"
                    />
            </xpath>
            <xpath expr="//sheet" position="inside">
                <footer>
                    <button name="subcontracting_record_component" attrs="{'invisible': ['|', ('state', '=', 'to_close'), ('qty_producing', '&lt;', 0.001)]}" string="Continue" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button name="subcontracting_record_component" attrs="{'invisible': [('state', '!=', 'to_close')]}" string="Record Production" type="object" class="oe_highlight" data-hotkey="q"/>
                    <button string="Discard" special="cancel" data-hotkey="z" />
                </footer>
            </xpath>
            <xpath expr="//div[hasclass('oe_chatter')]" position="replace"/>
        </field>
    </record>

    <record id="mrp_production_subcontracting_portal_form_view" model="ir.ui.view">
        <field name="name">mrp.production.subcontracting.portal.form.view</field>
        <field name="model">mrp.production</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp_production_subcontracting_form_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="attributes">
                <attribute name="options">{'no_create_edit': True, 'no_open': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='product_qty']" position="attributes">
                <attribute name="attrs">{'readonly': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='lot_producing_id']" position="attributes">
                <attribute name="domain">[('id', '=', False)]</attribute>
                <attribute name="options">{'no_create_edit': True, 'no_open': True}</attribute>
            </xpath>
            <xpath expr="//field[@name='move_line_raw_ids']" position="attributes">
                <attribute name="context">{'tree_view_ref': 'mrp_subcontracting.mrp_subcontracting_portal_stock_move_line_tree_view', 'default_company_id': company_id, 'default_location_id': location_src_id, 'default_location_dest_id': production_location_id, 'bom_product_ids': bom_product_ids}</attribute>
            </xpath>
            <xpath expr="//field[@name='forecasted_issue']" position="replace"/>
            <xpath expr="//button[@name='%(mrp.action_change_production_qty)d']" position="replace"/>
            <xpath expr="//button[@name='action_product_forecast_report']" position="replace"/>
            <xpath expr="//button[@name='action_product_forecast_report']" position="replace"/>
        </field>
    </record>

    <record id="mrp_production_subcontracting_tree_view" model="ir.ui.view">
        <field name="name">mrp.production.subcontracting.tree</field>
        <field name="model">mrp.production</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp.mrp_production_tree_view" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="incoming_picking"/>
            </xpath>
        </field>
    </record>

    <record id="mrp_production_subcontracting_filter" model="ir.ui.view">
        <field name="name">mrp.production.subcontracting.select</field>
        <field name="model">mrp.production</field>
        <field name="mode">primary</field>
        <field name="priority">1000</field>
        <field name="inherit_id" ref="mrp.view_mrp_production_filter" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="name" string="Incoming transfer" filter_domain="[('incoming_picking.name', 'ilike', self)]"/>
            </xpath>
        </field>
    </record>
</odoo>

