<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_employee_form_inherit_hr_attendance" model="ir.ui.view">
        <field name="name">hr.employee</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="priority">110</field>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <field name="attendance_state" invisible="1"/>
                <button name="%(hr_attendance_action)d"
                        class="oe_stat_button"
                        icon="fa-clock-o"
                        type="action"
                        context="{'search_default_employee_id': id, 'search_default_check_in_filter': '1'}"
                        groups="hr_attendance.group_hr_attendance_user"
                        help="Worked hours last month">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="hours_last_month_display" widget="float_time"/> Hours
                        </span>
                        <span class="o_stat_text">
                            Last Month
                        </span>
                    </div>
                </button>
                <button name="%(hr_attendance_overtime_action)d"
                        class="oe_stat_button"
                        icon="fa-history"
                        type="action"
                        attrs="{'invisible': [('total_overtime', '=', 0.0)]}"
                        context="{'search_default_employee_id': active_id}"
                        groups="hr_attendance.group_hr_attendance_user">
                    <div class="o_stat_info">
                        <span class="o_stat_value text-success" attrs="{'invisible': [('total_overtime', '&lt;', 0)]}">
                            <field name="total_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_value text-danger" attrs="{'invisible': [('total_overtime', '&gt;=', 0)]}">
                            <field name="total_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_text">Extra Hours</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

    <record id="hr_user_view_form" model="ir.ui.view">
        <field name="name">hr.user.preferences.view.form.attendance.inherit</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="hr.res_users_view_form_profile"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <field name="employee_ids" invisible="1"/>
                <button name="%(hr_attendance_action_employee)d"
                        class="oe_stat_button"
                        icon="fa-calendar"
                        type="action"
                        context="{'search_default_employee_id': employee_ids, 'search_default_check_in_filter': '1'}"
                        groups="base.group_user"
                        help="Worked hours last month">
                    <div class="o_field_widget o_stat_info">
                        <span class="o_stat_value">
                            <field name="hours_last_month_display" widget="float_time"/> Hours
                        </span>
                        <span class="o_stat_text">
                            Last Month
                        </span>
                    </div>
                </button>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="%(hr_attendance_overtime_action)d"
                        class="oe_stat_button"
                        icon="fa-history"
                        type="action"
                        attrs="{'invisible': [('total_overtime', '=', 0.0)]}"
                        context="{'search_default_employee_id': employee_ids}"
                        groups="base.group_user"
                        help="Amount of extra hours">
                    <div class="o_stat_info">
                        <span class="o_stat_value text-success" attrs="{'invisible': [('total_overtime', '&lt;', 0)]}">
                            <field name="total_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_value text-danger" attrs="{'invisible': [('total_overtime', '&gt;=', 0)]}">
                            <field name="total_overtime" widget="float_time"/>
                        </span>
                        <span class="o_stat_text">Extra Hours</span>
                    </div>
                </button>
            </xpath>
        </field>
    </record>

    <!-- employee kanban view specifically for hr_attendance (to check in/out) -->
    <record id="hr_employees_view_kanban" model="ir.ui.view">
        <field name="name">hr.employee.kanban</field>
        <field name="model">hr.employee</field>
        <field name="priority">99</field>
        <field name="arch" type="xml">
            <kanban class="o_hr_employee_attendance_kanban" create="false" action="action_employee_kiosk_confirm" type="object">
                <field name="attendance_state"/>
                <field name="hours_today"/>
                <field name="total_overtime"/>
                <field name="id"/>
                <templates>
                    <t t-name="kanban-box">
                    <div class="oe_kanban_global_click">
                        <div class="o_kanban_image">
                            <img t-att-src="kanban_image('hr.employee.public', 'avatar_128', record.id.raw_value)" alt="Employee"/>
                        </div>
                        <div class="oe_kanban_details">
                            <div id="textbox">
                                <div class="float-end" t-if="record.attendance_state.raw_value == 'checked_in'">
                                    <span id="oe_hr_attendance_status" class="fa fa-circle text-success me-1" role="img" aria-label="Available" title="Available"></span>
                                </div>
                                <div class="float-end" t-if="record.attendance_state.raw_value == 'checked_out'">
                                    <span id="oe_hr_attendance_status" class="fa fa-circle text-warning me-1"
                                          role="img" aria-label="Not available" title="Not available">
                                    </span>
                                </div>
                                <strong>
                                    <field name="name"/>
                                </strong>
                            </div>
                            <ul>
                                <li t-if="record.job_id.raw_value"><field name="job_id"/></li>
                                <li t-if="record.work_location_id.raw_value"><field name="work_location_id"/></li>
                            </ul>
                        </div>
                    </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_employee_attendance_action_kanban" model="ir.actions.act_window">
        <field name="name">Employees</field>
        <field name="res_model">hr.employee.public</field>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="hr_employees_view_kanban"/>
        <field name="target">fullscreen</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new employee
            </p><p>
                Add a few employees to be able to select an employee here and perform his check in / check out.
                To create employees go to the Employees menu.
            </p>
        </field>
    </record>

</odoo>
