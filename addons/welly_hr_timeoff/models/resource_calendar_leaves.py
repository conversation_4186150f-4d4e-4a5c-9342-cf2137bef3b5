from odoo import models, fields, api
from odoo.exceptions import ValidationError
from datetime import timedelta

class ResourceCalendarLeaves(models.Model):
    _name = 'resource.calendar.leaves'
    _inherit = ['resource.calendar.leaves', 'mail.thread']
    
    # Thêm tracking cho các trường name, date_from, date_to,
    name = fields.Char(string='Tên', tracking=True, required=True)
    date_from = fields.Date(string='Ngày bắt đầu', tracking=True, required=True, index=True)
    date_to = fields.Date(string='Ngày kết thúc', tracking=True, required=True, index=True)
    
    # Trường liên kết với ca làm việc
    shift_ids = fields.One2many('hr.work.shift', 'resource_calendar_leaves_id', string='Ca làm việc')
    
    # Trường selection để chọn áp dụng cho ca làm việc nào
    apply_for_shift_type = fields.Selection([
        ('fixed', 'Cố <PERSON>'),
        ('flexible', '<PERSON><PERSON>'),
        ('all', '<PERSON>ố <PERSON> & <PERSON>')
    ], string='Áp dụng cho ca làm việc', required=True, default='fixed', tracking=True)
    
    @api.constrains('date_from', 'date_to', 'apply_for_shift_type')
    def _validate_date_from_and_date_to(self):
        for record in self:
            
            # Kiểm tra ngày bắt đầu không thể lớn hơn ngày kết thúc
            if record.date_from and record.date_to and record.date_from > record.date_to:
                raise ValidationError("Ngày bắt đầu không thể lớn hơn ngày kết thúc")
            
            # Update lại nghỉ lễ cho ca làm việc
            record.shift_ids.resource_calendar_leaves_id = False
            domain = [('shift_type', '=', record.apply_for_shift_type)] if record.apply_for_shift_type != 'all' else []
            domain.extend([('date', '>=', record.date_from), ('date', '<=', record.date_to), '|', ('active', '=', True), ('active', '=', False)])
            shift_ids = self.env['hr.work.shift'].search(domain)
            shift_ids.resource_calendar_leaves_id = record.id

    # Màu của ngày nghỉ lễ (mặc định màu tím)
    color = fields.Integer(default=11)

    def unlink(self):
        # Không cho phép xoá đối với những ngày lễ đã diễn ra
        for record in self:
            today = fields.Datetime.now()
            if record.date_from <= today.date():
                raise ValidationError("Không thể xoá ngày lễ đã diễn ra")
        return super(ResourceCalendarLeaves, self).unlink()

