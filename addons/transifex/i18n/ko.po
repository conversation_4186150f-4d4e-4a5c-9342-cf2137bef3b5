# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* transifex
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-23 14:34+0000\n"
"PO-Revision-Date: 2023-04-14 06:18+0000\n"
"Last-Translator: Sarah <PERSON>, 2023\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: transifex
#: model:ir.model,name:transifex.model_base
msgid "Base"
msgstr "기준액"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__source
msgid "Code"
msgstr "코드"

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_code_translation
msgid "Code Translation"
msgstr "코드 번역"

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/fields/translation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
#, python-format
msgid "Contribute"
msgstr "기여"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__display_name
msgid "Display Name"
msgstr "표시명"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Group By"
msgstr "그룹별"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__id
msgid "ID"
msgstr "ID"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__lang
msgid "Language"
msgstr "사용 언어"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__module
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Module"
msgstr "모듈"

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__module
msgid "Module this term belongs to"
msgstr "이 용어가 속한 모듈"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Not Translated"
msgstr "번역되지 않음"

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__transifex_url
msgid "Propose a modification in the official version of Odoo"
msgstr "Odoo 공식 버전 수정 제안"

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/reload_code_translations_views.xml:0
#, python-format
msgid "Reload"
msgstr "새로 고침"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Search Code Translations"
msgstr "코드 번역 검색"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex"
msgstr "Transifex"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex Code Translation"
msgstr "Transifex 코드 번역"

#. module: transifex
#: model:ir.actions.server,name:transifex.action_code_translations
#: model:ir.ui.menu,name:transifex.menu_transifex_code_translations
msgid "Transifex Code Translations"
msgstr "Transifex 코드 번역"

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_translation
msgid "Transifex Translation"
msgstr "Transifex 번역"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__transifex_url
msgid "Transifex URL"
msgstr "Transifex URL"

#. module: transifex
#: model:ir.actions.server,name:transifex.transifex_code_translation_reload_ir_actions_server
#: model:ir.cron,cron_name:transifex.transifex_code_translation_reload
msgid "Transifex: Reload code translations"
msgstr "Transifex: 코드 번역 다시 불러오기"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__value
msgid "Translation Value"
msgstr "번역된 값"
