# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_payumoney
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~15.2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-02-11 14:33+0000\n"
"PO-Revision-Date: 2022-02-11 14:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "Merchant Key"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__payumoney_merchant_salt
msgid "Merchant Salt"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_payumoney
#: model:account.payment.method,name:payment_payumoney.payment_method_payumoney
#: model:ir.model.fields.selection,name:payment_payumoney.selection__payment_acquirer__provider__payumoney
msgid "PayUmoney"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_acquirer
msgid "Payment Acquirer"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_account_payment_method
msgid "Payment Methods"
msgstr ""

#. module: payment_payumoney
#: model:ir.model,name:payment_payumoney.model_payment_transaction
msgid "Payment Transaction"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,field_description:payment_payumoney.field_payment_acquirer__provider
msgid "Provider"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference (%s)"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__provider
msgid "The Payment Service Provider to use with this acquirer"
msgstr ""

#. module: payment_payumoney
#: model:ir.model.fields,help:payment_payumoney.field_payment_acquirer__payumoney_merchant_key
msgid "The key solely used to identify the account with PayU money"
msgstr ""

#. module: payment_payumoney
#: code:addons/payment_payumoney/models/payment_transaction.py:0
#, python-format
msgid "The payment encountered an error with code %s"
msgstr ""
