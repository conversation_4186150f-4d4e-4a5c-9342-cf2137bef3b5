# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
previous_label=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§
next.title=à¦ªà¦°à¦¬à¦°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
next_label=à¦ªà¦°à¦¬à¦°à§à¦¤à§

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=à¦ªà§à¦
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pagesCount}} à¦à¦° {{pageNumber}})

zoom_out.title=à¦à§à¦ à¦®à¦¾à¦ªà§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_out_label=à¦à§à¦ à¦®à¦¾à¦ªà§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_in.title=à¦¬à§ à¦®à¦¾à¦ªà§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_in_label=à¦¬à§ à¦®à¦¾à¦ªà§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom.title=à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨à§à¦° à¦®à¦¾à¦ª
presentation_mode.title=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨à¦¾ à¦®à§à¦¡ à¦¸à§à¦¯à§à¦à¦ à¦à¦°à§à¦¨
presentation_mode_label=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨à¦¾ à¦®à§à¦¡
open_file.title=à¦«à¦¾à¦à¦² à¦à§à¦²à§à¦¨
open_file_label=à¦à§à¦²à§à¦¨
print.title=à¦ªà§à¦°à¦¿à¦¨à§à¦ à¦à¦°à§à¦¨
print_label=à¦ªà§à¦°à¦¿à¦¨à§à¦ à¦à¦°à§à¦¨
download.title=à¦¡à¦¾à¦à¦¨à¦²à§à¦¡ à¦à¦°à§à¦¨
download_label=à¦¡à¦¾à¦à¦¨à¦²à§à¦¡ à¦à¦°à§à¦¨
bookmark.title=à¦¬à¦°à§à¦¤à¦®à¦¾à¦¨ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨ (à¦à¦ªà¦¿ à¦à¦°à§à¦¨ à¦à¦¥à¦¬à¦¾ à¦¨à¦¤à§à¦¨ à¦à¦à¦¨à§à¦¡à§à¦¤à§ à¦à§à¦²à§à¦¨)
bookmark_label=à¦¬à¦°à§à¦¤à¦®à¦¾à¦¨ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨

# Secondary toolbar and context menu
tools.title=à¦¸à¦°à¦à§à¦à¦¾à¦®
tools_label=à¦¸à¦°à¦à§à¦à¦¾à¦®
first_page.title=à¦ªà§à¦°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
first_page.label=à¦ªà§à¦°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
first_page_label=à¦ªà§à¦°à¦¥à¦® à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
last_page.title=à¦¸à¦°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
last_page.label=à¦¸à¦°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
last_page_label=à¦¸à¦°à§à¦¬à¦¶à§à¦· à¦ªà§à¦·à§à¦ à¦¾à§ à¦à¦²à§à¦¨
page_rotate_cw.title=à¦¡à¦¾à¦¨à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§
page_rotate_cw.label=à¦¡à¦¾à¦¨à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§
page_rotate_cw_label=à¦¡à¦¾à¦¨à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§
page_rotate_ccw.title=à¦¬à¦¾à¦à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§
page_rotate_ccw.label=à¦¬à¦¾à¦à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§
page_rotate_ccw_label=à¦¬à¦¾à¦à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦¨à§ à¦¹à¦¬à§

cursor_text_select_tool.title=à¦à§à¦à§à¦¸à¦ à¦¨à¦¿à¦°à§à¦¬à¦¾à¦à¦¨ à¦¸à¦°à¦à§à¦à¦¾à¦® à¦¸à¦à§à¦°à¦¿à¦¯à¦¼ à¦à¦°à§à¦¨
cursor_text_select_tool_label=à¦à§à¦à§à¦¸à¦ à¦¨à¦¿à¦°à§à¦¬à¦¾à¦à¦¨à§à¦° à¦¸à¦°à¦à§à¦à¦¾à¦®
cursor_hand_tool.title=à¦¹à§à¦¯à¦¾à¦¨à§à¦¡ à¦à§à¦² à¦¸à¦à§à¦°à¦¿à¦¯à¦¼ à¦à¦°à§à¦¨
cursor_hand_tool_label=à¦¹à§à¦¯à¦¾à¦¨à§à¦¡ à¦à§à¦²

scroll_vertical.title=à¦à¦²à§à¦²à¦®à§à¦¬ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à§à¦¨
scroll_vertical_label=à¦à¦²à§à¦²à¦®à§à¦¬ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦
scroll_horizontal.title=à¦à¦¨à§à¦­à§à¦®à¦¿à¦ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à§à¦¨
scroll_horizontal_label=à¦à¦¨à§à¦­à§à¦®à¦¿à¦ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦
scroll_wrapped.title=à¦à¦¬à§à¦¤ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à§à¦¨
scroll_wrapped_label=à¦à¦¬à§à¦¤ à¦¸à§à¦à§à¦°à§à¦²à¦¿à¦

spread_none.title=à¦à¦¡à¦¼à¦¿à¦¯à¦¼à§ à¦ªà¦°à¦¾ à¦ªà¦¾à¦¤à¦¾à¦à§ à¦¯à§à¦ à¦à¦°à¦¬à§à¦¨ à¦¨à¦¾
spread_none_label=à¦à§à¦¾à¦¨à§ à¦¨à§
spread_odd.title=à¦¬à¦¿à¦à§à¦¡à¦¼-à¦¸à¦à¦à§à¦¯à¦¾à¦° à¦ªà§à¦·à§à¦ à¦¾à¦à§à¦²à¦¿à¦° à¦¸à¦¾à¦¥à§ à¦¶à§à¦°à§ à¦¹à¦à¦¯à¦¼à¦¾ à¦ªà§à¦·à§à¦ à¦¾ à¦¸à§à¦ªà§à¦°à§à¦¡à¦à§à¦²à¦¿à¦¤à§ à¦¯à§à¦à¦¦à¦¾à¦¨ à¦à¦°à§à¦¨
spread_odd_label=à¦¬à¦¿à¦à§à¦¡à¦¼ à¦¸à§à¦ªà§à¦°à§à¦¡à¦¸
spread_even.title=à¦à§à¦¡à¦¼-à¦¸à¦à¦à§à¦¯à¦¾à¦° à¦ªà§à¦·à§à¦ à¦¾à¦à§à¦²à¦¿à¦° à¦¸à¦¾à¦¥à§ à¦¶à§à¦°à§ à¦¹à¦à¦¯à¦¼à¦¾ à¦ªà§à¦·à§à¦ à¦¾ à¦¸à§à¦ªà§à¦°à§à¦¡à¦à§à¦²à¦¿à¦¤à§ à¦¯à§à¦à¦¦à¦¾à¦¨ à¦à¦°à§à¦¨
spread_even_label=à¦à§à¦¡à¦¼ à¦¸à§à¦ªà§à¦°à§à¦¡

# Document properties dialog box
document_properties.title=à¦¨à¦¥à¦¿à¦° à¦¬à§à¦¶à¦¿à¦·à§à¦à§à¦¯â¦
document_properties_label=à¦¨à¦¥à¦¿à¦° à¦¬à§à¦¶à¦¿à¦·à§à¦à§à¦¯â¦
document_properties_file_name=à¦«à¦¾à¦à¦²à§à¦° à¦¨à¦¾à¦®:
document_properties_file_size=à¦«à¦¾à¦à¦²à§à¦° à¦®à¦¾à¦ª:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} à¦®à§à¦à¦¾à¦¬à¦¾à¦à¦ ({{size_b}} bytes)
document_properties_title=à¦¶à¦¿à¦°à§à¦¨à¦¾à¦®:
document_properties_author=à¦²à§à¦à¦:
document_properties_subject=à¦¬à¦¿à¦·à§:
document_properties_keywords=à¦¨à¦¿à¦°à§à¦¦à§à¦¶à¦ à¦¶à¦¬à§à¦¦:
document_properties_creation_date=à¦¨à¦¿à¦°à§à¦®à¦¾à¦£à§à¦° à¦¤à¦¾à¦°à¦¿à¦:
document_properties_modification_date=à¦ªà¦°à¦¿à¦¬à¦°à§à¦¤à¦¨à§à¦° à¦¤à¦¾à¦°à¦¿à¦:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¦¨à¦¿à¦°à§à¦®à¦¾à¦¤à¦¾:
document_properties_producer=PDF à¦¨à¦¿à¦°à§à¦®à¦¾à¦¤à¦¾:
document_properties_version=PDF à¦¸à¦à¦¸à§à¦à¦°à¦£:
document_properties_page_count=à¦®à§à¦ à¦ªà§à¦·à§à¦ à¦¾:
document_properties_page_size=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦¸à¦¾à¦à¦:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=à¦à¦²à¦®à§à¦¬
document_properties_page_size_orientation_landscape=à¦à§à¦¾à¦à§à¦¿
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=à¦²à§à¦à¦¾à¦°
document_properties_page_size_name_legal=à¦²à¦¿à¦à¦¾à¦²
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=à¦¦à§à¦°à§à¦¤ à¦à¦¯à¦¼à§à¦¬ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨:
document_properties_linearized_yes=à¦¹à§à¦¯à¦¾à¦
document_properties_linearized_no=à¦¨à¦¾
document_properties_close=à¦¬à¦¨à§à¦§ à¦à¦°à§à¦¨

print_progress_message=à¦¡à¦à§à¦®à§à¦¨à§à¦ à¦ªà§à¦°à¦¿à¦¨à§à¦à¦¿à¦-à¦° à¦à¦¨à§à¦¯ à¦¤à§à¦°à¦¿ à¦à¦°à¦¾ à¦¹à¦à§à¦à§...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=à¦¬à¦¾à¦¤à¦¿à¦²

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à¦¸à¦¾à¦à¦¡à¦¬à¦¾à¦° à¦à¦à¦² à¦à¦°à§à¦¨
toggle_sidebar_notification.title=à¦¸à¦¾à¦à¦¡à¦¬à¦¾à¦° à¦à¦à¦² à¦à¦°à§à¦¨ (à¦¨à¦¥à¦¿à¦¤à§ à¦°à¦¯à¦¼à§à¦à§ à¦à¦à¦à¦²à¦¾à¦à¦¨/à¦¸à¦à¦¯à§à¦à§à¦¤à¦¿)
toggle_sidebar_label=à¦¸à¦¾à¦à¦¡à¦¬à¦¾à¦° à¦à¦à¦² à¦à¦°à§à¦¨
document_outline.title=à¦¡à¦à§à¦®à§à¦¨à§à¦ à¦à¦à¦à¦²à¦¾à¦à¦¨ à¦¦à§à¦à¦¾à¦¨ (à¦¦à§à¦¬à¦¾à¦° à¦à§à¦²à¦¿à¦ à¦à¦°à§à¦¨ à¦¬à¦¾à§à¦¾à¦¤à§//collapse à¦¸à¦®à¦¸à§à¦¤ à¦à¦à¦à§à¦®)
document_outline_label=à¦¡à¦à§à¦®à§à¦¨à§à¦ à¦à¦à¦à¦²à¦¾à¦à¦¨
attachments.title=à¦¸à¦à¦¯à§à¦à§à¦¤à¦¿à¦¸à¦®à§à¦¹ à¦¦à§à¦à¦¾à¦¨
attachments_label=à¦¸à¦à¦¯à§à¦à§à¦¤ à¦¬à¦¸à§à¦¤à§
thumbs.title=à¦¥à¦¾à¦®à§à¦¬-à¦¨à§à¦à¦² à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
thumbs_label=à¦¥à¦¾à¦®à§à¦¬-à¦¨à§à¦à¦² à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
findbar.title=à¦¨à¦¥à¦¿à¦¤à§ à¦à§à¦à¦à§à¦¨
findbar_label=à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à¦°à§à¦¨

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¦ªà§à¦·à§à¦ à¦¾ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¦ªà§à¦·à§à¦ à¦¾ {{page}}-à¦° à¦¥à¦¾à¦®à§à¦¬-à¦¨à§à¦à¦²

# Find panel button title and messages
find_input.title=à¦à§à¦à¦à§à¦¨
find_input.placeholder=à¦¨à¦¥à¦¿à¦° à¦®à¦§à§à¦¯à§ à¦à§à¦à¦à§à¦¨â¦
find_previous.title=à¦à¦¿à¦¹à§à¦¨à¦¿à¦¤ à¦ªà¦à¦à§à¦¤à¦¿à¦° à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à¦°à§à¦¨
find_previous_label=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§
find_next.title=à¦à¦¿à¦¹à§à¦¨à¦¿à¦¤ à¦ªà¦à¦à§à¦¤à¦¿à¦° à¦ªà¦°à¦¬à¦°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨ à¦à¦°à§à¦¨
find_next_label=à¦ªà¦°à¦¬à¦°à§à¦¤à§
find_highlight=à¦¸à¦®à¦à§à¦° à¦à¦à§à¦à§à¦¬à¦² à¦à¦°à§à¦¨
find_match_case_label=à¦¹à¦°à¦«à§à¦° à¦à¦¾à¦à¦¦ à¦®à§à¦²à¦¾à¦¨à§ à¦¹à¦¬à§
find_entire_word_label=à¦¸à¦®à§à¦ªà§à¦°à§à¦£ à¦¶à¦¬à§à¦¦à¦à§à¦²à¦¿
find_reached_top=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦ªà§à¦°à¦¾à¦°à¦®à§à¦­à§ à¦ªà§à¦à§ à¦à§à¦à§, à¦¨à§à¦à§à¦° à¦à¦à¦¶ à¦¥à§à¦à§ à¦à¦°à¦®à§à¦­ à¦à¦°à¦¾ à¦¹à¦¬à§
find_reached_bottom=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦à¦¨à§à¦¤à¦¿à¦® à¦ªà§à¦°à¦¾à¦¨à§à¦¤à§ à¦ªà§à¦à§ à¦à§à¦à§, à¦ªà§à¦°à¦¥à¦® à¦à¦à¦¶ à¦¥à§à¦à§ à¦à¦°à¦®à§à¦­ à¦à¦°à¦¾ à¦¹à¦¬à§
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} à¦à¦° {{current}} à¦ à¦®à¦¿à¦²
find_match_count[two]={{total}} à¦à¦° {{current}} à¦®à¦¿à¦²à¦à§
find_match_count[few]={{total}} à¦à¦° {{current}} à¦®à¦¿à¦²à¦à§
find_match_count[many]={{total}} à¦à¦° {{current}} à¦®à¦¿à¦²à¦à§
find_match_count[other]={{total}} à¦à¦° {{current}} à¦®à¦¿à¦²à¦à§
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} à¦à¦° à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_match_count_limit[one]={{limit}} à¦à¦° à¦¥à§à¦à§ à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_match_count_limit[two]={{limit}} à¦à¦° à¦¥à§à¦à§ à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_match_count_limit[few]={{limit}} à¦à¦° à¦¥à§à¦à§ à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_match_count_limit[many]={{limit}} à¦à¦° à¦¥à§à¦à§ à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_match_count_limit[other]={{limit}} à¦à¦° à¦¥à§à¦à§ à¦¬à§à¦¶à¦¿ à¦®à¦¿à¦²à¦à§
find_not_found=à¦ªà¦à¦à§à¦¤à¦¿ à¦ªà¦¾à¦à§à¦¾ à¦¯à¦¾à§à¦¨à¦¿

# Error panel labels
error_more_info=à¦à¦¤à¦¿à¦°à¦¿à¦à§à¦¤ à¦¤à¦¥à§à¦¯
error_less_info=à¦à¦® à¦¤à¦¥à§à¦¯
error_close=à¦¬à¦¨à§à¦§ à¦à¦°à§à¦¨
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Message: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=File: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Line: {{line}}
rendering_error=à¦ªà§à¦·à§à¦ à¦¾ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨à¦à¦¾à¦²à§ à¦à¦à¦à¦¿ à¦¸à¦®à¦¸à§à¦¯à¦¾ à¦¦à§à¦à¦¾ à¦¦à¦¿à§à§à¦à§à¥¤

# Predefined zoom values
page_scale_width=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦ªà§à¦°à¦¸à§à¦¥ à¦à¦¨à§à¦¯à¦¾à§à§
page_scale_fit=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦®à¦¾à¦ª à¦à¦¨à§à¦¯à¦¾à§à§
page_scale_auto=à¦¸à§à¦¬à§à¦à¦à§à¦°à¦¿à§ à¦®à¦¾à¦ª à¦¨à¦¿à¦°à§à¦§à¦¾à¦°à¦£
page_scale_actual=à¦ªà§à¦°à¦à§à¦¤ à¦®à¦¾à¦ª
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=à¦¤à§à¦°à§à¦à¦¿
loading_error=PDF à¦²à§à¦¡ à¦à¦°à¦¾à¦° à¦¸à¦®à§ à¦¸à¦®à¦¸à§à¦¯à¦¾ à¦¦à§à¦à¦¾ à¦¦à¦¿à§à§à¦à§à¥¤
invalid_file_error=à¦à¦¬à§à¦§ à¦¬à¦¾ à¦à§à¦·à¦¤à¦¿à¦à§à¦°à¦¸à§à¦¤ à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¾à¦à¦²à¥¤
missing_file_error=à¦à¦¨à§à¦ªà¦¸à§à¦¥à¦¿à¦¤ PDF à¦«à¦¾à¦à¦²
unexpected_response_error=à¦¸à¦¾à¦°à§à¦­à¦¾à¦° à¦¥à§à¦à§ à¦à¦ªà§à¦°à¦¤à§à¦¯à¦¾à¦¶à¦¿à¦¤ à¦¸à¦¾à§à¦¾ à¦ªà¦¾à¦à§à¦¾ à¦à§à¦à§à¥¤

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Annotation]
password_label=à¦à¦ PDF à¦«à¦¾à¦à¦² à¦à§à¦²à¦¾à¦° à¦à¦¨à§à¦¯ à¦ªà¦¾à¦¸à¦à¦¯à¦¼à¦¾à¦°à§à¦¡ à¦¦à¦¿à¦¨à¥¤
password_invalid=à¦ªà¦¾à¦¸à¦à§à¦¾à¦°à§à¦¡ à¦¸à¦ à¦¿à¦ à¦¨à§à¥¤ à¦à¦¨à§à¦à§à¦°à¦¹ à¦à¦°à§ à¦ªà§à¦¨à¦°à¦¾à§ à¦ªà§à¦°à¦à§à¦·à§à¦à¦¾ à¦à¦°à§à¦¨à¥¤
password_ok=OK
password_cancel=à¦¬à¦¾à¦¤à¦¿à¦² à¦à¦°à§à¦¨

printing_not_supported=à¦¸à¦¤à¦°à§à¦à¦¬à¦¾à¦°à§à¦¤à¦¾: à¦à¦ à¦¬à§à¦°à¦¾à¦à¦à¦¾à¦° à¦¦à§à¦¬à¦¾à¦°à¦¾ à¦ªà§à¦°à¦¿à¦¨à§à¦ à¦¬à§à¦¯à¦¬à¦¸à§à¦¥à¦¾ à¦¸à¦®à§à¦ªà§à¦°à§à¦£à¦°à§à¦ªà§ à¦¸à¦®à¦°à§à¦¥à¦¿à¦¤ à¦¨à§à¥¤
printing_not_ready=à¦¸à¦¤à¦°à§à¦à¦¬à¦¾à¦£à§: à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦¸à¦®à§à¦ªà§à¦°à§à¦£à¦°à§à¦ªà§ à¦®à§à¦¦à§à¦°à¦£à§à¦° à¦à¦¨à§à¦¯ à¦²à§à¦¡ à¦à¦°à¦¾ à¦¹à¦¯à¦¼ à¦¨à¦¾.
web_fonts_disabled=à¦à¦¯à¦¼à§à¦¬ à¦«à¦¨à§à¦ à¦¨à¦¿à¦·à§à¦à§à¦°à¦¿à¦¯à¦¼ à¦à¦°à¦¾ à¦¹à¦¯à¦¼à§à¦à§: à¦à¦®à¦¬à§à¦¡à§à¦¡ à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¨à§à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à¦¤à§ à¦à¦à§à¦·à¦®.
document_colors_not_allowed=à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦¨à¦¥à¦¿ à¦¤à¦¾à¦¦à§à¦° à¦¨à¦¿à¦à¦¸à§à¦¬ à¦°à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à¦¾à¦° à¦à¦¨à§à¦¯ à¦à¦¨à§à¦®à¦¤à¦¿à¦ªà§à¦°à¦¾à¦ªà§à¦¤ à¦¨à¦¯à¦¼: à¦¬à§à¦°à¦¾à¦à¦à¦¾à¦°à§ à¦¨à¦¿à¦·à§à¦à§à¦°à¦¿à¦¯à¦¼ à¦à¦°à¦¾ à¦¹à¦¯à¦¼à§à¦à§ à§à§à¦¨  'à¦ªà§à¦ à¦¤à¦¾à¦¦à§à¦° à¦¨à¦¿à¦à¦¸à§à¦¬ à¦°à¦ à¦¨à¦¿à¦°à§à¦¬à¦¾à¦à¦¨ à¦à¦°à¦¾à¦° à¦à¦¨à§à¦®à¦¤à¦¿ à¦ªà§à¦°à¦¦à¦¾à¦¨ à¦à¦°à¦¾ à§à¦¾à§à¥¤'
