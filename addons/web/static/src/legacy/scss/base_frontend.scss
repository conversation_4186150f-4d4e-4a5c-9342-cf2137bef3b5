// Frontend general
html, body, #wrapwrap {
    width: 100%;
    height: 100%;
}
#wrapwrap {
    // The z-index is useful to prevent that children with a negative z-index
    // go behind the wrapwrap (we create a new stacking context).
    z-index: 0;
    position: relative;
    display: flex;
    flex-flow: column nowrap;

    > * {
        flex: 0 0 auto;
    }
    > main {
        flex: 1 0 auto;
    }
}
.modal-open #wrapwrap {
    overflow: hidden;
}

// TODO: This whole block can be removed once the scroll bar is moved back from
//       the #wrapwrap to the body. The fact the scroll bar is on the #wrapwrap
//       prevent browser to print more than what is above the fold. This block
//       is moving the scroll to the body when printing the page.
@media screen {
    html, body {
        overflow: hidden;
    }
    #wrapwrap {
        // ... we delegate the scroll to that top element instead of the window/body
        // This is at least needed for the edit mode to not have a double scrollbar
        // due to the right editor panel (and since we want to minimize the style
        // difference between edit mode and non-edit mode (wysiwyg)...).
        overflow: auto;
    }
}
