<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

  <t t-name="web.ActionDialog.header" t-inherit="web.Dialog.header" t-inherit-mode="primary" owl="1">
    <xpath expr="//h4[contains(concat(' ',normalize-space(@class),' '),' modal-title ')]" position="before">
      <DebugMenu t-if="env.debug" />
    </xpath>
  </t>
  <t t-name="web.ActionDialog" t-inherit="web.Dialog" t-inherit-mode="primary" owl="1">
    <xpath expr="//main[hasclass('modal-body')]" position="attributes">
      <attribute name="t-att-class">
        {"o_act_window": props.actionType === "ir.actions.act_window"}
      </attribute>
    </xpath>
    <xpath expr="//t[@t-slot='default']" position="replace">
      <t t-if="props.ActionComponent" t-component="props.ActionComponent" t-props="props.actionProps"/>
    </xpath>
    <xpath expr="//t[@t-slot='header']" position="replace">
      <t t-call="web.ActionDialog.header">
        <t t-set="close" t-value="props.close"/>
        <t t-set="fullscreen" t-value="props.isFullscreen"/>
      </t>
    </xpath>
  </t>

  <t t-name="web.LegacyAdaptedActionDialog" t-inherit="web.ActionDialog" t-inherit-mode="primary" owl="1">
    <xpath expr="//t[@t-slot='footer']" position="replace">
      <t t-if="!isLegacy">
        <t t-slot="buttons">
          <button class="btn btn-primary o-default-button" t-on-click="data.close">Ok</button>
        </t>
      </t>
    </xpath>
  </t>

</templates>
