.ribbon {
    width: 150px;
    height: 150px;
    overflow: hidden;
    position: absolute;

    &::before, &::after {
        position: absolute;
        z-index: -1;
        content: '';
        display: block;
        border: 5px solid #2980b9;
    }

    & span {
        z-index: 1;
        position: absolute;
        display: grid;
        align-items: center;
        width: 225px;
        height: 48px;
        padding: 0 44px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, .1);
        color: #fff;
        font-weight: $o-font-weight-bold;
        font-family: 'Lato', sans-serif;
        text-shadow: 0 1px 1px rgba(0, 0, 0, .2);
        text-transform: uppercase;
        text-align: center;
        overflow: hidden;
        user-select: none;
        &.o_small {
            font-size: 12px;
        }
        &.o_medium {
            font-size: 15px;
        }
    }

    &-top-right {
        margin-top: -$o-sheet-vpadding;
        right: 0;
        pointer-events: none;

        &::before, &::after {
            border-top-color: black;
            border-right-color: black;
        }

        &::before {
            top: 0;
            left: 0;
        }

        &::after {
            bottom: 0;
            right: 0;
        }

        & span {
            left: -15px;
            top: 30px;
            transform: rotate(45deg);
        }
    }
}

// after ribbon widget there can be field widgets or oe_title which may
// have widgets on right end, add margin-right so ribbon not overlap on it
.ribbon {
    &:not(.o_invisible_modifier) {
        ~ .oe_title, ~ .o_field_widget {
            margin-right: 100px;
        }
    }
}
