# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web
# 
# Translators:
# <AUTHOR> <EMAIL>, 2022
# <AUTHOR> <EMAIL>, 2022
# bf2549c5415a9287249cba2b8a5823c7, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON> <<EMAIL>>, 2022
# digitalliuzg8888, 2022
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> CHEN <<EMAIL>>, 2023
# Wil Odoo, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-25 10:43+0000\n"
"PO-Revision-Date: 2022-09-22 05:55+0000\n"
"Last-Translator: 何彬 <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid " records"
msgstr " 记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "# Code editor"
msgstr "# 代码编辑器"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d days ago"
msgstr "%d 天前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d hours ago"
msgstr "%d 小时前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d minutes ago"
msgstr "%d 分钟前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d months ago"
msgstr "%d 月前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "%d years ago"
msgstr "%d 年前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "%s Files"
msgstr "%s 文件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "%s days ago"
msgstr "%s 天前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "%s records"
msgstr "%s记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct date"
msgstr "'%s' 不是正确的日期"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/dates.js:0
#, python-format
msgid "'%s' is not a correct date or datetime"
msgstr "'%s'不是正确的日期、日期时间或时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not a correct date, datetime nor time"
msgstr "'%s'不是正确的日期、日期时间或时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct datetime"
msgstr "'%s' 不是正确的日期时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct float"
msgstr "'%s' 不是正确的浮点数"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct integer"
msgstr "'%s' 不是正确的整数"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "'%s' is not a correct monetary field"
msgstr "‘%s’ 不是正确的货币字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/time.js:0
#, python-format
msgid "'%s' is not convertible to date, datetime nor time"
msgstr "'%s' 无法转换为日期、日期时间或时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid "'%s' is unsynchronized with '%s'."
msgstr "'%s'与'%s'不同步。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "(%s/%sMB)"
msgstr "(%s/%sMB)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "(Community Edition)"
msgstr "(社区版)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(change)"
msgstr "(更改)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(count)"
msgstr "(计数)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "(create)"
msgstr "(创建)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#, python-format
msgid "(current)"
msgstr "（ 当前 ）"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "(no result)"
msgstr "(无结果)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "(no string)"
msgstr "(无字符串)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "(nolabel)"
msgstr "(无标签)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"
msgstr ""
") format(\"woff\");\n"
"                        font-weight: normal;\n"
"                        font-style: normal;\n"
"                    }"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "+ KEY"
msgstr "+ KEY"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "07/08/2020"
msgstr "07/08/2020"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "08/07/2020"
msgstr "08/07/2020"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "1 record"
msgstr "1记录"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">$ 2,887.50</span>"
msgstr "<span class=\"text-nowrap\">$ 2,887.50</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$<span class=\"oe_currency_value\">\n"
"                                                       22,137.50</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">11,750.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"
msgstr ""
"<span class=\"text-nowrap\">$ <span "
"class=\"oe_currency_value\">7,500.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">1,500.00</span>"
msgstr "<span class=\"text-nowrap\">1,500.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">2,350.00</span>"
msgstr "<span class=\"text-nowrap\">2,350.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span class=\"text-nowrap\">Tax 15%</span>"
msgstr "<span class=\"text-nowrap\">税 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                       Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"
msgstr ""
"<span class=\"w-100 o_force_ltr\" itemprop=\"streetAddress\">77 Santa Barbara\n"
"                                       Rd<br/>Pleasant Hill CA 94523<br/>United States</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span id=\"line_tax_ids\">Tax 15%</span>"
msgstr "<span id=\"line_tax_ids\">税 15%</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span itemprop=\"name\">Deco Addict</span>"
msgstr "<span itemprop=\"name\">Deco Addict</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"
msgstr "<span>$ <span class=\"oe_currency_value\">19,250.00</span></span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>5.00</span>"
msgstr "<span>5.00</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Amount</span>"
msgstr "<span>金额</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Description</span>"
msgstr "<span>描述</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>Invoice</span>\n"
"                           <span>INV/2023/00003</span>"
msgstr ""
"<span>发票</span>\n"
"                           <span>INV/2023/00003</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Payment terms: 30 Days</span>"
msgstr "<span>付款条件：30 天</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Quantity</span>"
msgstr "<span>数量</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Taxes</span>"
msgstr "<span>税金</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<span>Unit Price</span>"
msgstr "<span>单价</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8220] Four Person Desk<br/>\n"
"                                       Four person modern office workstation</span>"
msgstr ""
"<span>[FURN_8220] 四人办公桌<br/>\n"
"                                       四个工位时尚办公桌</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"<span>[FURN_8999] Three-Seat Sofa<br/>\n"
"                                       Three Seater Sofa with Lounger in Steel Grey Colour</span>"
msgstr ""
"<span>[FURN_8999] 三座沙发<br/>\n"
"                                       灰色靠背含钢材料沙发</span>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Due Date:</strong>"
msgstr "<strong>到期日期:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Invoice Date:</strong>"
msgstr "<strong>结算日期:</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Total</strong>"
msgstr "<strong>总计</strong>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid "<strong>Untaxed Amount</strong>"
msgstr "<strong>未税金额</strong>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=ilike"
msgstr "=ilike"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "=like"
msgstr "=like"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"
msgstr ""
"@font-face {\n"
"                        font-family: \"font\";\n"
"                        src: url(data:font/ttf;base64,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A filter with same name already exists."
msgstr "相同名称的筛选已经存在。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.js:0
#, python-format
msgid "A name for your favorite filter is required."
msgstr "必须为您收藏的筛选输入一个名称。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"A popup window has been blocked. You may need to change your browser "
"settings to allow popup windows for this page."
msgstr "Odoo的弹出窗口已被浏览器阻止弹出。您需要更改浏览器的设置选项以便允许本页的弹出窗口。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ALL"
msgstr "ALL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "ANY"
msgstr "ANY"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Denied"
msgstr "访问被拒绝"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Access Error"
msgstr "访问错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Access to all Enterprise Apps"
msgstr "访问全部企业版应用"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/ace/ace_field.js:0
#, python-format
msgid "Ace Editor"
msgstr "王牌编辑"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/views/form/status_bar_buttons/status_bar_buttons.xml:0
#, python-format
msgid "Action"
msgstr "动作"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Action ID:"
msgstr "动作ID:"

#. module: web
#: model:ir.model,name:web.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "动作窗口视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Assets Debugging"
msgstr "激活资产调试"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Activate Tests Assets Debugging"
msgstr "激活测试资产调试"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Activate debug mode (with assets)"
msgstr "激活调试模式(使用资产)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode"
msgstr "激活开发者模式"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with assets)"
msgstr "激活开发者模式 (使用资源)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Activate the developer mode (with tests assets)"
msgstr "激活开发者模式（使用资源测试）"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Add"
msgstr "添加"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add Custom Filter"
msgstr "添加自定义筛选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#, python-format
msgid "Add Custom Group"
msgstr "添加自定义分组"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "Add a Property"
msgstr "添加属性"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Add a Value"
msgstr "添加值"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#, python-format
msgid "Add a condition"
msgstr "添加条件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Add a line"
msgstr "添加明细行"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add branch"
msgstr "添加分支"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Add column"
msgstr "添加列"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add filter"
msgstr "添加筛选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add new value"
msgstr "添加新值"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add node"
msgstr "添加节点"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Add qweb directive context"
msgstr "添加qweb指令上下文"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Add tag"
msgstr "添加标签"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Add to Favorites"
msgstr "添加到收藏"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Add: "
msgstr "添加： "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Add: %s"
msgstr "添加：%s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Additionnal actions"
msgstr "额外动作"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt & Sign"
msgstr "采用和签署"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#, python-format
msgid "Adopt Your Signature"
msgstr "采纳您的签字"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "Alert"
msgstr "警报"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/search_panel_model_extension.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_arch_parser.js:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "All"
msgstr "全部"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#, python-format
msgid "All day"
msgstr "全天"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "All users"
msgstr "所有用户"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Among the"
msgstr "当"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#, python-format
msgid "An error occured while uploading."
msgstr "上传中发生错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "An error occurred"
msgstr "发生错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "And more"
msgstr "以及更多"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Any"
msgstr "任意"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_header
msgid ""
"Appears by default on the top right corner of your printed documents (report"
" header)."
msgstr "将出现在打印的报告的右上角（报告页眉）"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/group_by_menu/custom_group_by_item.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#, python-format
msgid "Apply"
msgstr "应用"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Archive"
msgstr "存档"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Archive All"
msgstr "全部归档"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid ""
"Are you sure that you want to archive all the records from this column?"
msgstr "您确定要归档此列中的所有记录吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure that you want to archive all the selected records?"
msgstr "您确定要归档所有选定的记录吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Are you sure that you want to archive this record?"
msgstr "您确定要存档此记录吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Are you sure that you want to remove this column ?"
msgstr "您确定要移除此列吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid "Are you sure that you want to remove this filter?"
msgstr "您确定要移除此筛选吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records ?"
msgstr "您确认要删除这些数据吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete these records?"
msgstr "您确认要删除这些数据吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Are you sure you want to delete this column?"
msgstr "您确定要删除这一列吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid ""
"Are you sure you want to delete this property field? It will be removed for "
"everyone using the \"%s\" %s."
msgstr "您确定要删除这个属性字段吗？它将从每个使用\"%s\"%s的人那里删除"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record ?"
msgstr "您确定要删除此记录吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Are you sure you want to delete this record?"
msgstr "您确认要删除这些数据吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Are you sure you want to perform the following update on those"
msgstr "您确定要执行以下更新"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "As a default text when no value are set"
msgstr "当没有设置值时，作为默认文本"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Ascending"
msgstr "升序"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Attach"
msgstr "附加"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Attachment"
msgstr "附件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.js:0
#, python-format
msgid "Attachment Image"
msgstr "附件图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Auto"
msgstr "自动"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Available fields"
msgstr "可用字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Avatar"
msgstr "形象"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background_image
msgid "Background Image"
msgstr "背景图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/badge/badge_field.js:0
#, python-format
msgid "Badge"
msgstr "徽标"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/badge_selection/badge_selection_field.js:0
#, python-format
msgid "Badges"
msgstr "徽标"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Bar Chart"
msgstr "柱状图"

#. module: web
#: model:ir.model,name:web.model_base
msgid "Base"
msgstr "基础"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Become Superuser"
msgstr "成为超级用户"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"Binary fields can not be exported to Excel unless their content is "
"base64-encoded. That does not seem to be the case for %s."
msgstr "二进制字段不能导出到Excel，除非它们的内容是Base64编码。这似乎不适用于 %s。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "Binary file"
msgstr "二进制文件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Bugfixes guarantee"
msgstr "补丁修复保证"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button"
msgstr "按钮"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Button Type:"
msgstr "按钮类型："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid ""
"By clicking Adopt & Sign, I agree that the chosen signature/initials will be"
" a valid electronic representation of my hand-written signature/initials for"
" all purposes when it is used on documents, including legally binding "
"contracts."
msgstr ""
"通过单击采用和签字，我同意所选的签字/姓名首字母将是我的手写签字/姓名首字母的有效电子表示，用于文件（包括具有法律约束力的合同）的所有目的。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/formatters.js:0
#, python-format
msgid "Bytes"
msgstr "字节"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Bytes|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"
msgstr "字节|Kb|Mb|Gb|Tb|Pb|Eb|Zb|Yb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "CLEAR"
msgstr "清空"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CMD"
msgstr "CMD"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "CTRL"
msgstr "CTRL"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/phone/phone_field.xml:0
#, python-format
msgid "Call"
msgstr "电话"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/core/signature/signature_dialog.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/signature_dialog.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/daterange/daterange_field.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.xml:0
#, python-format
msgid "Cancel Upload"
msgstr "取消上传"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Card color: %s"
msgstr "卡片颜色： %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Change default:"
msgstr "更改预设："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Change graph"
msgstr "更改图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields_owl.js:0
#: code:addons/web/static/src/views/fields/boolean/boolean_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Checkbox"
msgstr "复选框"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_checkboxes/many2many_checkboxes_field.js:0
#, python-format
msgid "Checkboxes"
msgstr "复选框"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Choose"
msgstr "选择"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_input/file_input.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Choose File"
msgstr "选择文件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Choose a debug command..."
msgstr "选择一个调试命令..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Clear"
msgstr "清除"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Clear Signature"
msgstr "清除签名"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/notifications/notification.xml:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/dialog.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Close"
msgstr "关闭"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Close menu"
msgstr "关闭菜单"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Colors"
msgstr "颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Column %s"
msgstr "列 %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/mobile_switch_company_menu/mobile_switch_company_menu.xml:0
#: model:ir.model,name:web.model_res_company
#, python-format
msgid "Companies"
msgstr "公司"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_id
msgid "Company"
msgstr "公司"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__company_details
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Company Details"
msgstr "公司详情"

#. module: web
#: model:ir.model,name:web.model_base_document_layout
msgid "Company Document Layout"
msgstr "公司文档布局"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo
msgid "Company Logo"
msgstr "公司 Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__name
msgid "Company Name"
msgstr "公司名称"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_header
msgid "Company Tagline"
msgstr "公司标语"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Company name"
msgstr "公司名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/comparison_menu/comparison_menu.xml:0
#, python-format
msgid "Comparison"
msgstr "比较"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Condition:"
msgstr "条件:"

#. module: web
#: model:ir.actions.act_window,name:web.action_base_document_layout_configurator
msgid "Configure your document layout"
msgstr "配置您的文档布局"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "确认"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection lost. Trying to reconnect..."
msgstr "连接断开，尝试重新连接..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_handlers.js:0
#, python-format
msgid "Connection restored. You are back online."
msgstr "连接已恢复，您已回到在线状态。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Context:"
msgstr "上下文:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "控制面板按钮"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copied"
msgstr "已复制"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Copied !"
msgstr "已复制 !"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy"
msgstr "复制"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Multiline Text to Clipboard"
msgstr "复制多行文字到粘贴板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy Text to Clipboard"
msgstr "复制文字到粘贴板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy URL to Clipboard"
msgstr "复制URL到粘贴板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "Copy the full error to clipboard"
msgstr "复制完整错误到剪贴板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/copy_clipboard/copy_clipboard_field.js:0
#, python-format
msgid "Copy to Clipboard"
msgstr "复制到剪贴板"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
msgid "Copyright &amp;copy;"
msgstr "Copyright &amp;copy;"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Copyright © 2004"
msgstr "版权所有 © 2004"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid "Could not connect to the server"
msgstr "未能连接至服务器"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.js:0
#, python-format
msgid "Could not display the selected image"
msgstr "无法显示所选图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "Could not display the selected pdf"
msgstr "无法显示选定的pdf"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#, python-format
msgid "Could not display the specified image url."
msgstr "无法显示指定的图像网址。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "Could not serialize XML"
msgstr "不能序列化 XML"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid ""
"Could not set the cover image: incorrect field (\"%s\") is provided in the "
"view."
msgstr "无法设置封面图像：视图中提供了错误的字段（“%s”）。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Could not start scanning. "
msgstr "无法开始扫描. "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/utils.js:0
#, python-format
msgid "Count"
msgstr "计数"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__country_id
msgid "Country"
msgstr "国家"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Create"
msgstr "创建"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Create "
msgstr "创建 "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create \"%s\""
msgstr "创建\"%s\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create \"<strong>%s</strong>\""
msgstr "创建  \"<strong>%s</strong>\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create %s"
msgstr "创建 %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Create <strong>%s</strong> as a new %s?"
msgstr "将<strong>%s</strong>创建为新的%s？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create and Edit..."
msgstr "创建并编辑..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Create and edit..."
msgstr "创建并编辑..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/fields/x2many/x2many_field.xml:0
#, python-format
msgid "Create record"
msgstr "创建记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Create: %s"
msgstr "创建：%s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_uid
msgid "Created by"
msgstr "创建人"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__create_date
msgid "Created on"
msgstr "创建时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation Date:"
msgstr "创建日期："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Creation User:"
msgstr "创建用户："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Cumulative"
msgstr "累积"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#, python-format
msgid "Current state"
msgstr "当前状态"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__custom_colors
msgid "Custom Colors"
msgstr "自定义颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark blue"
msgstr "深蓝色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dark purple"
msgstr "深紫色"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Database"
msgstr "数据库"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/date/date_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date"
msgstr "日期"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/datetime/datetime_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Date & Time"
msgstr "日期和时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Day"
msgstr "天"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#, python-format
msgid "Deactivate debug mode"
msgstr "退出调试模式"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Deactivate the developer mode"
msgstr "取消开发者模式"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "Debug tools..."
msgstr "调试工具..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Decimal"
msgstr "小数"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Default"
msgstr "默认"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default State"
msgstr "默认状态"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Default Value"
msgstr "默认值"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default text is used when no values are set"
msgstr "当没有设置值时使用默认文本"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Default value"
msgstr "默认值"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Default:"
msgstr "默认："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/many2many_tags/tags_list.xml:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Delete"
msgstr "删除"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Delete Property Field"
msgstr "删除属性字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Delete item"
msgstr "删除项目"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Delete node"
msgstr "删除节点"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.xml:0
#, python-format
msgid "Delete row"
msgstr "删除行"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Delete row "
msgstr "删除行 "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Descending"
msgstr "降序"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Developer Tools"
msgstr "开发者工具"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Different currencies cannot be aggregated"
msgstr "不同货币无法汇总"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Discard changes"
msgstr "丢弃变更"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Discard record"
msgstr "丢弃记录"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_bar.js:0
#, python-format
msgid "Do you really want to cancel the upload of %s?"
msgstr "您真的要取消上传 %s 吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Do you really want to delete this export template?"
msgstr "您真的要删除此导出模板？"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__external_report_layout_id
msgid "Document Template"
msgstr "单据模板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Documentation"
msgstr "文档"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Domain"
msgstr "域"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_control_panel.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Domain node"
msgstr "域节点"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not properly formed"
msgstr "域格式不正确"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Domain not supported"
msgstr "域不支持"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Domain:"
msgstr "域:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Don't leave yet,"
msgstr "先别走，"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Don't leave yet,<br />it's still loading..."
msgstr "请不要离开，<br />它仍在加载..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Download"
msgstr "下载"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Download PDF Preview"
msgstr "下载PDF预览"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Download xlsx"
msgstr "下载 xlsx"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Draw"
msgstr "画"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#, python-format
msgid "Duplicate"
msgstr "复制"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.xml:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: code:addons/web/static/src/views/kanban/kanban_record_quick_create.xml:0
#, python-format
msgid "Edit"
msgstr "编辑"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Edit Action"
msgstr "编辑动作"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#, python-format
msgid "Edit Column"
msgstr "编辑列"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Edit Domain"
msgstr "编辑域"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit SearchView"
msgstr "编辑搜索视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Edit Stage"
msgstr "编辑阶段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Edit View: "
msgstr "编辑视图:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Edit record"
msgstr "编辑记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#, python-format
msgid "Edit: %s"
msgstr "修改: %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/email/email_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__email
#: model_terms:ir.ui.view,arch_db:web.login
#, python-format
msgid "Email"
msgstr "Email"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Empty email address"
msgstr "空的电子邮件地址"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Enable profiling"
msgstr "启用性能分析"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Enter e-mail address"
msgstr "输入电子邮件地址"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/highlight_text/form_label_highlight_text.xml:0
#, python-format
msgid "Enterprise"
msgstr "企业"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_service.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Error"
msgstr "错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "Esc to discard"
msgstr "按退出键 丢弃"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everybody's calendars"
msgstr "每一个人的日历"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#, python-format
msgid "Everything"
msgstr "一切"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Expand all"
msgstr "展开全部"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export"
msgstr "导出"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Export All"
msgstr "导出全部"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Export Data"
msgstr "导出数据"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Export Format:"
msgstr "导出格式："

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid "Exporting grouped data to csv is not supported."
msgstr "不支持将分组数据导出到csv。"

#. module: web
#. odoo-python
#. odoo-javascript
#: code:addons/web/controllers/export.py:0
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "External ID"
msgstr "外部ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "External link"
msgstr "外部链接"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "FILTER"
msgstr "筛选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/control_panel_model_extension.js:0
#, python-format
msgid "Failed to evaluate search context"
msgstr "无法评估搜索上下文"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "False"
msgstr "否"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/boolean_favorite/boolean_favorite_field.js:0
#, python-format
msgid "Favorite"
msgstr "收藏夹"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.xml:0
#, python-format
msgid "Favorites"
msgstr "收藏"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Field Type"
msgstr "字段类型"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Field:"
msgstr "字段："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Fields to export"
msgstr "要导出的字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/binary/binary_field.js:0
#, python-format
msgid "File"
msgstr "文件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "File upload"
msgstr "文件上传"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/custom_favorite_item.js:0
#, python-format
msgid "Filter with same name already exists."
msgstr "以相同名字的筛选已经存在。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/filter_menu/filter_menu.xml:0
#, python-format
msgid "Filters"
msgstr "筛选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Flip axis"
msgstr "翻转轴"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/float/float_field.js:0
#, python-format
msgid "Float"
msgstr "浮动"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Fold"
msgstr "收拢"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Followed by"
msgstr "关注者"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#, python-format
msgid "Followed-by"
msgstr "关注者"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__font
msgid "Font"
msgstr "字体"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/font_selection/font_selection_field.js:0
#, python-format
msgid "Font Selection"
msgstr "字体选择"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Footer"
msgstr "页脚"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "显示在所有报告下方的页脚文字。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_controller.js:0
#, python-format
msgid ""
"For Excel compatibility, data cannot be exported if there are more than 16384 columns.\n"
"\n"
"Tip: try to flip axis, filter further or reduce the number of measures."
msgstr ""
"为了与Excel兼容，如果有超过16384列的数据，则无法导出。\n"
"\n"
"提示：尝试翻转轴，进一步过滤或减少措施的数量。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_view.js:0
#, python-format
msgid "Form"
msgstr "表单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/qweb/qweb_view.js:0
#, python-format
msgid "Freedom View"
msgstr "自由查看"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Fri"
msgstr "周五"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#, python-format
msgid "Fuchsia"
msgstr "紫红色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Full Name"
msgstr "完整名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Fushia"
msgstr "紫紅色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "GNU LGPL Licensed"
msgstr "GNU LGPL授权"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Get View"
msgstr "获取视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Get this feature and much more with Odoo Enterprise!"
msgstr "通过Odoo企业版获得该功能及其他更多！"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/url/url_field.xml:0
#, python-format
msgid "Go to URL"
msgstr "转到链接"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#, python-format
msgid "Graph"
msgstr "图形"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Green"
msgstr "绿色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/group_by_menu/group_by_menu.xml:0
#, python-format
msgid "Group By"
msgstr "分组"

#. module: web
#: model:ir.model,name:web.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/handle/handle_field.js:0
#, python-format
msgid "Handle"
msgstr "把手"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "标题文本显示在所有报告的顶部。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#, python-format
msgid ""
"Heads up! Your recent changes are too large to save automatically. Please "
"click the <i class=\"fa fa-cloud-upload fa-fw\"></i> button now to ensure "
"your work is saved before you exit this tab."
msgstr ""
"注意！您最近所做的更改太大，无法自动保存。请立即点击 <i class=\"fa fa-cloud-upload fa-"
"fw\"></i>按钮，确保在退出此选项卡前保存您的工作。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hide in Kanban"
msgstr "在看板中隐藏"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.xml:0
#, python-format
msgid "Hide in kanban"
msgstr "在看板中隐藏"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit DOWN to navigate to the list below"
msgstr "点击方向键下，导航到列表的下面"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to"
msgstr "点击 ENTER "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to CREATE"
msgstr "点击 ENTER 创建"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ENTER to SAVE"
msgstr "点击 ENTER 保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Hit ESCAPE to DISCARD"
msgstr "按退出键 丢弃"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#, python-format
msgid "I am sure about this."
msgstr "我确信这一点。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "I want to update data (import-compatible export)"
msgstr "我想更新数据（导入兼容的导出）"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__id
msgid "ID"
msgstr "ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "ID:"
msgstr "ID："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#, python-format
msgid ""
"If you change %s or %s, the synchronization will be reapplied and the data "
"will be modified."
msgstr "如果您改变%s或%s，同步将被重新应用，数据将被修改。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/attachment_image/attachment_image_field.xml:0
#: code:addons/web/static/src/views/fields/image/image_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.js:0
#: code:addons/web/static/src/views/fields/image_url/image_url_field.xml:0
#, python-format
msgid "Image"
msgstr "图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "In %s days"
msgstr "在 %s 天内"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/integer/integer_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Integer"
msgstr "整数"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "Internal link"
msgstr "内部链接"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Interval"
msgstr "区间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Invalid data"
msgstr "无效的数据"

#. module: web
#. odoo-python
#: code:addons/web/controllers/database.py:0
#: code:addons/web/controllers/database.py:0
#, python-format
msgid ""
"Invalid database name. Only alphanumerical characters, underscore, hyphen "
"and dot are allowed."
msgstr "数据库名称无效。只允许字母数字字符、下划线、连字符和点。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Invalid domain"
msgstr "无效的域"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Invalid field chain"
msgstr "非法的字段链"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid ""
"Invalid field chain. You may have used a non-existing field name or followed"
" a non-relational field."
msgstr "无效的字段链。您可能使用了一个不存在的字段名，或跟随一个非关系字段。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Invalid fields"
msgstr "无效的字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Invalid fields:"
msgstr "无效的字段:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/basic_relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "Invalid fields: "
msgstr "无效的字段: "

#. module: web
#. odoo-python
#: code:addons/web/controllers/domain.py:0
#, python-format
msgid "Invalid model: %s"
msgstr "无效模型：%s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Invite"
msgstr "邀请"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Invite New Users"
msgstr "邀请新用户"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.js:0
#, python-format
msgid "Inviting..."
msgstr "邀请..."

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__is_company_details_empty
msgid "Is Company Details Empty"
msgstr "公司详情是否为空"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid ""
"It is possible that the \"t-call\" time does not correspond to the overall time of the\n"
"            template. Because the global time (in the drop down) does not take into account the\n"
"            duration which is not in the rendering (look for the template, read, inheritance,\n"
"            compilation...). During rendering, the global time also takes part of the time to make\n"
"            the profile as well as some part not logged in the function generated by the qweb."
msgstr ""
"有可能 \"t-call \"时间与模板的整体时间不一致。\n"
"            模板的整体时间。因为全局时间（在下拉菜单中）并没有考虑到\n"
"            不在渲染中的时间（寻找模板，读取，继承。\n"
"            编译...）。在渲染过程中，全局时间也考虑到了制作配置文件的部分时间\n"
"            以及一些没有被记录在qweb生成的函数中的部分时间。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_view.js:0
#, python-format
msgid "Kanban"
msgstr "看板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_examples_dialog.js:0
#, python-format
msgid "Kanban Examples"
msgstr "看板例子"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_record.js:0
#, python-format
msgid "Kanban: no action for type: "
msgstr "看板: 类型无动作: "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/label_selection/label_selection_field.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Label Selection"
msgstr "标签选择"

#. module: web
#. odoo-python
#: code:addons/web/controllers/session.py:0
#, python-format
msgid "Languages"
msgstr "语言"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout____last_update
msgid "Last Modified on"
msgstr "最后修改时间"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification Date:"
msgstr "最后修改日期："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Latest Modification by:"
msgstr "最后修改者："

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Layout"
msgstr "布局"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__layout_background
msgid "Layout Background"
msgstr "前景色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Leave the Developer Tools"
msgstr "退出开发者工具"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Light blue"
msgstr "浅蓝"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Line Chart"
msgstr "线状图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_view.js:0
#, python-format
msgid "List"
msgstr "列表"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Load"
msgstr "加载"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_dev_tool.xml:0
#, python-format
msgid "Load demo data"
msgstr "装入样例数据"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Load more... ("
msgstr "加载更多...("

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/webclient/loading_indicator/loading_indicator.xml:0
#, python-format
msgid "Loading"
msgstr "正在加载"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Loading, please wait..."
msgstr "加载中，请稍候..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Loading..."
msgstr "加载中..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in"
msgstr "登录"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Log in as superuser"
msgstr "以超级用户身份登录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#: model_terms:ir.ui.view,arch_db:web.login_successful
#, python-format
msgid "Log out"
msgstr "登出"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_bold
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
#: model_terms:ir.ui.view,arch_db:web.frontend_layout
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Logo"
msgstr "Logo"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_primary_color
msgid "Logo Primary Color"
msgstr "Logo主要颜色"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__logo_secondary_color
msgid "Logo Secondary Color"
msgstr "Logo次要颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "MailDeliveryException"
msgstr "邮件收发异常"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Main actions"
msgstr "主要动作"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Manage Attachments"
msgstr "管理附件"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Manage Databases"
msgstr "管理数据库"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Manage Filters"
msgstr "管理筛选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one_barcode/many2one_barcode_field.js:0
#, python-format
msgid "Many2OneBarcode"
msgstr "Many2OneBarcode"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2many"
msgstr "多对多"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Many2one"
msgstr "多对一"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match"
msgstr "匹配"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with"
msgstr "匹配记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Match records with the following rule:"
msgstr "根据以下规则匹配记录："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Maybe you should consider reloading the application by pressing F5..."
msgstr "若长时间未响应，请按下F5重新加载应用..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view.xml:0
#, python-format
msgid "Measures"
msgstr "测量"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Medium blue"
msgstr "中蓝色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "Meeting Subject"
msgstr "会议主题"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.xml:0
#, python-format
msgid "Meeting Subject:"
msgstr "会议主题:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: model:ir.model,name:web.model_ir_ui_menu
#, python-format
msgid "Menu"
msgstr "菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Method:"
msgstr "方法："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Missing Record"
msgstr "缺失记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Mobile support"
msgstr "支持移动设备"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Model"
msgstr "模型"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "Model Record Rules"
msgstr "模型记录规则"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Model:"
msgstr "模型:"

#. module: web
#: model:ir.model,name:web.model_ir_model
msgid "Models"
msgstr "模型"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Modifiers:"
msgstr "装饰器："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Mon"
msgstr "周一"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/monetary/monetary_field.js:0
#, python-format
msgid "Monetary"
msgstr "货币"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Month"
msgstr "月"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#: code:addons/web/static/src/views/form/button_box/button_box.xml:0
#, python-format
msgid "More"
msgstr "更多"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Move to %s..."
msgstr "移动到%s……"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/text/text_field.js:0
#, python-format
msgid "Multiline Text"
msgstr "多行文字"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "My Odoo.com account"
msgstr "Odoo.com账户"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "NONE"
msgstr "无"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_model.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/kanban/kanban_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "New"
msgstr "新建"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "New %s"
msgstr "新建 %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#: code:addons/web/static/src/views/calendar/quick_create/calendar_quick_create.js:0
#, python-format
msgid "New Event"
msgstr "新活动"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.xml:0
#, python-format
msgid "New Property"
msgstr "新属性"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "New design"
msgstr "新设计"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "New template"
msgstr "新模板"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "New:"
msgstr "新建："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "New: %s"
msgstr "新建：%s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Next"
msgstr "下一页"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Next page"
msgstr "下一页"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "No"
msgstr "否"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_value.js:0
#: code:addons/web/static/src/views/fields/properties/property_value.xml:0
#, python-format
msgid "No Access"
msgstr "禁止访问"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "No Update:"
msgstr "不可更新:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/client_actions.js:0
#, python-format
msgid "No action with id '%s' could be found"
msgstr "没有找到id为'%s'的动作"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "No color"
msgstr "无颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "No command found"
msgstr "未找到命令"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_renderer.js:0
#, python-format
msgid "No data"
msgstr "无数据"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid "No data to display"
msgstr "无数据显示"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.js:0
#, python-format
msgid "No debug command found"
msgstr "未找到调试命令"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "No device can be found."
msgstr "找不到任何设备。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "No match found."
msgstr "没有找到匹配。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "No menu found"
msgstr "没有找到菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/formatters.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "No records"
msgstr "没有记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "No records found!"
msgstr "没有找到记录！"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "No result"
msgstr "没有结果"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#, python-format
msgid "No result found"
msgstr "找不到结果"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#: code:addons/web/static/src/views/relational_model.js:0
#, python-format
msgid "No valid record to save"
msgstr "无有效记录保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "No view of type '%s' could be found in the current action."
msgstr "在当前的动作中找不到类型为'%s‘的视图。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "None"
msgstr "无"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0 code:addons/web/models/models.py:0
#: code:addons/web/models/models.py:0
#, python-format
msgid "Not Set"
msgstr "未设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#, python-format
msgid "Not active state"
msgstr "非启用的状态"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.xml:0
#, python-format
msgid "Not active state, click to change it"
msgstr "处於非启用状态，单击以更改它"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Object:"
msgstr "对象:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
#, python-format
msgid "Odoo"
msgstr "Odoo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Odoo Apps will be available soon"
msgstr "Odoo Apps 将马上可用"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Client Error"
msgstr "Odoo客户端错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Error"
msgstr "Odoo错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Network Error"
msgstr "Odoo网络错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_edition.xml:0
#, python-format
msgid "Odoo S.A."
msgstr "Odoo S.A."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Server Error"
msgstr "Odoo服务器错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Odoo Session Expired"
msgstr "Odoo会话过期"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Odoo Warning"
msgstr "Odoo警告"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/barcode/barcode_scanner.js:0
#, python-format
msgid "Odoo needs your authorization first."
msgstr "Odoo首先需要您的授权。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid ""
"Of the %d records selected, only the first %d have been archived/unarchived."
msgstr "在所选的%d条记录中，只有前%d条已被归档/未归档。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Oh snap!"
msgstr "哦，快!"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/confirmation_dialog/confirmation_dialog.js:0
#: code:addons/web/static/src/core/dialog/dialog.xml:0
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/core/dialog.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_confirm_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#: code:addons/web/static/src/views/calendar/calendar_year/calendar_year_popover.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/webclient/actions/action_dialog.xml:0
#, python-format
msgid "Ok"
msgstr "确定"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "On change:"
msgstr "变更时："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "One2many"
msgstr "一对多"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid ""
"Only employees can access this database. Please contact the administrator."
msgstr "只有员工可以访问此数据库，请与管理员联系。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#, python-format
msgid "Only the first %d records have been deleted (out of %d selected)"
msgstr "只有前%d条记录被删除（在所选的%d条记录中）。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Only the first %s records have been deleted (out of %s selected)"
msgstr "只有前%s条记录被删除(在所选的%s条中)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for category (found type "
"%(field_type)s)"
msgstr "只有类别 %(supported_types)s 支持于类别 (找到类别 %(field_type)s)"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid ""
"Only types %(supported_types)s are supported for filter (found type "
"%(field_type)s)"
msgstr "只有类型 %(supported_types)s 支持于筛选 (找到类型 %(field_type)s)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Only you"
msgstr "仅限您自己"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Open View"
msgstr "打开视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu.xml:0
#, python-format
msgid "Open developer tools"
msgstr "开启开发者工具"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open:"
msgstr "打开："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#, python-format
msgid "Open: "
msgstr "打开： "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Open: %s"
msgstr "打开：%s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Option Name"
msgstr "选项名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#, python-format
msgid "Optional columns"
msgstr "可选列"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Orange"
msgstr "橘色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_progressbar.js:0
#: code:addons/web/static/src/views/kanban/kanban_model.js:0
#, python-format
msgid "Other"
msgstr "其他"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.js:0
#, python-format
msgid "PDF Viewer"
msgstr "PDF 查看器"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "PDF controls"
msgstr "PDF 控制"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#, python-format
msgid "PDF file"
msgstr "PDF文件"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_striped
msgid ""
"Page:\n"
"                    <span class=\"page\"/>\n"
"                    of\n"
"                    <span class=\"topage\"/>"
msgstr ""
"页:\n"
"                    <span class=\"page\"/>\n"
"                    /\n"
"                    <span class=\"topage\"/>"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.external_layout_boxed
#: model_terms:ir.ui.view,arch_db:web.external_layout_standard
msgid "Page: <span class=\"page\"/> / <span class=\"topage\"/>"
msgstr "页: <span class=\"page\"/> / <span class=\"topage\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Pager"
msgstr "分页"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__paperformat_id
msgid "Paper format"
msgstr "纸张格式"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__partner_id
msgid "Partner"
msgstr "业务伙伴"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid "Password"
msgstr "密码"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "Pending Invitations:"
msgstr "待定邀请："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/percent_pie/percent_pie_field.js:0
#, python-format
msgid "PercentPie"
msgstr "百分比派"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/percentage/percentage_field.js:0
#, python-format
msgid "Percentage"
msgstr "百分比"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Percentage Pie"
msgstr "百分比饼"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/phone/phone_field.js:0
#: model:ir.model.fields,field_description:web.field_base_document_layout__phone
#, python-format
msgid "Phone"
msgstr "电话"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/colorpicker.js:0
#, python-format
msgid "Pick a color"
msgstr "选一种颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Pie Chart"
msgstr "饼图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid ""
"Pie chart cannot mix positive and negative numbers. Try to change your "
"domain to only display positive results"
msgstr "饼图不能混用正数和负数。尝试改变您的域只显示正数结果"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Pivot"
msgstr "透视表"

#. module: web
#. odoo-python
#: code:addons/web/controllers/pivot.py:0
#, python-format
msgid "Pivot %(title)s (%(model_name)s)"
msgstr "数据透视%(title)s（%(model_name)s）。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/pivot/pivot_controller.xml:0
#, python-format
msgid "Pivot settings"
msgstr "数据透视表设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Please be patient."
msgstr "请耐心等待。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#, python-format
msgid "Please click on the \"save\" button first"
msgstr "请先点击“保存”按钮"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Please complete your properties before adding a new one"
msgstr "在添加新的属性之前，请完成您的属性"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Please enter a numerical value"
msgstr "请输入一个数字"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please enter save field list name"
msgstr "请输入字段列表的名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/attach_document.js:0
#, python-format
msgid "Please save before attaching a file"
msgstr "附加文件前请先保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#, python-format
msgid "Please select fields to export..."
msgstr "请选择要导出的字段..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Please select fields to save export list..."
msgstr "请选择要保存成导出列表的字段..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Please update translations of :"
msgstr "请更新以下翻译："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid ""
"Please use the copy button to report the error to your support service."
msgstr "请使用复制按钮将错误报告给您的支持服务。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.report_invoice_wizard_preview
msgid ""
"Please use the following communication for your payment : <b><span>\n"
"                           INV/2023/00003</span></b>"
msgstr ""
"请使用以下通讯方式付款：<b><span>\n"
"                           INV/2023/00003</span></b>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.js:0
#, python-format
msgid "Please, scan again !"
msgstr "请重新扫描！"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.brand_promotion_message
msgid "Powered by %s%s"
msgstr "由%s%s提供支持"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_layout
msgid "Powered by <span>Odoo</span>"
msgstr "由<span>Odoo</span>提供支持"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Preferences"
msgstr "偏好"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview
msgid "Preview"
msgstr "预览"

#. module: web
#: model:ir.actions.report,name:web.action_report_externalpreview
msgid "Preview External Report"
msgstr "预览外部报告"

#. module: web
#: model:ir.actions.report,name:web.action_report_internalpreview
msgid "Preview Internal Report"
msgstr "预览内部报告"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__preview_logo
msgid "Preview logo"
msgstr "预览Logo"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/pager/pager.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#, python-format
msgid "Previous"
msgstr "上一页"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Period"
msgstr "前一期间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Previous Year"
msgstr "前一年"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous menu"
msgstr "上一级菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Previous page"
msgstr "上一页"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__primary_color
msgid "Primary Color"
msgstr "主要颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#: code:addons/web/static/src/search/action_menus/action_menus.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#: code:addons/web/static/src/webclient/actions/reports/report_action.xml:0
#, python-format
msgid "Print"
msgstr "打印"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/components/action_menus.js:0
#, python-format
msgid "Printing options"
msgstr "打印选项"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.xml:0
#, python-format
msgid "Priority"
msgstr "优先级"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Processing..."
msgstr "处理中..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/progress_bar/progress_bar_field.js:0
#, python-format
msgid "Progress Bar"
msgstr "进度条"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Properties"
msgstr "权益"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "Property %s"
msgstr "属性%s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Property Name"
msgstr "属性名称"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Purple"
msgstr "紫色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q1"
msgstr "Q1"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q2"
msgstr "Q2"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q3"
msgstr "Q3"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Q4"
msgstr "Q4"

#. module: web
#: model:ir.model.fields.selection,name:web.selection__ir_actions_act_window_view__view_mode__qweb
msgid "QWeb"
msgstr "QWeb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#, python-format
msgid "Quarter"
msgstr "季"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Quick add"
msgstr "快速添加"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Quick search: %s"
msgstr "快速搜索: %s"

#. module: web
#: model:ir.model,name:web.model_ir_qweb_field_image
#: model:ir.model,name:web.model_ir_qweb_field_image_url
msgid "Qweb Field Image"
msgstr "Qweb 图像字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGB"
msgstr "RGB"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "RGBA"
msgstr "RGBA"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/radio/radio_field.js:0
#, python-format
msgid "Radio"
msgstr "单选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record qweb"
msgstr "记录qweb"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record sql"
msgstr "记录 sql"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Record traces"
msgstr "记录痕迹"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_item.xml:0
#, python-format
msgid "Recording..."
msgstr "记录中……"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Red"
msgstr "红色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/reference/reference_field.js:0
#, python-format
msgid "Reference"
msgstr "参考"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Refresh"
msgstr "刷新"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.js:0
#, python-format
msgid "Regenerate Assets Bundles"
msgstr "重新生成资产包"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "Relation not allowed"
msgstr "关联不允许"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Relation to follow"
msgstr "关系如下"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Relation:"
msgstr "关系："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/x2many/x2many_field.js:0
#, python-format
msgid "Relational table"
msgstr "关系表"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Remaining Days"
msgstr "剩余天数"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#, python-format
msgid "Remove"
msgstr "移除"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Remove Cover Image"
msgstr "移除封面图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Remove Property"
msgstr "移除属性"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/data_export.js:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.js:0
#, python-format
msgid "Remove field"
msgstr "移除字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Remove from Favorites"
msgstr "从收藏移除"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_field_input_with_tags.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Remove tag"
msgstr "移除标签"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.xml:0
#, python-format
msgid "Remove this favorite from the list"
msgstr "从清单中移除这个收藏"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid "Report"
msgstr "报表"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "报告页脚"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__report_layout_id
msgid "Report Layout"
msgstr "报告布局"

#. module: web
#: model:ir.actions.report,name:web.action_report_layout_preview
msgid "Report Layout Preview"
msgstr "报告布局预览"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Request timeout"
msgstr "请求超时"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#, python-format
msgid "Reset domain"
msgstr "重置域名"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "Reset to logo colors"
msgstr "重置至徽标颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/clickbot/clickbot_loader.js:0
#, python-format
msgid "Run Click Everywhere Test"
msgstr "运行 Click Everywhere Test"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Mobile Tests"
msgstr "运行JS 移动端测试"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_providers.js:0
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Run JS Tests"
msgstr "运行JS 测试"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/search_panel/search_panel.xml:0
#, python-format
msgid "SEE RESULT"
msgstr "查看结果"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#: code:addons/web/static/src/views/fields/signature/signature_field.xml:0
#, python-format
msgid "SIGNATURE"
msgstr "签字"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Salmon pink"
msgstr "朱紅色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sat"
msgstr "周六"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector_dialog/domain_selector_dialog.xml:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector_dialog.js:0
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/fields/translation_dialog.xml:0
#: code:addons/web/static/src/views/form/form_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Save"
msgstr "保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & Close"
msgstr "保存和关闭"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/views/fields/relational_utils.xml:0
#: code:addons/web/static/src/views/view_dialogs/form_view_dialog.xml:0
#, python-format
msgid "Save & New"
msgstr "保存和新建"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save as :"
msgstr "另存为 :"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Save as:"
msgstr "另存为："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Save current search"
msgstr "保存当前搜索"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "Save default"
msgstr "保存默认"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Save manually"
msgstr "手动保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Save record"
msgstr "保存记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#: code:addons/web/static/src/views/fields/many2one/many2one_field.xml:0
#, python-format
msgid "Scan barcode"
msgstr "扫描条码"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings/settings_app.xml:0
#, python-format
msgid "Search"
msgstr "搜索"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search More..."
msgstr "搜索更多..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_selector/model_selector.js:0
#, python-format
msgid "Search a Model..."
msgstr "搜索模型"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search a field..."
msgstr "搜索字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "Search for a command..."
msgstr "搜索一个命令..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "Search for a menu..."
msgstr "搜索菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Search for records"
msgstr "搜索记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.js:0
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Search..."
msgstr "搜索..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/calendar/filter_panel/calendar_filter_panel.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Search: %s"
msgstr "搜索: %s"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__secondary_color
msgid "Secondary Color"
msgstr "次要颜色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#, python-format
msgid "See details"
msgstr "查看详情"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.xml:0
#, python-format
msgid "See examples"
msgstr "查看示例"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/legacy/js/views/view_dialogs.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/select_create_dialog.xml:0
#, python-format
msgid "Select"
msgstr "选择"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login
msgid ""
"Select <i class=\"fa fa-database\" role=\"img\" aria-label=\"Database\" "
"title=\"Database\"/>"
msgstr "选择 <i class=\"fa fa-database\" role=\"img\" aria-label=\"数据库\" title=\"数据库\"/>"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition_selection.xml:0
#, python-format
msgid "Select Default"
msgstr "默认选择"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select Signature Style"
msgstr "选择签名样式"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#, python-format
msgid "Select a model to add a filter."
msgstr "选择模型添加到筛选。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/debug_items.js:0
#, python-format
msgid "Select a view"
msgstr "选择视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "Select all"
msgstr "全选"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Select all records matching the search"
msgstr "选取所有符合搜索条件的记录"

#. module: web
#. openerp-web
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Select field"
msgstr "选择字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Select records"
msgstr "选择记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Selected records"
msgstr "选择的记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/selection/selection_field.js:0
#, python-format
msgid "Selection"
msgstr "选中内容"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Selection:"
msgstr "选择:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/email/email_field.xml:0
#, python-format
msgid "Send Email"
msgstr "发送EMail"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "Set Defaults"
msgstr "设默认"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "Set a Cover Image"
msgstr "设置一张封面图像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a kanban state..."
msgstr "设置一个看板状态..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "Set a priority..."
msgstr "设定一个优先级..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid "Set a timezone on your user"
msgstr "在您的用户设置时区"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/state_selection/state_selection_field.js:0
#, python-format
msgid "Set kanban state..."
msgstr "设置看板状态..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/priority/priority_field.js:0
#, python-format
msgid "Set priority..."
msgstr "设置优先权..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Settings"
msgstr "设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Share with all users"
msgstr "与所有用户共享"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Shortcuts"
msgstr "快捷键"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Show sub-fields"
msgstr "显示子字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/apps.js:0
#, python-format
msgid "Showing locally available modules"
msgstr "显示本地可用模块"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/signature.js:0
#, python-format
msgid "Signature"
msgstr "签字"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Size:"
msgstr "尺寸："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/ajax.js:0
#, python-format
msgid ""
"Something happened while trying to contact the server, check that the server"
" is online and that you still have a working network connection."
msgstr "在试图联系服务器时发生了一些事情，请检查服务器是否线上，并且您的网络连接是否仍然正常。"

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "Something horrible happened"
msgstr "出现糟糕的事情"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Sort graph"
msgstr "排序图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_button/view_button.xml:0
#, python-format
msgid "Special:"
msgstr "特殊："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#: code:addons/web/static/src/views/graph/graph_controller.xml:0
#, python-format
msgid "Stacked"
msgstr "堆叠"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#: code:addons/web/static/src/views/fields/relational_utils.js:0
#, python-format
msgid "Start typing..."
msgstr "开始输入..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/stat_info/stat_info_field.js:0
#, python-format
msgid "Stat Info"
msgstr "统计信息"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/statusbar/statusbar_field.js:0
#, python-format
msgid "Status"
msgstr "状态"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.xml:0
#, python-format
msgid "Stay Here"
msgstr "待在这儿"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_error_dialog/form_error_dialog.xml:0
#, python-format
msgid "Stay here"
msgstr "呆在这里"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/core/ui/block_ui.js:0
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading..."
msgstr "仍加载中..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Still loading...<br />Please be patient."
msgstr "仍在加载...<br />请耐心等待。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#, python-format
msgid "Style"
msgstr "风格"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Styles"
msgstr "样式"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Sun"
msgstr "周日"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.js:0
#, python-format
msgid "Support"
msgstr "支持"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "Syntax error"
msgstr "语法错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "TIP"
msgstr "提示"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#: code:addons/web/static/src/views/fields/many2many_tags/many2many_tags_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Tags"
msgstr "标签"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "Take a minute to get a coffee,"
msgstr "花一分钟时间去拿杯咖啡，"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid "Take a minute to get a coffee,<br />because it's loading..."
msgstr "系统仍在加载<br />请耐心等待。"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__vat
msgid "Tax ID"
msgstr "税ID"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/view_dialogs/export_data_dialog.xml:0
#, python-format
msgid "Template:"
msgstr "模板:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/char/char_field.js:0
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "Text"
msgstr "文本"

#. module: web
#: model:ir.model.fields,help:web.field_base_document_layout__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr "税务识别号。此处的值将根据国家格式进行验证。可以使用“/”表示合作伙伴无需纳税。"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"The content of this cell is too long for an XLSX file (more than %s "
"characters). Please use the CSV format for this export."
msgstr "对于XLSX文件，此单元格的内容太长（超过%s个字符）。请使用此导出的CSV格式。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The field is empty, there's nothing to save."
msgstr "该领域是空的，没有什么可以保存。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid ""
"The operation was interrupted. This usually means that the current operation"
" is taking too much time."
msgstr "操作被中断了。这通常意味着当前的操作花费了太多时间。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/files.js:0
#, python-format
msgid "The selected file (%sB) is over the maximum allowed file size (%sB)."
msgstr "所选文件（%sB）超过允许的最大文件大小（%sB）。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "The selected file exceed the maximum file size of %s."
msgstr "所选文件超出了文件的最大值设定：%s。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid ""
"The type of the field '%s' must be a many2many field with a relation to "
"'ir.attachment' model."
msgstr "字段类型  '%s' 必须是一个关联到 'ir.attachment'模型的 many2many字段。"

#. module: web
#. odoo-python
#: code:addons/web/controllers/export.py:0
#, python-format
msgid ""
"There are too many rows (%s rows, limit: %s) to export as Excel 2007-2013 "
"(.xlsx) format. Consider splitting the export."
msgstr "太多行(%s 行，限制：%s)而未能导出为 Excel 2007-2013 (.xlsx) 格式。考虑拆分导出。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "There is no available image to be set as cover."
msgstr "没有可以用于封面的图像。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#, python-format
msgid "There was a problem while uploading your file"
msgstr "上传您的文件时发生了问题"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/file_handler.js:0
#, python-format
msgid "There was a problem while uploading your file."
msgstr "上传您的文件时出现问题"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.neutralize_banner
msgid "This database is neutralized."
msgstr "此数据库已中和。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/date_picker.js:0
#, python-format
msgid "This date is in the future. Make sure this is what you expect."
msgstr "这是将来的日期。确定是您所期望的。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "This date is on the future. Make sure it is what you expected."
msgstr "这个日期是在将来。确信这是您所期望的。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "This domain is not supported."
msgstr "此域不受支持。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already first"
msgstr "这字段已经是第一个"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "This field is already last"
msgstr "这字段已经是最后一个"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "This file is invalid. Please select an image."
msgstr "错误的文件类型。请选择图像文件。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#, python-format
msgid ""
"This filter is global and will be removed for everybody if you continue."
msgstr "这是一个所有人都可以使用的全球筛选，如果您继续，将会向所有人移除。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_externalreport
msgid "This is a sample of an external report."
msgstr "这是外部报告的一个例子。"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.preview_internalreport
msgid "This is a sample of an internal report."
msgstr "这是内部报告的一个例子。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "This tag is already available"
msgstr "这标签已经是可用的"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "This update will only consider the records of the current page."
msgstr "这个更新将只考虑当前页面的记录。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Thu"
msgstr "周四"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/float_time/float_time_field.js:0
#, python-format
msgid "Time"
msgstr "时间"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/timezone_mismatch/timezone_mismatch_field.js:0
#, python-format
msgid ""
"Timezone Mismatch : This timezone is different from that of your browser.\n"
"Please, set the same timezone as your browser's to avoid time discrepancies in your system."
msgstr ""
"时区不匹配：时区与您的浏览器不同。\n"
"请设置与浏览器相同的时区，以避免系统中的时间差异。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/calendar/calendar_controller.xml:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Today"
msgstr "今天"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/boolean_toggle/boolean_toggle_field.js:0
#, python-format
msgid "Toggle"
msgstr "切换"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#: code:addons/web/static/src/webclient/burger_menu/burger_menu.xml:0
#, python-format
msgid "Toggle menu"
msgstr "切换菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Tomorrow"
msgstr "明天"

#. module: web
#. odoo-python
#: code:addons/web/models/models.py:0
#, python-format
msgid "Too many items to display."
msgstr "有太多的物品需要展示。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Total"
msgstr "合计"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/translation_dialog.js:0
#: code:addons/web/static/src/views/fields/translation_dialog.js:0
#, python-format
msgid "Translate: %s"
msgstr "翻译： %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/field_utils.js:0
#, python-format
msgid "True"
msgstr "真"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/no_content_helpers.xml:0
#, python-format
msgid ""
"Try to add some records, or make sure that there is no\n"
"                    active filter in the search bar."
msgstr ""
"尝试添加一些记录，或者确保搜索栏中没有\n"
"                    搜索栏中没有活动的筛选。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Tue"
msgstr "周二"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/model_field_selector/model_field_selector_popover.xml:0
#, python-format
msgid "Type a default text or press ENTER"
msgstr "键入默认文本或按回车键"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/signature/name_and_signature.xml:0
#: code:addons/web/static/src/legacy/xml/name_and_signature.xml:0
#, python-format
msgid "Type your name to sign"
msgstr "输入您的姓名进行签名"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Type:"
msgstr "类型："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/url/url_field.js:0
#, python-format
msgid "URL"
msgstr "网址"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Unable to find Wkhtmltopdf on this system. The report will be shown in html."
msgstr "在这个系统上找不到Wkhtmltopdf。报告将以HTML显示。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/form/form_status_indicator/form_status_indicator.xml:0
#, python-format
msgid "Unable to save"
msgstr "无法保存"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/form/form_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/views/form/form_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Unarchive"
msgstr "取消归档"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unarchive All"
msgstr "取消归档所有"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught CORS Error"
msgstr "未捕获的CORS错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Javascript Error"
msgstr "未捕获的Javascript错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid "Uncaught Promise"
msgstr "未捕获的承诺"

#. module: web
#. odoo-python
#. odoo-javascript
#: code:addons/web/controllers/export.py:0
#: code:addons/web/static/src/views/calendar/calendar_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#: code:addons/web/static/src/views/graph/graph_model.js:0
#, python-format
msgid "Undefined"
msgstr "未定义的"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "Unfold"
msgstr "展开"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_service.js:0
#, python-format
msgid ""
"Unknown CORS error\n"
"\n"
"An unknown CORS error occured.\n"
"The error probably originates from a JavaScript file served from a different origin.\n"
"(Opening your browser console might give you a hint on the error.)"
msgstr ""
"未知的CORS错误\n"
"\n"
"发生了一个未知的 CORS 错误。\n"
"这个错误可能源于一个从不同来源提供的JavaScript文件。\n"
"(打开您的浏览器控制台可能给您一个错误的提示）。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/py_utils.js:0
#, python-format
msgid "Unknown nonliteral type "
msgstr "未知的非字面类型 "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/list/list_editable_renderer.js:0
#, python-format
msgid "Unlink row "
msgstr "取消关联 "

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/_deprecated/data.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#: code:addons/web/static/src/search/control_panel/control_panel.xml:0
#, python-format
msgid "Unnamed"
msgstr "未命名的"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_confirmation_dialog.js:0
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_view.xml:0
#, python-format
msgid "Unsaved changes"
msgstr "未保存的更改"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/graph/graph_view.js:0
#: code:addons/web/static/src/views/pivot/pivot_view.js:0
#, python-format
msgid "Untitled"
msgstr "未命名的"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "Update to:"
msgstr "更新到:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade now"
msgstr "马上升级"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to enterprise"
msgstr "升级至企业版"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/fields/upgrade_dialog.xml:0
#, python-format
msgid "Upgrade to future versions"
msgstr "升级到未来版本"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#: code:addons/web/static/src/views/kanban/kanban_cover_image_dialog.xml:0
#, python-format
msgid "Upload and Set"
msgstr "上传并设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/binary/binary_field.xml:0
#: code:addons/web/static/src/views/fields/pdf_viewer/pdf_viewer_field.xml:0
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
#, python-format
msgid "Upload your file"
msgstr "上传您的文件"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploaded"
msgstr "已上传"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.xml:0
#, python-format
msgid "Uploading"
msgstr "上传中"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/relational_fields.js:0
#, python-format
msgid "Uploading Error"
msgstr "上传错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/many2many_binary/many2many_binary_field.js:0
#, python-format
msgid "Uploading error"
msgstr "上传错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/file_handler.xml:0
#, python-format
msgid "Uploading..."
msgstr "正在上传..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/file_upload/file_upload_progress_record.js:0
#, python-format
msgid "Uploading... (%s%)"
msgstr "上传... (%s%)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column_quick_create.js:0
#: code:addons/web/static/src/views/kanban/kanban_column_quick_create.js:0
#, python-format
msgid "Use This For My Kanban"
msgstr "应用到当前我的看板界面"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/favorite_menu/custom_favorite_item.xml:0
#, python-format
msgid "Use by default"
msgstr "默认使用"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu.xml:0
#, python-format
msgid "User"
msgstr "用户"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "User Error"
msgstr "用户错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#, python-format
msgid "Validation Error"
msgstr "验证错误"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "Values"
msgstr "值"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_renderer.js:0
#: code:addons/web/static/src/views/form/form_label.js:0
#, python-format
msgid "Values set here are company-specific."
msgstr "Values set here are company-specific."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Variation"
msgstr "变形"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View %s"
msgstr "视图 %s"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Access Rights"
msgstr "查看访问权限"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Fields"
msgstr "查看字段"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "View In Kanban"
msgstr "看板中的视图"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/legacy/debug_manager.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#: code:addons/web/static/src/views/debug_items.js:0
#, python-format
msgid "View Metadata"
msgstr "查看元数据"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/debug_items.js:0
#, python-format
msgid "View Record Rules"
msgstr "查看记录规则"

#. module: web
#: model:ir.model.fields,field_description:web.field_ir_actions_act_window_view__view_mode
msgid "View Type"
msgstr "视图类型"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "View switcher"
msgstr "视图切换器"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.js:0
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_controller.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/control_panel.xml:0
#: code:addons/web/static/src/search/favorite_menu/favorite_menu.js:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#: code:addons/web/static/src/views/list/list_controller.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.benchmark_suite
msgid "Web Benchmarks"
msgstr "网络基准测试"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_mobile_suite
msgid "Web Mobile Tests"
msgstr "网络移动端测试"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.qunit_suite
msgid "Web Tests"
msgstr "网络测试"

#. module: web
#: model:ir.model.fields,field_description:web.field_base_document_layout__website
msgid "Website Link"
msgstr "网站链接"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/widgets/week_days/week_days.js:0
#, python-format
msgid "Wed"
msgstr "周三"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_renderer.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Week"
msgstr "周"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/effects/effect_service.js:0
#, python-format
msgid "Well Done!"
msgstr "干的漂亮 !"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#, python-format
msgid "Widget:"
msgstr "小部件:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/settings_form_controller.js:0
#, python-format
msgid "Would you like to save your changes?"
msgstr "您是否要保存更改?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/special_fields.js:0
#: code:addons/web/static/src/views/fields/iframe_wrapper/iframe_wrapper_field.js:0
#, python-format
msgid "Wrap raw html within an iframe"
msgstr "在一个iframe中包裹原始html"

#. module: web
#. odoo-python
#: code:addons/web/controllers/home.py:0
#, python-format
msgid "Wrong login/password"
msgstr "错误的登录名/密码"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/debug_menu_items.xml:0
#, python-format
msgid "XML ID:"
msgstr "XML ID："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/utils/dates.js:0
#: code:addons/web/static/src/views/calendar/calendar_controller.js:0
#, python-format
msgid "Year"
msgstr "年"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/colorlist/colorlist.js:0
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "Yellow"
msgstr "黄色"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_bar.js:0
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_column.js:0
#: code:addons/web/static/src/legacy/js/views/list/list_renderer.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#: code:addons/web/static/src/views/fields/field_tooltip.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.js:0
#: code:addons/web/static/src/views/list/list_renderer.js:0
#: code:addons/web/static/src/views/pivot/pivot_model.js:0
#, python-format
msgid "Yes"
msgstr "是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/fields/basic_fields.js:0
#: code:addons/web/static/src/views/fields/remaining_days/remaining_days_field.js:0
#, python-format
msgid "Yesterday"
msgstr "昨天"

#. module: web
#: model_terms:ir.ui.view,arch_db:web.login_successful
msgid "You are logged in."
msgstr "您已登录"

#. module: web
#. odoo-python
#: code:addons/web/controllers/binary.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "您无权在此处上传附件。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/widgets/model_field_selector_popover.js:0
#, python-format
msgid "You cannot follow relations for this field chain construction"
msgstr "您无法跟踪此字段链结构的关系"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_definition.js:0
#, python-format
msgid "You do not have access to the model \"%s\"."
msgstr "您无权访问模型\"%s\""

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "You may not believe it,"
msgstr "您可能不相信，"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/core/misc.js:0
#, python-format
msgid ""
"You may not believe it,<br />but the application is actually loading..."
msgstr "请耐心等待，<br />应用仍在加载..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/property_tags.js:0
#, python-format
msgid "You need to be able to edit parent first to add property tags"
msgstr "您需要先编辑母级才能添加属性标签。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#: code:addons/web/static/src/views/fields/properties/properties_field.js:0
#, python-format
msgid "You need to be able to edit parent first to configure property fields"
msgstr "您需要先编辑母级才能配置属性字段。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/basic/basic_controller.js:0
#: code:addons/web/static/src/views/fields/translation_button.js:0
#, python-format
msgid ""
"You need to save this new record before editing the translation. Do you want"
" to proceed?"
msgstr "您需要在编辑翻译之前保存此新记录，您要继续吗？"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You need to start Odoo with at least two workers to print a pdf version of "
"the reports."
msgstr "您需要至少使用两个工人来启动Odoo以打印报告的pdf版本。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"You should upgrade your version of Wkhtmltopdf to at least 0.12.0 in order "
"to get a correct display of headers and footers as well as support for "
"table-breaking between pages."
msgstr "您应该将Wkhtmltopdf的版本至少升级到0.12.0，以便正确显示页眉和页脚，以及支持页之间的表分隔。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/errors/error_dialogs.xml:0
#: code:addons/web/static/src/public/error_notifications.js:0
#, python-format
msgid "Your Odoo session expired. The current page is about to be refreshed."
msgstr "您的Odoo会话已过期。当前页面即将被刷新。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/actions/action_service.js:0
#, python-format
msgid ""
"Your installation of Wkhtmltopdf seems to be broken. The report will be "
"shown in html."
msgstr "您安装的Wkhtmltopdf 似乎坏了。报告将以HTML格式显示。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/views/kanban/kanban_record.js:0
#, python-format
msgid "[No widget %s]"
msgstr "[没有挂件 %s]"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "a day ago"
msgstr "一天前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a minute ago"
msgstr "大约一分钟前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a month ago"
msgstr "大约一月前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about a year ago"
msgstr "大约一年前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "about an hour ago"
msgstr "大约一小时前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "all"
msgstr "所有"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "all records"
msgstr "所有记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "and"
msgstr "and"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "any"
msgstr "任意"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "are valid for this update."
msgstr "适用于此更新。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "as a new"
msgstr "另存为"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "at:"
msgstr "在："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "because it's loading..."
msgstr "因为它正在加载中..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "but the application is actually loading..."
msgstr "但应用程序实际上正在加载中..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "child of"
msgstr "子类"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "contains"
msgstr "包含"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/search/search_bar/search_bar.js:0
#, python-format
msgid "date"
msgstr "日期"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "does not contain"
msgstr "不含"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "doesn't contain"
msgstr "不包含"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/network/download.js:0
#, python-format
msgid "downloading..."
msgstr "下载中..."

#. module: web
#: model_terms:ir.ui.view,arch_db:web.view_base_document_layout
msgid "e.g. Global Business Solutions"
msgstr "例如：全球商业解决方案"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "for:"
msgstr "为："

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than"
msgstr "大于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "greater than or equal to"
msgstr "大于或等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/colorpicker.xml:0
#, python-format
msgid "hex"
msgstr "十六进制"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hour"
msgstr "小时"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "hours"
msgstr "小时"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "in"
msgstr "在"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is"
msgstr "是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is No"
msgstr "不是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is Yes"
msgstr "是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after"
msgstr "在之后"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is after or equal to"
msgstr "迟于或等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before"
msgstr "在之前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is before or equal to"
msgstr "早于或等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is between"
msgstr "介于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is equal to"
msgstr "等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not"
msgstr "不是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "is not ="
msgstr "is not ="

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not equal to"
msgstr "不等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is not set"
msgstr "未设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "is set"
msgstr "已设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/ui/block_ui.js:0
#, python-format
msgid "it's still loading..."
msgstr "仍在加载中..."

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/utils/numbers.js:0
#: code:addons/web/static/src/legacy/js/core/utils.js:0
#, python-format
msgid "kMGTPE"
msgstr "kMGTPE"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than"
msgstr "少于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/l10n/translation.js:0
#: code:addons/web/static/src/legacy/js/core/translation.js:0
#, python-format
msgid "less than a minute ago"
msgstr "少于一分钟前"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/legacy/js/control_panel/search_utils.js:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.js:0
#, python-format
msgid "less than or equal to"
msgstr "少于或等于"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "like"
msgstr "像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/menus/menu_providers.js:0
#, python-format
msgid "menus"
msgstr "菜单"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minute"
msgstr "分钟"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/views/calendar/calendar_common/calendar_common_popover.js:0
#, python-format
msgid "minutes"
msgstr "分钟"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/settings_form_view/widgets/res_config_invite_users.xml:0
#, python-format
msgid "more"
msgstr "更多"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "ms"
msgstr "秒"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_palette.xml:0
#, python-format
msgid "new tab"
msgstr "新选项卡"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/default_providers.js:0
#, python-format
msgid "no description provided"
msgstr "未提供说明"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_operator.xml:0
#, python-format
msgid "none"
msgstr "无"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "not"
msgstr "不是"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not in"
msgstr "不在"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#, python-format
msgid "not like"
msgstr "不像"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "not set (false)"
msgstr "未设置 (否)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_root_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of the following rules:"
msgstr "以下规则:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_branch_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "of:"
msgstr "的:"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "on any screen to show shortcut overlays and"
msgstr "在任何屏幕上显示快捷方式叠加和"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/js/views/action_model.js:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/filter_menu/custom_filter_item.xml:0
#: code:addons/web/static/src/search/search_model.js:0
#, python-format
msgid "or"
msgstr "或"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_operators.js:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "parent of"
msgstr "父类"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#: code:addons/web/static/src/core/debug/profiling/profiling_qweb.xml:0
#, python-format
msgid "query"
msgstr "询问"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/fields/domain/domain_field.xml:0
#: code:addons/web/static/src/views/fields/properties/property_definition.xml:0
#, python-format
msgid "record(s)"
msgstr "记录"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "records ?"
msgstr "记录?"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/kanban.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#: code:addons/web/static/src/views/kanban/kanban_renderer.xml:0
#, python-format
msgid "remaining)"
msgstr "剩余)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/search/search_bar/search_bar.xml:0
#, python-format
msgid "search"
msgstr "搜索"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#: code:addons/web/static/src/views/list/list_controller.xml:0
#, python-format
msgid "selected"
msgstr "已选取"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#: code:addons/web/static/src/views/list/list_confirmation_dialog.xml:0
#, python-format
msgid "selected records,"
msgstr "所选记录,"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/domain_selector_leaf_node.xml:0
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "set"
msgstr "设置"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/domain_selector/fields/domain_selector_boolean_field.xml:0
#: code:addons/web/static/src/legacy/js/widgets/domain_selector.js:0
#, python-format
msgid "set (true)"
msgstr "设置(真)"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "to trigger a shortcut."
msgstr "以触发快捷方式。"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/legacy/xml/base.xml:0
#, python-format
msgid "type a default text or press ENTER"
msgstr "键入默认文本或按 ENTER 键"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/webclient/user_menu/user_menu_items.xml:0
#, python-format
msgid "— press"
msgstr "按下"

#. module: web
#. odoo-javascript
#: code:addons/web/static/src/core/commands/command_items.xml:0
#, python-format
msgid "— search for"
msgstr "— 搜寻："
