# -*- coding: utf-8 -*-
from datetime import date, datetime, timedelta
from odoo import models, fields, api
from odoo.tools.translate import _
from odoo.exceptions import UserError
from odoo.tools import DEFAULT_SERVER_DATE_FORMAT
from odoo.modules.module import get_resource_path
from odoo.exceptions import ValidationError
from odoo.addons.report_xlsx.models.xlsx_utils import XlsxUtils


class ReportAccount18(models.TransientModel):
    _name = 'report.account.18'
    _inherit = 'wizard.preview.xlsx'
    _description = 'Bảng cân đối phát sinh công nợ'

    def _get_account_ids(self):
        debit_account_ids = self.env['account.account'].search([('company_id', '=', self.env.company.id), ('debit_account', '=', True)])
        if debit_account_ids:
            list_debit_acc = [0]
            for e in debit_account_ids:
                list_debit_acc.append(e.id)
            sql_account = f'''
            with recursive account as (
            -- initial anchor
                (select id, code, name, parent_id from account_account where id in {tuple(list_debit_acc)})
                union distinct
            --  recursive member 
                (select sub_account.id, sub_account.code, sub_account.name, sub_account.parent_id from account_account sub_account
                 inner join account on sub_account.parent_id = account.id)
            )
            select id, code, name, parent_id from account
            '''
            self._cr.execute(sql_account)
            accounts = self._cr.dictfetchall()
            list_account = []
            for account in accounts:
                list_account.append(account.get('id'))
                list_account.append(account.get('parent_id'))
            debit_account_ids = self.env['account.account'].browse(list_account)

        if not debit_account_ids:
            raise ValidationError('Chưa cấu hình tài khoản công nợ!')
        return debit_account_ids.ids

    name = fields.Char(default=_('Bảng cân đối phát sinh công nợ'), readonly=1)
    report_name = fields.Char()
    preview_xml = fields.Char(readonly=1)
    date_from = fields.Date('Từ ngày',  required=True, default=date.today().replace(day=1))
    date_to = fields.Date('Đến ngày',  required=True, default=date.today())
    account_ids = fields.Many2many('account.account', default=_get_account_ids, help='Lọc ra tất cả các tài khoản công nợ')
    account_id = fields.Many2one('account.account', string='Tài khoản',)
    partner_id = fields.Many2one('res.partner', string='Đối tượng')

    def format_where_list(self, list_param):
        if len(list_param) == 1:
            list_param.append(0)
        return tuple(list_param)

    @api.constrains('date_from', 'date_to')
    def check_from_date_to_date(self):
        if self.date_from and self.date_to and self.date_from > self.date_to:
            raise ValidationError('Khoảng thời gian lên báo cáo không đúng. Xin hãy nhập lại')

    def format_date(self, date):
        date = '{}/{}/{}'.format(date.day, date.month, date.year)
        return date

    def get_data(self):
        params = [self.date_from, self.date_from, self.date_from, self.date_to, self.date_from, self.date_to,
                  self.date_to, self.date_to]
        where_clause = "am.state = 'posted' and aa.id in %s"
        params.append(self.format_where_list(self.account_ids.ids))
        if self.account_id:
            accounts = self.account_id.get_leaf_child_ids()
            where_clause += ''' and aa.id in %s'''
            params.append(tuple(accounts.ids + [0, 0],))
        if self.partner_id:
            where_clause += ''' and rp.id = %s'''
            params.append(self.partner_id.id)

        query = '''with t as (
                        SELECT dt.welly_code, 
                                dt.partner_id,
                                dt.partner_name,
                                dt.code,
                                case when SUM(o_debit) - sum(o_credit) >= 0 then SUM(o_debit) - sum(o_credit) else 0 end AS open_debit,
                                case when SUM(o_debit) - sum(o_credit) < 0 then abs(SUM(o_debit) - sum(o_credit)) else 0 end AS open_credit,
                                sum(dt.debit) as debit,
                                sum(dt.credit)as credit,
                                case when SUM(c_debit) - sum(c_credit) >= 0 then SUM(c_debit) - sum(c_credit) else 0 end AS close_debit,
                                case when SUM(c_debit) - sum(c_credit) < 0 then abs(SUM(c_debit) - sum(c_credit)) else 0 end AS close_credit
                        FROM
                            (SELECT aml.partner_id, 
                                rp.welly_code, 
                                rp.name as partner_name, 
                                aa.code, 
                                aa.name,
                                case when aml.date < %s then aml.debit else 0 end AS o_debit,
                                case when aml.date < %s then aml.credit else 0 end AS o_credit,
                                case when aml.date >= %s and aml.date <= %s then aml.debit else 0 end AS debit,
                                case when aml.date >= %s and aml.date <= %s then aml.credit else 0 end AS credit,
                                case when aml.date <= %s then aml.debit else 0 end AS c_debit,
                                case when aml.date <= %s then aml.credit else 0 end AS c_credit
                            FROM account_move_line aml
                            INNER JOIN account_account as aa ON aml.account_id = aa.id
                            LEFT JOIN account_move as am on am.id = aml.move_id
                            LEFT JOIN res_partner as rp on rp.id = aml.partner_id
                            WHERE {}) dt
                        GROUP BY dt.partner_id, dt.welly_code, dt.partner_name, dt.code, dt.name
                    )
                    SELECT t.welly_code,
                            t.partner_id,
                            t.partner_name,
                            t.code,
                            t.open_debit,
                            t.open_credit,
                            t.debit,
                            t.credit,
                            t.close_debit,
                            t.close_credit
                    FROM t
                    WHERE (t.open_debit + t.open_credit + t.debit + t.credit + t.close_debit + t.close_credit) <> 0
                    ORDER BY t.partner_name
                '''.format(where_clause)
        self._cr.execute(query, params)
        data = self.env.cr.dictfetchall()
        return data

    def generate_xlsx(self, wb, data):
        self.ensure_one()
        company = self.env.user.company_id
        ws = wb.add_worksheet(u'Báo cáo')
        utils = XlsxUtils(get_resource_path('w_account_report', 'templates', 'report_account_18.xls'), wb)
        utils.load_range_format(0, (0, 0, 9, 9))
        utils.load_range_format(0, (10, 0, 10, 9))
        utils.load_range_format(0, (20, 0, 20, 9))
        utils.load_range_format(0, (21, 0, 24, 9))
        # # Copy header range
        utils.copy_range(0, ws, (0, 0, 9, 9), 0)

        # Parameters section
        utils.write(ws, (0, 0), '%s' % company.name)
        utils.write(ws, (1, 0), '%s' % company.street or '')
        utils.write(ws, (4, 0), 'Từ ngày %s đến ngày %s' % (
            self.date_from.strftime('%d-%m-%Y'),
            self.date_to.strftime('%d-%m-%Y'),
        ))

        account_str = ''
        if self.account_id:
            account_str = 'TÀI KHOẢN: %s' % self.account_id.code
        utils.write(ws, (5, 0), account_str)

        partner_str = ''
        if self.partner_id:
            partner_str = 'ĐỐI TƯỢNG: %s' % self.partner_id.name.upper()
        utils.write(ws, (6, 0), partner_str)

        datas = self.get_data()

        total_open_debit = 0.0
        total_open_credit = 0.0
        total_debit = 0.0
        total_credit = 0.0
        total_close_debit = 0.0
        total_close_credit = 0.0
        row_post = 10
        stt = 1
        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        for data in datas:
            open_debit = data.get('open_debit', 0)
            open_credit = data.get('open_credit', 0)
            debit = data.get('debit', 0)
            credit = data.get('credit', 0)
            close_debit = data.get('close_debit', 0)
            close_credit = data.get('close_credit', 0)
            utils.write(ws, (row_post, 0), stt, _origin_cell_axis=(10, 0))
            account_code = data.get('code', False)
            account_id = self.env['account.account'].search([('code', '=', account_code)], limit=1)

            utils.write(ws, (row_post, 1), '{}'.format(data.get('welly_code', False) or ''), _origin_cell_axis=(10, 1))
            utils.write(ws, (row_post, 2), (data.get('partner_name', False) or '').upper(), _origin_cell_axis=(10, 2))
            utils.write(ws, (row_post, 3), account_code or '', _origin_cell_axis=(10, 3))
            # open
            utils.write(ws, (row_post, 4), open_debit or '', _origin_cell_axis=(10, 4))
            utils.write(ws, (row_post, 5), open_credit or '', _origin_cell_axis=(10, 5))
            # cycle
            utils.write(ws, (row_post, 6), debit or '', _origin_cell_axis=(10, 6))
            utils.write(ws, (row_post, 7), credit or '', _origin_cell_axis=(10, 7))

            #close
            utils.write(ws, (row_post, 8), close_debit or '', _origin_cell_axis=(10, 8))
            utils.write(ws, (row_post, 9), close_credit or '', _origin_cell_axis=(10, 9))

            row_post += 1
            stt += 1
            total_open_debit += open_debit
            total_open_credit += open_credit
            total_debit += debit
            total_credit += credit
            total_close_debit += close_debit
            total_close_credit += close_credit

        # total
        utils.copy_range(0, ws, (20, 0, 20, 9), row_post)
        utils.write(ws, (row_post, 4), total_open_debit or 0.0, _origin_cell_axis=(20, 4))
        utils.write(ws, (row_post, 5), total_open_credit or 0.0, _origin_cell_axis=(20, 5))
        utils.write(ws, (row_post, 6), total_debit or 0.0, _origin_cell_axis=(20, 6))
        utils.write(ws, (row_post, 7), total_credit or 0.0, _origin_cell_axis=(20, 7))
        utils.write(ws, (row_post, 8), total_close_debit or 0.0, _origin_cell_axis=(20, 8))
        utils.write(ws, (row_post, 9), total_close_credit or 0.0, _origin_cell_axis=(20, 9))

        # signed
        row_post += 1
        utils.copy_range(0, ws, (21, 0, 24, 9), row_post)

    def button_preview(self):
        path = self.preview_xlsx_report('w_account_report.report_account_18', self.id)
        result_html = self.convert_xlsx_html(path)
        self.preview_xml = self.convert_xlsx_html_sticky_header(result_html, 9, space_first_row=28) if path else None

    def button_download(self):
        self.button_preview()
        self.report_name = _('Bảng cân đối phát sinh công nợ')
        return self.env.ref('w_account_report.report_report_account_18_xlsx').report_action(self)


class ReportAccount18Xlsx(models.AbstractModel):
    _name = 'report.w_account_report.report_account_18'
    _inherit = 'report.report_xlsx.abstract'

    def generate_xlsx_report(self, workbook, data, objects):
        if self._context.get('preview_data'):
            objects.generate_xlsx(workbook, data)
        else:
            objects.with_context({'export_excel': True}).generate_xlsx(workbook, data)
