<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
    <!-- Multi - Company Rules -->
    <record id="rule_closing_entry_rule_company" model="ir.rule">
        <field name="name"><PERSON><PERSON> tắc kết chuyển: đa công ty</field>
        <field name="model_id" ref="model_closing_entry_rule"/>
        <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>

        <field name="perm_read" eval="1"/>
        <field name="perm_write" eval="1"/>
        <field name="perm_create" eval="1"/>
        <field name="perm_unlink" eval="1"/>
    </record>
</data>
</odoo>
