id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink

access_closing_entry_rule_group_account_synthesys,access_closing_entry_rule,model_closing_entry_rule,w_account_role.group_account_synthesys,1,0,0,0
access_closing_entry_group_account_synthesys,access_closing_entry_,model_closing_entry,w_account_role.group_account_synthesys,1,0,0,0

access_closing_entry_rule_group_account_sale,access_closing_entry_rule,model_closing_entry_rule,w_account_role.group_account_sale,1,1,1,1
access_closing_entry_group_account_sale,access_closing_entry_,model_closing_entry,w_account_role.group_account_sale,1,1,1,1

access_closing_entry_rule_group_account_management,access_closing_entry_rule,model_closing_entry_rule,w_account_role.group_account_management,1,1,1,1
access_closing_entry_group_account_management,access_closing_entry_,model_closing_entry,w_account_role.group_account_management,1,1,1,1

access_closing_entry_rule_group_coo,access_closing_entry_rule,model_closing_entry_rule,welly_base.group_coo,1,1,1,1
access_closing_entry_group_coo,access_closing_entry_,model_closing_entry,welly_base.group_coo,1,1,1,1

access_closing_entry_rule_group_system,access_closing_entry_rule,model_closing_entry_rule,base.group_system,1,1,1,1
access_closing_entry_group_system,access_closing_entry_,model_closing_entry,base.group_system,1,1,1,1