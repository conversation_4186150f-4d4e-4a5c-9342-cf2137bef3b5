# -*- coding: utf-8 -*-
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class AssetModify(models.TransientModel):
    _inherit = 'asset.modify'
    _description = 'Điều chỉnh Tài sản'

    @api.onchange('gain_value')
    def stop_gain_value(self):
        for r in self:
            if not r.gain_value:
                continue
            raise UserError('Việc điều chỉnh tăng giá trị khấu hao hiện đang bị chặn. Bạn chỉ có thể giảm giá trị khấu hao !')