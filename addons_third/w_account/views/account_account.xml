<odoo>
    <record id="view_account_form" model="ir.ui.view">
        <field name="name">account.account.form</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_form"/>
        <field name="arch" type="xml">
            <field name="tax_ids" position="before">
<!--                <field name="short_name"/>-->
                <field name="debit_account"/>
<!--                <field name="two_side"/>-->
                <field name="parent_id" context="{'show_parent_account': True}"/>
            </field>
            <field name="tax_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="tag_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="allowed_journal_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="currency_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="deprecated" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="group_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="account_type" position="attributes">
                <attribute name="widget"></attribute>
            </field>
        </field>
    </record>

    <record id="view_account_list" model="ir.ui.view">
        <field name="name">account.account.list</field>
        <field name="model">account.account</field>
        <field name="inherit_id" ref="account.view_account_list"/>
        <field name="arch" type="xml">
            <field name="account_type" position="attributes">
                <attribute name="widget"></attribute>
            </field>
            <field name="currency_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <xpath expr="//field[@name='account_type']" position="after">
                <field name="debit_account" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='name']" position="after">
                <field name="parent_id" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="account.action_account_form" model="ir.actions.act_window">
        <field name="context">{'show_parent_account': True}</field>
    </record>
</odoo>