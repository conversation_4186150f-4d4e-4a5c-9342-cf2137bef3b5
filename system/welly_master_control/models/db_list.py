import odoo
from odoo.service import db
from odoo.http import request
from odoo import http
from contextlib import closing
import logging
from functools import wraps
from decorator import decorator
from inspect import unwrap

_logger = logging.getLogger(__name__)

original_list_dbs = db.list_dbs
original_db_filter = http.db_filter

def check_db_version(dbname):
    """
    Check if database has valid Odoo version by checking base module
    Returns True if version exists, False otherwise
    """
    try:
        db_conn = odoo.sql_db.db_connect(dbname)
        with closing(db_conn.cursor()) as cr:
            # Ki<PERSON>m tra xem bảng ir_module_module có tồn tại không
            cr.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ir_module_module'
            )
            """)
            table_exists = cr.fetchone()[0]
            
            if not table_exists:
                return False
            
            cr.execute("""
            SELECT latest_version FROM ir_module_module 
            WHERE name = 'base' LIMIT 1
            """)
            result = cr.fetchone()
            return bool(result and result[0])
    except Exception:
        return False

def custom_list_dbs(force=False):
    """
    Custom implementation of list_dbs that allows showing all databases
    when accessed through system domain
    """
    if hasattr(request, 'httprequest') and request.httprequest.host.startswith('system.'):
        # For system domain, bypass the dbfilter and show all databases
        db_conn = odoo.sql_db.db_connect('postgres')
        with closing(db_conn.cursor()) as cr:
            try:
                chosen_template = odoo.tools.config['db_template']
                templates_list = tuple(set(['postgres', chosen_template]))
                cr.execute(
                    "select datname from pg_database "
                    "where datdba=(select usesysid from pg_user where usename=current_user) "
                    "and not datistemplate and datallowconn and datname not in %s "
                    "order by datname", 
                    (templates_list,)
                )
                # Filter databases based on version check
                all_dbs = [odoo.tools.ustr(name) for (name,) in cr.fetchall()]
                return [db_name for db_name in all_dbs if check_db_version(db_name)]
            except Exception:
                _logger.exception('Listing databases failed in custom_list_dbs:')
                return []
    
    # For all other cases, use the original implementation
    all_dbs = original_list_dbs(force)
    return [db_name for db_name in all_dbs if check_db_version(db_name)]

def custom_db_filter(dbs, host=None):
    """
    Custom implementation of db_filter that allows showing all databases
    when accessed through system domain
    """
    if hasattr(request, 'httprequest') and request.httprequest.host.startswith('system.'):
        return ['system']
    return original_db_filter(dbs, host)

# Apply the monkey patch
db.list_dbs = custom_list_dbs
http.db_filter = custom_db_filter


# patch db

original_check_db_management_enabled = db.check_db_management_enabled

def custom_check_db_management_enabled(method):
    def if_db_mgt_enabled(method, self, *args, **kwargs):
        if request.httprequest.host.startswith('system.'):
            return method(self, *args, **kwargs)
        return original_check_db_management_enabled(method)(self, *args, **kwargs)
    return decorator(if_db_mgt_enabled, method)

db.check_db_management_enabled = custom_check_db_management_enabled


# unwrap

# patch exp_dump
original_exp_dump = unwrap(db.exp_dump)

@db.check_db_management_enabled
def custom_exp_dump(db_name, format):
    return original_exp_dump(db_name, format)

db.exp_dump = custom_exp_dump


# patch exp_create_database
original_exp_create_database = unwrap(db.exp_create_database)

@db.check_db_management_enabled
def custom_exp_create_database(db_name, demo, lang, user_password='admin', login='admin', country_code=None, phone=None):
    return original_exp_create_database(db_name, demo, lang, user_password, login, country_code, phone)

db.exp_create_database = custom_exp_create_database

# patch exp_duplicate_database
original_exp_duplicate_database = unwrap(db.exp_duplicate_database)

@db.check_db_management_enabled
def custom_exp_duplicate_database(db_original_name, db_name, neutralize_database=False):
    return original_exp_duplicate_database(db_original_name, db_name, neutralize_database)

db.exp_duplicate_database = custom_exp_duplicate_database

# patch exp_drop
original_exp_drop = unwrap(db.exp_drop)

@db.check_db_management_enabled
def custom_exp_drop(db_name):
    return original_exp_drop(db_name)

db.exp_drop = custom_exp_drop
